/**
 * 第5批次节点修复后测试
 * 验证所有节点类型是否正确注册
 */
import { NodeRegistry } from '../NodeRegistry';
import { registerRenderingNodes } from '../RenderingNodes';

describe('第5批次节点修复后测试', () => {
  let registry: NodeRegistry;

  beforeEach(() => {
    registry = new NodeRegistry();
    registerRenderingNodes(registry);
  });

  describe('节点注册验证', () => {
    test('应该成功注册所有相机控制节点', () => {
      const cameraTargetNode = registry.getNodeType('rendering/camera/setCameraTarget');
      const cameraFOVNode = registry.getNodeType('rendering/camera/setCameraFOV');
      
      expect(cameraTargetNode).toBeDefined();
      expect(cameraFOVNode).toBeDefined();
      expect(cameraTargetNode?.label).toBe('设置相机目标');
      expect(cameraFOVNode?.label).toBe('设置相机视野');
    });

    test('应该成功注册所有光照系统节点', () => {
      const lightNodes = [
        'rendering/light/createDirectionalLight',
        'rendering/light/createPointLight',
        'rendering/light/createSpotLight',
        'rendering/light/createAmbientLight',
        'rendering/light/setLightColor',
        'rendering/light/setLightIntensity'
      ];

      lightNodes.forEach(nodeType => {
        const node = registry.getNodeType(nodeType);
        expect(node).toBeDefined();
        expect(node?.constructor).toBeDefined();
      });
    });

    test('应该成功注册所有阴影系统节点', () => {
      const shadowNodes = [
        'rendering/shadow/enableShadows',
        'rendering/shadow/setShadowMapSize'
      ];

      shadowNodes.forEach(nodeType => {
        const node = registry.getNodeType(nodeType);
        expect(node).toBeDefined();
        expect(node?.constructor).toBeDefined();
      });
    });

    test('应该成功注册所有材质系统节点', () => {
      const materialNodes = [
        'rendering/material/createBasicMaterial',
        'rendering/material/createStandardMaterial',
        'rendering/material/createPhysicalMaterial',
        'rendering/material/setMaterialColor',
        'rendering/material/setMaterialTexture',
        'rendering/material/setMaterialOpacity'
      ];

      materialNodes.forEach(nodeType => {
        const node = registry.getNodeType(nodeType);
        expect(node).toBeDefined();
        expect(node?.constructor).toBeDefined();
      });
    });

    test('应该成功注册所有后处理节点', () => {
      const postProcessNodes = [
        'rendering/postprocess/enableFXAA',
        'rendering/postprocess/enableSSAO',
        'rendering/postprocess/enableBloom'
      ];

      postProcessNodes.forEach(nodeType => {
        const node = registry.getNodeType(nodeType);
        expect(node).toBeDefined();
        expect(node?.constructor).toBeDefined();
      });
    });

    test('应该成功注册LOD系统节点', () => {
      const lodNode = registry.getNodeType('rendering/lod/setLODLevel');
      expect(lodNode).toBeDefined();
      expect(lodNode?.label).toBe('设置LOD级别');
    });

    test('应该成功注册所有物理刚体节点', () => {
      const physicsNodes = [
        'physics/rigidbody/createRigidBody',
        'physics/rigidbody/setMass',
        'physics/rigidbody/setFriction',
        'physics/rigidbody/setRestitution'
      ];

      physicsNodes.forEach(nodeType => {
        const node = registry.getNodeType(nodeType);
        expect(node).toBeDefined();
        expect(node?.constructor).toBeDefined();
      });
    });

    test('所有节点都应该有正确的属性', () => {
      const allNodeTypes = registry.getAllNodeTypes();
      
      allNodeTypes.forEach(nodeType => {
        expect(nodeType.type).toBeDefined();
        expect(nodeType.label).toBeDefined();
        expect(nodeType.description).toBeDefined();
        expect(nodeType.constructor).toBeDefined();
        expect(nodeType.category).toBeDefined();
      });
    });

    test('应该注册预期数量的新节点', () => {
      const allNodeTypes = registry.getAllNodeTypes();
      
      // 计算第5批次的节点数量
      const batch5NodeTypes = allNodeTypes.filter(nodeType => 
        nodeType.type.includes('setCameraTarget') ||
        nodeType.type.includes('setCameraFOV') ||
        nodeType.type.includes('createDirectionalLight') ||
        nodeType.type.includes('createPointLight') ||
        nodeType.type.includes('createSpotLight') ||
        nodeType.type.includes('createAmbientLight') ||
        nodeType.type.includes('setLightColor') ||
        nodeType.type.includes('setLightIntensity') ||
        nodeType.type.includes('enableShadows') ||
        nodeType.type.includes('setShadowMapSize') ||
        nodeType.type.includes('createBasicMaterial') ||
        nodeType.type.includes('createStandardMaterial') ||
        nodeType.type.includes('createPhysicalMaterial') ||
        nodeType.type.includes('setMaterialColor') ||
        nodeType.type.includes('setMaterialTexture') ||
        nodeType.type.includes('setMaterialOpacity') ||
        nodeType.type.includes('enableFXAA') ||
        nodeType.type.includes('enableSSAO') ||
        nodeType.type.includes('enableBloom') ||
        nodeType.type.includes('setLODLevel') ||
        nodeType.type.includes('createRigidBody') ||
        nodeType.type.includes('setMass') ||
        nodeType.type.includes('setFriction') ||
        nodeType.type.includes('setRestitution')
      );

      // 应该有24个新节点
      expect(batch5NodeTypes.length).toBeGreaterThanOrEqual(24);
    });
  });

  describe('节点分类验证', () => {
    test('渲染节点应该属于正确的类别', () => {
      const renderingNodes = registry.getAllNodeTypes().filter(
        nodeType => nodeType.type.startsWith('rendering/')
      );

      renderingNodes.forEach(nodeType => {
        expect([
          'RENDERING',
          'ENTITY' // 某些渲染节点可能属于ENTITY类别
        ]).toContain(nodeType.category);
      });
    });

    test('物理节点应该属于PHYSICS类别', () => {
      const physicsNodes = registry.getAllNodeTypes().filter(
        nodeType => nodeType.type.startsWith('physics/')
      );

      physicsNodes.forEach(nodeType => {
        expect(nodeType.category).toBe('PHYSICS');
      });
    });
  });
});
