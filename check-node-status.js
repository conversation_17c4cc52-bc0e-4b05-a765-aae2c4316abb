const fs = require('fs');
const path = require('path');

// 定义节点映射表 - 根据文档中的序号和实际实现的节点类型
const nodeMapping = {
  // 第一批次 001-050
  '001': 'core/events/onStart',
  '002': 'core/events/onUpdate', 
  '003': 'core/flow/branch',
  '004': 'core/flow/sequence',
  '005': 'core/debug/print',
  '006': 'math/basic/add',
  '007': 'math/basic/subtract',
  '008': 'math/basic/multiply',
  '009': 'math/basic/divide',
  '010': 'math/trigonometry/sin',
  '011': 'math/trigonometry/cos',
  '012': 'math/vector/magnitude',
  '013': 'math/vector/normalize',
  '014': 'logic/flow/branch',
  '015': 'logic/comparison/equal',
  '016': 'logic/comparison/notEqual',
  '017': 'logic/comparison/greater',
  '018': 'logic/comparison/less',
  '019': 'logic/logical/and',
  '020': 'logic/logical/or',
  '021': 'logic/logical/not',
  '022': 'entity/get',
  '023': 'entity/component/get',
  '024': 'entity/component/add',
  '025': 'entity/component/remove',
  '026': 'entity/transform/getPosition',
  '027': 'entity/transform/setPosition',
  '028': 'entity/transform/getRotation',
  '029': 'entity/transform/setRotation',
  '030': 'physics/raycast',
  '031': 'physics/applyForce',
  '032': 'physics/applyImpulse',
  '033': 'physics/setVelocity',
  '034': 'physics/getVelocity',
  '035': 'physics/collision/onEnter',
  '036': 'physics/collision/onExit',
  '037': 'physics/softbody/createCloth',
  '038': 'physics/softbody/createRope',
  '039': 'physics/softbody/createSoftBody',
  '040': 'physics/softbody/setStiffness',
  '041': 'physics/softbody/setDamping',
  '042': 'network/connectToServer',
  '043': 'network/sendMessage',
  '044': 'network/events/onMessage',
  '045': 'network/disconnect',
  '046': 'ai/animation/generateBodyAnimation',
  '047': 'ai/animation/generateFacialAnimation',
  '048': 'ai/model/load',
  '049': 'ai/model/generateText',
  '050': 'GetTime',
  // 第二批次 051-100
  '051': 'Delay',
  '052': 'Timer',
  '053': 'PlayAnimation',
  '054': 'StopAnimation',
  '055': 'SetAnimationSpeed',
  '056': 'GetAnimationState',
  '057': 'KeyboardInput',
  '058': 'MouseInput',
  '059': 'GamepadInput',
  '060': 'PlayAudio',
  '061': 'StopAudio',
  '062': 'SetVolume',
  '063': 'AudioAnalyzer',
  '064': 'Audio3D',
  '065': 'debug/breakpoint',
  '066': 'debug/log',
  '067': 'debug/performanceTimer',
  '068': 'debug/variableWatch',
  '069': 'debug/assert',
  '070': 'network/security/encryptData',
  '071': 'network/security/decryptData',
  '072': 'network/security/hashData',
  '073': 'network/security/authenticateUser',
  '074': 'network/security/validateSession',
  '075': 'network/webrtc/createConnection',
  '076': 'network/webrtc/sendDataChannelMessage',
  '077': 'network/webrtc/createDataChannel',
  '078': 'network/webrtc/closeConnection',
  '079': 'ai/emotion/analyze',
  '080': 'ai/emotion/driveAnimation',
  '081': 'ai/nlp/classifyText',
  '082': 'ai/nlp/recognizeEntities',
  '083': 'ai/nlp/analyzeSentiment',
  '084': 'ai/nlp/extractKeywords',
  '085': 'network/protocol/udpSend',
  '086': 'network/protocol/httpRequest',
  '087': 'network/protocol/tcpConnect',
  '088': 'string/concat',
  '089': 'string/substring',
  '090': 'string/replace',
  '091': 'string/split',
  '092': 'string/length',
  '093': 'string/toUpperCase',
  '094': 'string/toLowerCase',
  '095': 'string/trim',
  '096': 'array/push',
  '097': 'array/pop',
  '098': 'array/length',
  '099': 'array/get',
  '100': 'array/set'
};

// 检查引擎注册状态
function checkEngineRegistration(nodeType) {
  const engineFiles = [
    'engine/src/visualscript/presets/CoreNodes.ts',
    'engine/src/visualscript/presets/MathNodes.ts',
    'engine/src/visualscript/presets/LogicNodes.ts',
    'engine/src/visualscript/presets/EntityNodes.ts',
    'engine/src/visualscript/presets/PhysicsNodes.ts',
    'engine/src/visualscript/presets/SoftBodyNodes.ts',
    'engine/src/visualscript/presets/NetworkNodes.ts',
    'engine/src/visualscript/presets/AINodes.ts',
    'engine/src/visualscript/presets/TimeNodes.ts',
    'engine/src/visualscript/presets/AnimationNodes.ts',
    'engine/src/visualscript/presets/InputNodes.ts',
    'engine/src/visualscript/presets/AudioNodes.ts',
    'engine/src/visualscript/presets/DebugNodes.ts',
    'engine/src/visualscript/presets/NetworkSecurityNodes.ts',
    'engine/src/visualscript/presets/WebRTCNodes.ts',
    'engine/src/visualscript/presets/AIEmotionNodes.ts',
    'engine/src/visualscript/presets/AINLPNodes.ts',
    'engine/src/visualscript/presets/NetworkProtocolNodes.ts',
    'engine/src/visualscript/presets/StringNodes.ts',
    'engine/src/visualscript/presets/ArrayNodes.ts'
  ];

  for (const filePath of engineFiles) {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.includes(`type: '${nodeType}'`)) {
        return true;
      }
    }
  }
  return false;
}

// 检查编辑器注册状态
function checkEditorRegistration(nodeType) {
  const editorFile = 'editor/src/services/NodeRegistryService.ts';
  if (fs.existsSync(editorFile)) {
    const content = fs.readFileSync(editorFile, 'utf8');
    return content.includes(`type: '${nodeType}'`);
  }
  return false;
}

console.log('=== 检查前50个节点的实现状态 ===\n');

let completedCount = 0;
let engineRegisteredCount = 0;
let editorRegisteredCount = 0;

for (let i = 1; i <= 50; i++) {
  const nodeId = i.toString().padStart(3, '0');
  const nodeType = nodeMapping[nodeId];
  
  if (!nodeType) {
    console.log(`${nodeId}. 未定义节点类型`);
    continue;
  }

  const engineRegistered = checkEngineRegistration(nodeType);
  const editorRegistered = checkEditorRegistration(nodeType);
  const isCompleted = engineRegistered && editorRegistered;

  if (engineRegistered) engineRegisteredCount++;
  if (editorRegistered) editorRegisteredCount++;
  if (isCompleted) completedCount++;

  const status = isCompleted ? '✅' : '';
  const engineStatus = engineRegistered ? '引擎✓' : '引擎✗';
  const editorStatus = editorRegistered ? '编辑器✓' : '编辑器✗';
  
  console.log(`${status} ${nodeId}. ${nodeType} - ${engineStatus} ${editorStatus}`);
}

console.log(`\n=== 统计结果 ===`);
console.log(`完全完成的节点: ${completedCount}/50`);
console.log(`引擎注册的节点: ${engineRegisteredCount}/50`);
console.log(`编辑器注册的节点: ${editorRegisteredCount}/50`);
