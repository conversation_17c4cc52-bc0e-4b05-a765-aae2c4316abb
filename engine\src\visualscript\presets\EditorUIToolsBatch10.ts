/**
 * 编辑器UI与工具系统节点
 * 第10批次：编辑器UI与工具系统（节点271-300）
 * 包含场景编辑、UI编辑、工具辅助等功能
 */

import * as THREE from 'three';
import { Node, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// 场景编辑节点（271-280）
// ============================================================================

/**
 * 取消组合实体节点 (271)
 * 取消实体组合
 */
export class UngroupEntitiesNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'group',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '实体组'
    });
    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'entities',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '解组后的实体'
    });
  }

  public execute(): any {
    try {
      const group = this.getInputValue('group');
      const scene = this.getInputValue('scene');

      if (!group || !scene) {
        throw new Error('实体组和场景对象不能为空');
      }

      const entities = [];

      // 将组中的实体移动到场景中
      while (group.children.length > 0) {
        const entity = group.children[0];
        group.remove(entity);
        scene.add(entity);
        entities.push(entity);
      }

      // 移除空的组
      if (group.parent) {
        group.parent.remove(group);
      }

      // 设置输出值
      this.setOutputValue('entities', entities);

      return {
        completed: true,
        entities: entities
      };
    } catch (error) {
      console.error('取消组合实体失败:', error);
      this.setOutputValue('entities', []);
      return {
        completed: false,
        entities: []
      };
    }
  }
}

/**
 * 设置实体父对象节点 (272)
 * 设置实体的父子关系
 */
export class SetEntityParentNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '子实体'
    });
    this.addInput({
      name: 'parent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '父实体'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '子实体'
    });
  }

  public execute(): any {
    try {
      const entity = this.getInputValue('entity');
      const parent = this.getInputValue('parent');

      if (!entity || !parent) {
        throw new Error('实体和父对象不能为空');
      }

      // 从当前父对象中移除
      if (entity.parent) {
        entity.parent.remove(entity);
      }

      // 添加到新的父对象
      parent.add(entity);

      // 设置输出值
      this.setOutputValue('entity', entity);

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('设置实体父对象失败:', error);
      const entity = this.getInputValue('entity');
      this.setOutputValue('entity', entity);
      return {
        completed: false,
        entity: entity
      };
    }
  }
}

/**
 * 移动实体节点 (273)
 * 移动实体位置
 */
export class MoveEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });
    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.INPUT,
      description: '新位置',
      defaultValue: new THREE.Vector3(0, 0, 0)
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '实体对象'
    });
  }

  public execute(): any {
    try {
      const entity = this.getInputValue('entity');
      const position = this.getInputValue('position') || new THREE.Vector3(0, 0, 0);

      if (!entity) {
        throw new Error('实体对象不能为空');
      }

      // 设置实体位置
      entity.position.copy(position);

      // 设置输出值
      this.setOutputValue('entity', entity);

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('移动实体失败:', error);
      const entity = this.getInputValue('entity');
      this.setOutputValue('entity', entity);
      return {
        completed: false,
        entity: entity
      };
    }
  }
}

/**
 * 旋转实体节点 (274)
 * 旋转实体角度
 */
export class RotateEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });
    this.addInput({
      name: 'rotation',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.INPUT,
      description: '旋转角度',
      defaultValue: new THREE.Vector3(0, 0, 0)
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '实体对象'
    });
  }

  public execute(): any {
    try {
      const entity = this.getInputValue('entity');
      const rotation = this.getInputValue('rotation') || new THREE.Vector3(0, 0, 0);

      if (!entity) {
        throw new Error('实体对象不能为空');
      }

      // 设置实体旋转
      entity.rotation.set(rotation.x, rotation.y, rotation.z);

      // 设置输出值
      this.setOutputValue('entity', entity);

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('旋转实体失败:', error);
      const entity = this.getInputValue('entity');
      this.setOutputValue('entity', entity);
      return {
        completed: false,
        entity: entity
      };
    }
  }
}

/**
 * 缩放实体节点 (275)
 * 缩放实体大小
 */
export class ScaleEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });
    this.addInput({
      name: 'scale',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.INPUT,
      description: '缩放比例',
      defaultValue: new THREE.Vector3(1, 1, 1)
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '实体对象'
    });
  }

  public execute(): any {
    try {
      const entity = this.getInputValue('entity');
      const scale = this.getInputValue('scale') || new THREE.Vector3(1, 1, 1);

      if (!entity) {
        throw new Error('实体对象不能为空');
      }

      // 设置实体缩放
      entity.scale.copy(scale);

      // 设置输出值
      this.setOutputValue('entity', entity);

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('缩放实体失败:', error);
      const entity = this.getInputValue('entity');
      this.setOutputValue('entity', entity);
      return {
        completed: false,
        entity: entity
      };
    }
  }
}

/**
 * 隐藏实体节点 (276)
 * 隐藏场景实体
 */
export class HideEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '实体对象'
    });
  }

  public execute(): any {
    try {
      const entity = this.getInputValue('entity');

      if (!entity) {
        throw new Error('实体对象不能为空');
      }

      // 隐藏实体
      entity.visible = false;

      // 设置输出值
      this.setOutputValue('entity', entity);

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('隐藏实体失败:', error);
      const entity = this.getInputValue('entity');
      this.setOutputValue('entity', entity);
      return {
        completed: false,
        entity: entity
      };
    }
  }
}

/**
 * 显示实体节点 (277)
 * 显示场景实体
 */
export class ShowEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '实体对象'
    });
  }

  public execute(): any {
    try {
      const entity = this.getInputValue('entity');

      if (!entity) {
        throw new Error('实体对象不能为空');
      }

      // 显示实体
      entity.visible = true;

      // 设置输出值
      this.setOutputValue('entity', entity);

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('显示实体失败:', error);
      const entity = this.getInputValue('entity');
      this.setOutputValue('entity', entity);
      return {
        completed: false,
        entity: entity
      };
    }
  }
}

/**
 * 锁定实体节点 (278)
 * 锁定实体编辑
 */
export class LockEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '实体对象'
    });
  }

  public execute(): any {
    try {
      const entity = this.getInputValue('entity');

      if (!entity) {
        throw new Error('实体对象不能为空');
      }

      // 锁定实体编辑
      entity.userData = entity.userData || {};
      entity.userData.locked = true;

      // 设置输出值
      this.setOutputValue('entity', entity);

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('锁定实体失败:', error);
      const entity = this.getInputValue('entity');
      this.setOutputValue('entity', entity);
      return {
        completed: false,
        entity: entity
      };
    }
  }
}

/**
 * 解锁实体节点 (279)
 * 解锁实体编辑
 */
export class UnlockEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '实体对象'
    });
  }

  public execute(): any {
    try {
      const entity = this.getInputValue('entity');

      if (!entity) {
        throw new Error('实体对象不能为空');
      }

      // 解锁实体编辑
      entity.userData = entity.userData || {};
      entity.userData.locked = false;

      // 设置输出值
      this.setOutputValue('entity', entity);

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('解锁实体失败:', error);
      const entity = this.getInputValue('entity');
      this.setOutputValue('entity', entity);
      return {
        completed: false,
        entity: entity
      };
    }
  }
}

/**
 * 聚焦实体节点 (280)
 * 相机聚焦到实体
 */
export class FocusOnEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });
    this.addInput({
      name: 'camera',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '相机对象'
    });
    this.addInput({
      name: 'distance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '聚焦距离',
      defaultValue: 10
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'cameraPosition',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.OUTPUT,
      description: '相机位置'
    });
  }

  public execute(): any {
    try {
      const entity = this.getInputValue('entity');
      const camera = this.getInputValue('camera');
      const distance = this.getInputValue('distance') || 10;

      if (!entity || !camera) {
        throw new Error('实体对象和相机对象不能为空');
      }

      // 计算实体的包围盒
      const box = new THREE.Box3().setFromObject(entity);
      const center = box.getCenter(new THREE.Vector3());
      const size = box.getSize(new THREE.Vector3());

      // 计算合适的相机位置
      const maxDim = Math.max(size.x, size.y, size.z);
      const fov = camera.fov * (Math.PI / 180);
      const cameraDistance = Math.abs(maxDim / Math.sin(fov / 2)) + distance;

      // 设置相机位置
      const direction = new THREE.Vector3(1, 1, 1).normalize();
      const cameraPosition = center.clone().add(direction.multiplyScalar(cameraDistance));

      camera.position.copy(cameraPosition);
      camera.lookAt(center);

      // 设置输出值
      this.setOutputValue('cameraPosition', cameraPosition);

      return {
        completed: true,
        cameraPosition: cameraPosition
      };
    } catch (error) {
      console.error('聚焦实体失败:', error);
      const camera = this.getInputValue('camera');
      const cameraPosition = camera?.position || new THREE.Vector3();
      this.setOutputValue('cameraPosition', cameraPosition);
      return {
        completed: false,
        cameraPosition: cameraPosition
      };
    }
  }
}

// ============================================================================
// UI编辑节点（281-295）
// ============================================================================

/**
 * 创建UI元素节点 (281)
 * 创建用户界面元素
 */
export class CreateUIElementNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'elementType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'UI元素类型',
      defaultValue: 'div'
    });
    this.addInput({
      name: 'parent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '父容器',
      optional: true
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(): any {
    try {
      const elementType = this.getInputValue('elementType') || 'div';
      const parent = this.getInputValue('parent');

      // 创建UI元素
      const element = document.createElement(elementType);
      element.style.position = 'absolute';
      element.style.left = '0px';
      element.style.top = '0px';
      element.style.width = '100px';
      element.style.height = '100px';

      // 添加到父容器
      if (parent && parent.appendChild) {
        parent.appendChild(element);
      }

      // 设置输出值
      this.setOutputValue('element', element);

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('创建UI元素失败:', error);
      this.setOutputValue('element', null);
      return {
        completed: false,
        element: null
      };
    }
  }
}

/**
 * 删除UI元素节点 (282)
 * 删除用户界面元素
 */
export class DeleteUIElementNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'deleted',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '删除成功'
    });
  }

  public execute(): any {
    try {
      const element = this.getInputValue('element');

      if (!element) {
        throw new Error('UI元素不能为空');
      }

      // 从父容器中移除
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }

      // 设置输出值
      this.setOutputValue('deleted', true);

      return {
        completed: true,
        deleted: true
      };
    } catch (error) {
      console.error('删除UI元素失败:', error);
      this.setOutputValue('deleted', false);
      return {
        completed: false,
        deleted: false
      };
    }
  }
}

/**
 * 设置UI位置节点 (283)
 * 设置UI元素位置
 */
export class SetUIPositionNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addInput({
      name: 'x',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'X坐标',
      defaultValue: 0
    });
    this.addInput({
      name: 'y',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'Y坐标',
      defaultValue: 0
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(): any {
    try {
      const element = this.getInputValue('element');
      const x = this.getInputValue('x') || 0;
      const y = this.getInputValue('y') || 0;

      if (!element || !element.style) {
        throw new Error('UI元素不能为空');
      }

      // 设置位置
      element.style.left = `${x}px`;
      element.style.top = `${y}px`;

      // 设置输出值
      this.setOutputValue('element', element);

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI位置失败:', error);
      const element = this.getInputValue('element');
      this.setOutputValue('element', element);
      return {
        completed: false,
        element: element
      };
    }
  }
}

/**
 * 设置UI大小节点 (284)
 * 设置UI元素尺寸
 */
export class SetUISizeNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addInput({
      name: 'width',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '宽度',
      defaultValue: 100
    });
    this.addInput({
      name: 'height',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '高度',
      defaultValue: 100
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(): any {
    try {
      const element = this.getInputValue('element');
      const width = this.getInputValue('width') || 100;
      const height = this.getInputValue('height') || 100;

      if (!element || !element.style) {
        throw new Error('UI元素不能为空');
      }

      // 设置尺寸
      element.style.width = `${width}px`;
      element.style.height = `${height}px`;

      // 设置输出值
      this.setOutputValue('element', element);

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI大小失败:', error);
      const element = this.getInputValue('element');
      this.setOutputValue('element', element);
      return {
        completed: false,
        element: element
      };
    }
  }
}

/**
 * 设置UI文本节点 (285)
 * 设置UI元素文本内容
 */
export class SetUITextNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '文本内容',
      defaultValue: ''
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(): any {
    try {
      const element = this.getInputValue('element');
      const text = this.getInputValue('text') || '';

      if (!element) {
        throw new Error('UI元素不能为空');
      }

      // 设置文本内容
      if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
        element.value = text;
      } else {
        element.textContent = text;
      }

      // 设置输出值
      this.setOutputValue('element', element);

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI文本失败:', error);
      const element = this.getInputValue('element');
      this.setOutputValue('element', element);
      return {
        completed: false,
        element: element
      };
    }
  }
}

/**
 * 设置UI颜色节点 (286)
 * 设置UI元素颜色
 */
export class SetUIColorNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '颜色值',
      defaultValue: '#000000'
    });
    this.addInput({
      name: 'property',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '颜色属性',
      defaultValue: 'color'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(): any {
    try {
      const element = this.getInputValue('element');
      const color = this.getInputValue('color') || '#000000';
      const property = this.getInputValue('property') || 'color';

      if (!element || !element.style) {
        throw new Error('UI元素不能为空');
      }

      // 设置颜色
      element.style[property] = color;

      // 设置输出值
      this.setOutputValue('element', element);

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI颜色失败:', error);
      const element = this.getInputValue('element');
      this.setOutputValue('element', element);
      return {
        completed: false,
        element: element
      };
    }
  }
}

/**
 * 设置UI字体节点 (287)
 * 设置UI元素字体
 */
export class SetUIFontNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addInput({
      name: 'fontFamily',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '字体族',
      defaultValue: 'Arial'
    });
    this.addInput({
      name: 'fontSize',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '字体大小',
      defaultValue: 14
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(): any {
    try {
      const element = this.getInputValue('element');
      const fontFamily = this.getInputValue('fontFamily') || 'Arial';
      const fontSize = this.getInputValue('fontSize') || 14;

      if (!element || !element.style) {
        throw new Error('UI元素不能为空');
      }

      // 设置字体
      element.style.fontFamily = fontFamily;
      element.style.fontSize = `${fontSize}px`;

      // 设置输出值
      this.setOutputValue('element', element);

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI字体失败:', error);
      const element = this.getInputValue('element');
      this.setOutputValue('element', element);
      return {
        completed: false,
        element: element
      };
    }
  }
}

/**
 * 设置UI图像节点 (288)
 * 设置UI元素背景图像
 */
export class SetUIImageNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addInput({
      name: 'imageUrl',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '图像URL'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(): any {
    try {
      const element = this.getInputValue('element');
      const imageUrl = this.getInputValue('imageUrl');

      if (!element || !element.style) {
        throw new Error('UI元素不能为空');
      }

      if (!imageUrl) {
        throw new Error('图像URL不能为空');
      }

      // 设置背景图像
      if (element.tagName === 'IMG') {
        element.src = imageUrl;
      } else {
        element.style.backgroundImage = `url(${imageUrl})`;
        element.style.backgroundSize = 'cover';
        element.style.backgroundPosition = 'center';
      }

      // 设置输出值
      this.setOutputValue('element', element);

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI图像失败:', error);
      const element = this.getInputValue('element');
      this.setOutputValue('element', element);
      return {
        completed: false,
        element: element
      };
    }
  }
}

/**
 * 添加UI事件节点 (289)
 * 为UI元素添加事件
 */
export class AddUIEventNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addInput({
      name: 'eventType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '事件类型',
      defaultValue: 'click'
    });
    this.addInput({
      name: 'callback',
      type: SocketType.DATA,
      dataType: 'function',
      direction: SocketDirection.INPUT,
      description: '回调函数'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(): any {
    try {
      const element = this.getInputValue('element');
      const eventType = this.getInputValue('eventType') || 'click';
      const callback = this.getInputValue('callback');

      if (!element) {
        throw new Error('UI元素不能为空');
      }

      if (!callback || typeof callback !== 'function') {
        throw new Error('回调函数不能为空');
      }

      // 添加事件监听器
      element.addEventListener(eventType, callback);

      // 设置输出值
      this.setOutputValue('element', element);

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('添加UI事件失败:', error);
      const element = this.getInputValue('element');
      this.setOutputValue('element', element);
      return {
        completed: false,
        element: element
      };
    }
  }
}

/**
 * 移除UI事件节点 (290)
 * 移除UI元素事件
 */
export class RemoveUIEventNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addInput({
      name: 'eventType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '事件类型',
      defaultValue: 'click'
    });
    this.addInput({
      name: 'callback',
      type: SocketType.DATA,
      dataType: 'function',
      direction: SocketDirection.INPUT,
      description: '回调函数'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(): any {
    try {
      const element = this.getInputValue('element');
      const eventType = this.getInputValue('eventType') || 'click';
      const callback = this.getInputValue('callback');

      if (!element) {
        throw new Error('UI元素不能为空');
      }

      if (!callback || typeof callback !== 'function') {
        throw new Error('回调函数不能为空');
      }

      // 移除事件监听器
      element.removeEventListener(eventType, callback);

      // 设置输出值
      this.setOutputValue('element', element);

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('移除UI事件失败:', error);
      const element = this.getInputValue('element');
      this.setOutputValue('element', element);
      return {
        completed: false,
        element: element
      };
    }
  }
}

// ============================================================================
// UI控制和工具辅助节点（291-300）
// ============================================================================

/**
 * 设置UI可见性节点 (291)
 * 设置UI元素显示状态
 */
export class SetUIVisibleNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addInput({
      name: 'visible',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否可见',
      defaultValue: true
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(): any {
    try {
      const element = this.getInputValue('element');
      const visible = this.getInputValue('visible') !== false;

      if (!element) {
        throw new Error('UI元素不能为空');
      }

      // 设置可见性
      if (element.style) {
        element.style.display = visible ? 'block' : 'none';
      }

      // 设置输出值
      this.setOutputValue('element', element);

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI可见性失败:', error);
      const element = this.getInputValue('element');
      this.setOutputValue('element', element);
      return {
        completed: false,
        element: element
      };
    }
  }
}

/**
 * 设置UI启用状态节点 (292)
 * 设置UI元素交互状态
 */
export class SetUIEnabledNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addInput({
      name: 'enabled',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否启用',
      defaultValue: true
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(): any {
    try {
      const element = this.getInputValue('element');
      const enabled = this.getInputValue('enabled') !== false;

      if (!element) {
        throw new Error('UI元素不能为空');
      }

      // 设置启用状态
      element.disabled = !enabled;

      if (element.style) {
        element.style.pointerEvents = enabled ? 'auto' : 'none';
        element.style.opacity = enabled ? '1' : '0.5';
      }

      // 设置输出值
      this.setOutputValue('element', element);

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI启用状态失败:', error);
      const element = this.getInputValue('element');
      this.setOutputValue('element', element);
      return {
        completed: false,
        element: element
      };
    }
  }
}

/**
 * 设置UI层级节点 (293)
 * 设置UI元素渲染层级
 */
export class SetUILayerNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addInput({
      name: 'zIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'Z轴层级',
      defaultValue: 0
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(): any {
    try {
      const element = this.getInputValue('element');
      const zIndex = this.getInputValue('zIndex') || 0;

      if (!element || !element.style) {
        throw new Error('UI元素不能为空');
      }

      // 设置层级
      element.style.zIndex = zIndex.toString();

      // 设置输出值
      this.setOutputValue('element', element);

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI层级失败:', error);
      const element = this.getInputValue('element');
      this.setOutputValue('element', element);
      return {
        completed: false,
        element: element
      };
    }
  }
}

/**
 * 对齐UI元素节点 (294)
 * 对齐多个UI元素
 */
export class AlignUIElementsNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'elements',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: 'UI元素数组'
    });
    this.addInput({
      name: 'alignment',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '对齐方式',
      defaultValue: 'left'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'elements',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: 'UI元素数组'
    });
  }

  public execute(): any {
    try {
      const elements = this.getInputValue('elements');
      const alignment = this.getInputValue('alignment') || 'left';

      if (!elements || !Array.isArray(elements) || elements.length === 0) {
        throw new Error('UI元素数组不能为空');
      }

      // 对齐元素
      const firstElement = elements[0];
      if (!firstElement || !firstElement.style) {
        throw new Error('第一个元素无效');
      }

      const referenceRect = firstElement.getBoundingClientRect();

      elements.forEach((element, index) => {
        if (index === 0 || !element.style) return;

        switch (alignment) {
          case 'left':
            element.style.left = firstElement.style.left;
            break;
          case 'right':
            const elementRect = element.getBoundingClientRect();
            const offset = referenceRect.right - elementRect.right;
            element.style.left = `${parseInt(element.style.left || '0') + offset}px`;
            break;
          case 'top':
            element.style.top = firstElement.style.top;
            break;
          case 'bottom':
            const elementRect2 = element.getBoundingClientRect();
            const offset2 = referenceRect.bottom - elementRect2.bottom;
            element.style.top = `${parseInt(element.style.top || '0') + offset2}px`;
            break;
        }
      });

      // 设置输出值
      this.setOutputValue('elements', elements);

      return {
        completed: true,
        elements: elements
      };
    } catch (error) {
      console.error('对齐UI元素失败:', error);
      const elements = this.getInputValue('elements');
      this.setOutputValue('elements', elements);
      return {
        completed: false,
        elements: elements
      };
    }
  }
}

/**
 * 分布UI元素节点 (295)
 * 均匀分布UI元素
 */
export class DistributeUIElementsNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'elements',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: 'UI元素数组'
    });
    this.addInput({
      name: 'direction',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '分布方向',
      defaultValue: 'horizontal'
    });
    this.addInput({
      name: 'spacing',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '间距',
      defaultValue: 10
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'elements',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: 'UI元素数组'
    });
  }

  public execute(): any {
    try {
      const elements = this.getInputValue('elements');
      const direction = this.getInputValue('direction') || 'horizontal';
      const spacing = this.getInputValue('spacing') || 10;

      if (!elements || !Array.isArray(elements) || elements.length === 0) {
        throw new Error('UI元素数组不能为空');
      }

      // 均匀分布元素
      elements.forEach((element, index) => {
        if (element.style) {
          if (direction === 'horizontal') {
            element.style.left = `${index * spacing}px`;
          } else if (direction === 'vertical') {
            element.style.top = `${index * spacing}px`;
          }
        }
      });

      // 设置输出值
      this.setOutputValue('elements', elements);

      return {
        completed: true,
        elements: elements
      };
    } catch (error) {
      console.error('分布UI元素失败:', error);
      const elements = this.getInputValue('elements');
      this.setOutputValue('elements', elements);
      return {
        completed: false,
        elements: elements
      };
    }
  }
}

/**
 * 启用3D操作手柄节点 (296)
 * 启用3D操作手柄
 */
export class EnableGizmoNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'enabled',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否启用',
      defaultValue: true
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'enabled',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '启用状态'
    });
  }

  public execute(): any {
    try {
      const enabled = this.getInputValue('enabled') !== false;

      // 这里应该调用编辑器的Gizmo控制接口
      // 由于这是示例代码，我们只是模拟操作
      console.log(`3D操作手柄${enabled ? '启用' : '禁用'}`);

      // 设置输出值
      this.setOutputValue('enabled', enabled);

      return {
        completed: true,
        enabled: enabled
      };
    } catch (error) {
      console.error('启用3D操作手柄失败:', error);
      const enabled = this.getInputValue('enabled');
      this.setOutputValue('enabled', enabled);
      return {
        completed: false,
        enabled: enabled
      };
    }
  }
}

/**
 * 设置操作手柄模式节点 (297)
 * 设置操作手柄模式
 */
export class SetGizmoModeNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'mode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '操作模式',
      defaultValue: 'translate'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'mode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '当前模式'
    });
  }

  public execute(): any {
    try {
      const mode = this.getInputValue('mode') || 'translate';

      // 这里应该调用编辑器的Gizmo模式设置接口
      console.log(`设置操作手柄模式为: ${mode}`);

      // 设置输出值
      this.setOutputValue('mode', mode);

      return {
        completed: true,
        mode: mode
      };
    } catch (error) {
      console.error('设置操作手柄模式失败:', error);
      const mode = this.getInputValue('mode');
      this.setOutputValue('mode', mode);
      return {
        completed: false,
        mode: mode
      };
    }
  }
}

/**
 * 启用场景网格节点 (298)
 * 启用场景网格显示
 */
export class EnableGridNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'enabled',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否启用',
      defaultValue: true
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'enabled',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '启用状态'
    });
  }

  public execute(): any {
    try {
      const enabled = this.getInputValue('enabled') !== false;

      // 这里应该调用编辑器的网格显示控制接口
      console.log(`场景网格${enabled ? '启用' : '禁用'}`);

      // 设置输出值
      this.setOutputValue('enabled', enabled);

      return {
        completed: true,
        enabled: enabled
      };
    } catch (error) {
      console.error('启用场景网格失败:', error);
      const enabled = this.getInputValue('enabled');
      this.setOutputValue('enabled', enabled);
      return {
        completed: false,
        enabled: enabled
      };
    }
  }
}

/**
 * 设置网格间距节点 (299)
 * 设置网格间距
 */
export class SetGridSizeNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '网格间距',
      defaultValue: 1
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'size',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '网格间距'
    });
  }

  public execute(): any {
    try {
      const size = this.getInputValue('size') || 1;

      // 这里应该调用编辑器的网格间距设置接口
      console.log(`设置网格间距为: ${size}`);

      // 设置输出值
      this.setOutputValue('size', size);

      return {
        completed: true,
        size: size
      };
    } catch (error) {
      console.error('设置网格间距失败:', error);
      const size = this.getInputValue('size');
      this.setOutputValue('size', size);
      return {
        completed: false,
        size: size
      };
    }
  }
}

/**
 * 启用对象吸附节点 (300)
 * 启用对象吸附功能
 */
export class EnableSnapNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'enabled',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否启用',
      defaultValue: true
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'enabled',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '启用状态'
    });
  }

  public execute(): any {
    try {
      const enabled = this.getInputValue('enabled') !== false;

      // 这里应该调用编辑器的对象吸附控制接口
      console.log(`对象吸附功能${enabled ? '启用' : '禁用'}`);

      // 设置输出值
      this.setOutputValue('enabled', enabled);

      return {
        completed: true,
        enabled: enabled
      };
    } catch (error) {
      console.error('启用对象吸附失败:', error);
      const enabled = this.getInputValue('enabled');
      this.setOutputValue('enabled', enabled);
      return {
        completed: false,
        enabled: enabled
      };
    }
  }
}
