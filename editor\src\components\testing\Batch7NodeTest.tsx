/**
 * 第7批次节点测试组件
 * 用于测试新增的动画系统扩展节点功能
 */
import React, { useState, useEffect } from 'react';
import { Card, Button, Space, message, Descriptions, Tag, Alert, Divider } from 'antd';
import { 
  PlayCircleOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined,
  LoadingOutlined,
  ExperimentOutlined
} from '@ant-design/icons';
import { nodeRegistryService } from '../../services/NodeRegistryService';
import { engineNodeIntegration } from '../../services/EngineNodeIntegration';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  details?: string;
}

/**
 * 第7批次节点测试组件
 */
const Batch7NodeTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [nodeStats, setNodeStats] = useState({
    total: 0,
    animation: 0,
    audio: 0,
    scene: 0
  });

  useEffect(() => {
    // 统计节点数量
    const allNodes = nodeRegistryService.getAllNodes();
    const batch7Nodes = allNodes.filter(node => 
      node.type.startsWith('animation/curve/') ||
      node.type.startsWith('animation/state/') ||
      node.type.startsWith('audio/') ||
      node.type.startsWith('scene/')
    );

    const animationNodes = batch7Nodes.filter(node => 
      node.type.startsWith('animation/')
    );
    const audioNodes = batch7Nodes.filter(node => 
      node.type.startsWith('audio/')
    );
    const sceneNodes = batch7Nodes.filter(node => 
      node.type.startsWith('scene/')
    );

    setNodeStats({
      total: batch7Nodes.length,
      animation: animationNodes.length,
      audio: audioNodes.length,
      scene: sceneNodes.length
    });
  }, []);

  /**
   * 运行所有测试
   */
  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    const tests: TestResult[] = [];

    try {
      // 测试1: 节点注册测试
      tests.push(await testNodeRegistration());

      // 测试2: 动画节点测试
      tests.push(await testAnimationNodes());

      // 测试3: 音频节点测试
      tests.push(await testAudioNodes());

      // 测试4: 场景管理节点测试
      tests.push(await testSceneManagementNodes());

      // 测试5: 引擎集成测试
      tests.push(await testEngineIntegration());

      setTestResults(tests);

      // 显示测试结果
      const successCount = tests.filter(t => t.status === 'success').length;
      const totalCount = tests.length;

      if (successCount === totalCount) {
        message.success(`所有测试通过！(${successCount}/${totalCount})`);
      } else {
        message.warning(`部分测试失败 (${successCount}/${totalCount})`);
      }

    } catch (error) {
      console.error('测试运行失败:', error);
      message.error('测试运行失败');
    } finally {
      setIsRunning(false);
    }
  };

  /**
   * 测试节点注册
   */
  const testNodeRegistration = async (): Promise<TestResult> => {
    try {
      const allNodes = nodeRegistryService.getAllNodes();
      const batch7Nodes = allNodes.filter(node => 
        node.type.startsWith('animation/curve/') ||
        node.type.startsWith('animation/state/') ||
        node.type.startsWith('audio/') ||
        node.type.startsWith('scene/')
      );

      if (batch7Nodes.length >= 30) {
        return {
          name: '节点注册测试',
          status: 'success',
          message: `成功注册 ${batch7Nodes.length} 个节点`,
          details: `动画节点: ${nodeStats.animation}, 音频节点: ${nodeStats.audio}, 场景节点: ${nodeStats.scene}`
        };
      } else {
        return {
          name: '节点注册测试',
          status: 'error',
          message: `节点数量不足，期望30个，实际${batch7Nodes.length}个`,
          details: '部分节点可能未正确注册'
        };
      }
    } catch (error) {
      return {
        name: '节点注册测试',
        status: 'error',
        message: '节点注册测试失败',
        details: error instanceof Error ? error.message : '未知错误'
      };
    }
  };

  /**
   * 测试动画节点
   */
  const testAnimationNodes = async (): Promise<TestResult> => {
    try {
      const animationNodes = [
        'animation/curve/evaluateCurve',
        'animation/state/createStateMachine',
        'animation/state/addState',
        'animation/state/addTransition',
        'animation/state/setCurrentState'
      ];

      const missingNodes = animationNodes.filter(nodeType =>
        !nodeRegistryService.getNode(nodeType)
      );

      if (missingNodes.length === 0) {
        return {
          name: '动画节点测试',
          status: 'success',
          message: '所有动画节点注册成功',
          details: `包含曲线计算、状态机等5个节点`
        };
      } else {
        return {
          name: '动画节点测试',
          status: 'error',
          message: `缺少 ${missingNodes.length} 个动画节点`,
          details: `缺少节点: ${missingNodes.join(', ')}`
        };
      }
    } catch (error) {
      return {
        name: '动画节点测试',
        status: 'error',
        message: '动画节点测试失败',
        details: error instanceof Error ? error.message : '未知错误'
      };
    }
  };

  /**
   * 测试音频节点
   */
  const testAudioNodes = async (): Promise<TestResult> => {
    try {
      const audioNodes = [
        'audio/source/create3DAudioSource',
        'audio/source/setAudioPosition',
        'audio/listener/setListenerPosition',
        'audio/effect/createReverb',
        'audio/analysis/createAnalyzer',
        'audio/recording/startRecording'
      ];

      const registeredNodes = audioNodes.filter(nodeType =>
        nodeRegistryService.getNode(nodeType)
      );

      if (registeredNodes.length >= 6) {
        return {
          name: '音频节点测试',
          status: 'success',
          message: `音频节点注册成功 (${registeredNodes.length}/15)`,
          details: '包含3D音频、效果处理、分析等功能'
        };
      } else {
        return {
          name: '音频节点测试',
          status: 'error',
          message: `音频节点注册不足`,
          details: `仅注册了 ${registeredNodes.length} 个音频节点`
        };
      }
    } catch (error) {
      return {
        name: '音频节点测试',
        status: 'error',
        message: '音频节点测试失败',
        details: error instanceof Error ? error.message : '未知错误'
      };
    }
  };

  /**
   * 测试场景管理节点
   */
  const testSceneManagementNodes = async (): Promise<TestResult> => {
    try {
      const sceneNodes = [
        'scene/management/createScene',
        'scene/management/loadScene',
        'scene/management/addToScene',
        'scene/culling/enableFrustumCulling',
        'scene/optimization/enableBatching'
      ];

      const registeredNodes = sceneNodes.filter(nodeType =>
        nodeRegistryService.getNode(nodeType)
      );

      if (registeredNodes.length >= 5) {
        return {
          name: '场景管理节点测试',
          status: 'success',
          message: `场景管理节点注册成功 (${registeredNodes.length}/10)`,
          details: '包含场景创建、优化、剔除等功能'
        };
      } else {
        return {
          name: '场景管理节点测试',
          status: 'error',
          message: `场景管理节点注册不足`,
          details: `仅注册了 ${registeredNodes.length} 个场景节点`
        };
      }
    } catch (error) {
      return {
        name: '场景管理节点测试',
        status: 'error',
        message: '场景管理节点测试失败',
        details: error instanceof Error ? error.message : '未知错误'
      };
    }
  };

  /**
   * 测试引擎集成
   */
  const testEngineIntegration = async (): Promise<TestResult> => {
    try {
      // 检查引擎集成服务是否已初始化
      const isInitialized = engineNodeIntegration.getInitializationStatus();

      if (isInitialized) {
        return {
          name: '引擎集成测试',
          status: 'success',
          message: '引擎集成服务已正确初始化',
          details: '第7批次节点已集成到引擎中'
        };
      } else {
        return {
          name: '引擎集成测试',
          status: 'error',
          message: '引擎集成服务未初始化',
          details: '需要确保引擎服务正确启动'
        };
      }
    } catch (error) {
      return {
        name: '引擎集成测试',
        status: 'error',
        message: '引擎集成测试失败',
        details: error instanceof Error ? error.message : '未知错误'
      };
    }
  };

  /**
   * 获取状态图标
   */
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <LoadingOutlined />;
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title="第7批次节点测试 - 动画系统扩展（节点181-210）" style={{ marginBottom: '24px' }}>
        <Alert
          message="第7批次节点功能测试"
          description="测试动画曲线、状态机、高级音频系统、场景管理等30个新增节点的注册和集成情况"
          type="info"
          showIcon
          style={{ marginBottom: '16px' }}
        />

        <Descriptions bordered column={2} style={{ marginBottom: '16px' }}>
          <Descriptions.Item label="节点总数">{nodeStats.total}</Descriptions.Item>
          <Descriptions.Item label="动画节点">{nodeStats.animation}</Descriptions.Item>
          <Descriptions.Item label="音频节点">{nodeStats.audio}</Descriptions.Item>
          <Descriptions.Item label="场景节点">{nodeStats.scene}</Descriptions.Item>
        </Descriptions>

        <Space style={{ marginBottom: '16px' }}>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={runAllTests}
            loading={isRunning}
            disabled={isRunning}
          >
            {isRunning ? '测试运行中...' : '运行所有测试'}
          </Button>
          <Button
            icon={<ExperimentOutlined />}
            onClick={() => window.open('/visual-script-editor', '_blank')}
          >
            打开节点编辑器
          </Button>
        </Space>

        <Divider />

        {testResults.length > 0 && (
          <div>
            <h3>测试结果</h3>
            {testResults.map((result, index) => (
              <Card
                key={index}
                size="small"
                style={{ marginBottom: '8px' }}
                title={
                  <Space>
                    {getStatusIcon(result.status)}
                    {result.name}
                    <Tag color={result.status === 'success' ? 'green' : result.status === 'error' ? 'red' : 'blue'}>
                      {result.status === 'success' ? '通过' : result.status === 'error' ? '失败' : '运行中'}
                    </Tag>
                  </Space>
                }
              >
                <p>{result.message}</p>
                {result.details && (
                  <p style={{ color: '#666', fontSize: '12px' }}>
                    详情: {result.details}
                  </p>
                )}
              </Card>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
};

export default Batch7NodeTest;
