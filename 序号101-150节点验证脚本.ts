/**
 * 序号101-150节点验证脚本
 * 验证这50个节点是否都能在编辑器中通过拖拽方式使用
 */

import { NodeRegistryService } from './editor/src/services/NodeRegistryService';

/**
 * 序号101-150的50个节点列表
 */
const BATCH4_5_6_NODES = [
  // 第4批次：对象操作节点 (104-110)
  { id: 104, type: 'object/getProperty', name: '获取属性' },
  { id: 105, type: 'object/setProperty', name: '设置属性' },
  { id: 106, type: 'object/hasProperty', name: '检查属性' },
  { id: 107, type: 'object/keys', name: '获取键列表' },
  { id: 108, type: 'object/values', name: '获取值列表' },
  { id: 109, type: 'object/merge', name: '对象合并' },
  { id: 110, type: 'object/clone', name: '对象克隆' },
  
  // 第4批次：变量操作节点 (111-117)
  { id: 111, type: 'variable/get', name: '获取变量' },
  { id: 112, type: 'variable/set', name: '设置变量' },
  { id: 113, type: 'variable/increment', name: '变量递增' },
  { id: 114, type: 'variable/decrement', name: '变量递减' },
  { id: 115, type: 'variable/exists', name: '变量存在' },
  { id: 116, type: 'variable/delete', name: '删除变量' },
  { id: 117, type: 'variable/type', name: '变量类型' },
  
  // 第4批次：渲染相机节点 (118-120)
  { id: 118, type: 'rendering/camera/createPerspectiveCamera', name: '创建透视相机' },
  { id: 119, type: 'rendering/camera/createOrthographicCamera', name: '创建正交相机' },
  { id: 120, type: 'rendering/camera/setCameraPosition', name: '设置相机位置' },
  
  // 第5批次：渲染系统核心节点 (121-140)
  { id: 121, type: 'rendering/camera/setCameraTarget', name: '设置相机目标' },
  { id: 122, type: 'rendering/camera/setCameraFOV', name: '设置相机视野' },
  { id: 123, type: 'rendering/light/createDirectionalLight', name: '创建方向光' },
  { id: 124, type: 'rendering/light/createPointLight', name: '创建点光源' },
  { id: 125, type: 'rendering/light/createSpotLight', name: '创建聚光灯' },
  { id: 126, type: 'rendering/light/createAmbientLight', name: '创建环境光' },
  { id: 127, type: 'rendering/light/setLightColor', name: '设置光源颜色' },
  { id: 128, type: 'rendering/light/setLightIntensity', name: '设置光源强度' },
  { id: 129, type: 'rendering/shadow/enableShadows', name: '启用阴影' },
  { id: 130, type: 'rendering/shadow/setShadowMapSize', name: '设置阴影贴图大小' },
  { id: 131, type: 'rendering/material/createBasicMaterial', name: '创建基础材质' },
  { id: 132, type: 'rendering/material/createStandardMaterial', name: '创建标准材质' },
  { id: 133, type: 'rendering/material/createPhysicalMaterial', name: '创建物理材质' },
  { id: 134, type: 'rendering/material/setMaterialColor', name: '设置材质颜色' },
  { id: 135, type: 'rendering/material/setMaterialTexture', name: '设置材质纹理' },
  { id: 136, type: 'rendering/material/setMaterialOpacity', name: '设置材质透明度' },
  { id: 137, type: 'rendering/postprocess/enableFXAA', name: '启用抗锯齿' },
  { id: 138, type: 'rendering/postprocess/enableSSAO', name: '启用环境光遮蔽' },
  { id: 139, type: 'rendering/postprocess/enableBloom', name: '启用辉光效果' },
  { id: 140, type: 'rendering/lod/setLODLevel', name: '设置LOD级别' },
  
  // 第6批次：高级物理系统节点 (141-150)
  { id: 141, type: 'physics/advanced/createSoftBody', name: '创建软体' },
  { id: 142, type: 'physics/advanced/createFluid', name: '创建流体' },
  { id: 143, type: 'physics/advanced/createCloth', name: '创建布料' },
  { id: 144, type: 'physics/advanced/createParticleSystem', name: '创建粒子系统' },
  { id: 145, type: 'physics/advanced/setGravity', name: '设置重力' },
  { id: 146, type: 'physics/advanced/createJoint', name: '创建关节' },
  { id: 147, type: 'physics/advanced/setDamping', name: '设置阻尼' },
  { id: 148, type: 'physics/advanced/createConstraint', name: '创建约束' },
  { id: 149, type: 'physics/advanced/simulateWind', name: '模拟风力' },
  { id: 150, type: 'physics/advanced/createExplosion', name: '创建爆炸' }
];

/**
 * 验证结果接口
 */
interface ValidationResult {
  nodeId: number;
  nodeType: string;
  nodeName: string;
  editorRegistered: boolean;
  draggable: boolean;
  status: 'success' | 'warning' | 'error';
  issues: string[];
}

/**
 * 验证序号101-150的50个节点
 */
export function validateBatch4To6Nodes(): {
  summary: {
    total: number;
    success: number;
    warning: number;
    error: number;
  };
  results: ValidationResult[];
  recommendations: string[];
} {
  console.log('🔍 开始验证序号101-150的50个节点...');
  
  const nodeRegistry = NodeRegistryService.getInstance();
  
  const results: ValidationResult[] = [];
  let successCount = 0;
  let warningCount = 0;
  let errorCount = 0;

  // 获取所有已注册的节点
  const allRegisteredNodes = nodeRegistry.getAllNodes();
  const registeredNodeTypes = new Set(allRegisteredNodes.map(node => node.type));

  // 验证每个节点
  BATCH4_5_6_NODES.forEach(node => {
    const result: ValidationResult = {
      nodeId: node.id,
      nodeType: node.type,
      nodeName: node.name,
      editorRegistered: false,
      draggable: false,
      status: 'error',
      issues: []
    };

    // 检查编辑器注册状态
    result.editorRegistered = registeredNodeTypes.has(node.type);
    if (!result.editorRegistered) {
      result.issues.push('未在编辑器中注册');
    }

    // 检查拖拽功能（基于编辑器注册状态）
    result.draggable = result.editorRegistered;
    if (!result.draggable) {
      result.issues.push('无法拖拽使用');
    }

    // 确定状态
    if (result.editorRegistered && result.draggable) {
      result.status = 'success';
      successCount++;
    } else if (result.editorRegistered || result.draggable) {
      result.status = 'warning';
      warningCount++;
    } else {
      result.status = 'error';
      errorCount++;
    }

    results.push(result);
  });

  // 生成建议
  const recommendations: string[] = [];
  if (errorCount > 0) {
    recommendations.push(`需要修复 ${errorCount} 个节点的注册问题`);
  }
  if (warningCount > 0) {
    recommendations.push(`需要完善 ${warningCount} 个节点的集成`);
  }
  if (successCount === BATCH4_5_6_NODES.length) {
    recommendations.push('🎉 所有节点都已正确注册和集成！');
  }

  // 输出结果
  console.log('\n📊 验证结果统计:');
  console.log(`总计: ${BATCH4_5_6_NODES.length}个节点`);
  console.log(`✅ 成功: ${successCount}个 (${Math.round(successCount/BATCH4_5_6_NODES.length*100)}%)`);
  console.log(`⚠️ 警告: ${warningCount}个 (${Math.round(warningCount/BATCH4_5_6_NODES.length*100)}%)`);
  console.log(`❌ 错误: ${errorCount}个 (${Math.round(errorCount/BATCH4_5_6_NODES.length*100)}%)`);

  // 按批次输出详细结果
  console.log('\n📋 分批次验证结果:');
  console.log('第4批次 (101-120): 对象操作、变量操作、渲染基础节点');
  console.log('第5批次 (121-140): 渲染系统核心节点');
  console.log('第6批次 (141-150): 高级物理系统节点');

  return {
    summary: {
      total: BATCH4_5_6_NODES.length,
      success: successCount,
      warning: warningCount,
      error: errorCount
    },
    results,
    recommendations
  };
}

/**
 * 运行验证（如果直接执行此文件）
 */
if (require.main === module) {
  validateBatch4To6Nodes();
}
