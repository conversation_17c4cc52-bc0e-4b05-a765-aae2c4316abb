/**
 * GraphJSON 修复验证测试
 * 验证 GraphJSON 接口和 VisualScriptEngine 构造函数修复
 */
import { GraphJSON } from '../visualscript/graph/GraphJSON';
import { VisualScriptEngine, VisualScriptEngineOptions } from '../visualscript/VisualScriptEngine';
import { NodeRegistry } from '../visualscript/nodes/NodeRegistry';
import { ValueTypeRegistry } from '../visualscript/values/ValueTypeRegistry';
import { Entity } from '../core/Entity';
import { World } from '../core/World';
import { Engine } from '../core/Engine';

describe('GraphJSON 修复验证测试', () => {
  describe('GraphJSON 接口', () => {
    test('应该能够创建有效的 GraphJSON 对象', () => {
      const validGraphJSON: GraphJSON = {
        name: '测试图形',
        description: '测试描述',
        nodes: [],
        variables: [],
        customEvents: []
      };

      expect(validGraphJSON).toBeDefined();
      expect(validGraphJSON.name).toBe('测试图形');
      expect(validGraphJSON.nodes).toEqual([]);
      expect(validGraphJSON.variables).toEqual([]);
      expect(validGraphJSON.customEvents).toEqual([]);
    });

    test('GraphJSON 应该支持可选属性', () => {
      const graphWithOptionals: GraphJSON = {
        version: '1.0.0',
        name: '完整测试图形',
        description: '包含所有可选属性的测试',
        nodes: [],
        variables: [],
        customEvents: [],
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          author: '测试作者',
          tags: ['test', 'example'],
          customProperty: 'custom value'
        }
      };

      expect(graphWithOptionals.version).toBe('1.0.0');
      expect(graphWithOptionals.metadata).toBeDefined();
      expect(graphWithOptionals.metadata?.author).toBe('测试作者');
      expect(graphWithOptionals.metadata?.tags).toContain('test');
    });
  });

  describe('VisualScriptEngine 构造函数', () => {
    let engine: Engine;
    let world: World;
    let entity: Entity;
    let nodeRegistry: NodeRegistry;
    let valueTypeRegistry: ValueTypeRegistry;

    beforeEach(() => {
      engine = new Engine();
      world = new World(engine);
      entity = new Entity();
      nodeRegistry = new NodeRegistry();
      valueTypeRegistry = new ValueTypeRegistry();
    });

    test('应该能够创建有效的 VisualScriptEngineOptions', () => {
      const mockScript: GraphJSON = {
        name: '测试脚本',
        description: '用于测试的脚本',
        nodes: [],
        variables: [],
        customEvents: []
      };

      const options: VisualScriptEngineOptions = {
        script: mockScript,
        nodeRegistry: nodeRegistry,
        valueTypeRegistry: valueTypeRegistry,
        entity: entity,
        world: world
      };

      expect(options).toBeDefined();
      expect(options.script).toBe(mockScript);
      expect(options.nodeRegistry).toBe(nodeRegistry);
      expect(options.valueTypeRegistry).toBe(valueTypeRegistry);
      expect(options.entity).toBe(entity);
      expect(options.world).toBe(world);
    });

    test('应该能够创建 VisualScriptEngine 实例', () => {
      const mockScript: GraphJSON = {
        name: '测试脚本引擎',
        description: '用于测试引擎创建的脚本',
        nodes: [],
        variables: [],
        customEvents: []
      };

      expect(() => {
        const visualScriptEngine = new VisualScriptEngine({
          script: mockScript,
          nodeRegistry: nodeRegistry,
          valueTypeRegistry: valueTypeRegistry,
          entity: entity,
          world: world
        });
        
        expect(visualScriptEngine).toBeDefined();
      }).not.toThrow();
    });
  });

  describe('类型安全验证', () => {
    test('GraphJSON 应该要求必需的属性', () => {
      // 这个测试确保 TypeScript 编译器会捕获缺少必需属性的错误
      
      // 正确的对象应该编译通过
      const validGraph: GraphJSON = {
        nodes: [],
        variables: [],
        customEvents: []
      };
      
      expect(validGraph).toBeDefined();
      
      // 注意：以下代码如果取消注释会导致 TypeScript 编译错误
      // 这证明了类型检查正在工作
      
      // const invalidGraph: GraphJSON = {
      //   // 缺少 nodes 属性 - 应该导致编译错误
      //   variables: [],
      //   customEvents: []
      // };
    });

    test('VisualScriptEngineOptions 应该要求所有必需的属性', () => {
      const mockScript: GraphJSON = {
        nodes: [],
        variables: [],
        customEvents: []
      };

      const engine = new Engine();
      const world = new World(engine);
      const entity = new Entity();
      const nodeRegistry = new NodeRegistry();
      const valueTypeRegistry = new ValueTypeRegistry();

      // 正确的选项对象应该编译通过
      const validOptions: VisualScriptEngineOptions = {
        script: mockScript,
        nodeRegistry: nodeRegistry,
        valueTypeRegistry: valueTypeRegistry,
        entity: entity,
        world: world
      };
      
      expect(validOptions).toBeDefined();
      
      // 注意：以下代码如果取消注释会导致 TypeScript 编译错误
      // 这证明了类型检查正在工作
      
      // const invalidOptions: VisualScriptEngineOptions = {
      //   script: mockScript,
      //   nodeRegistry: nodeRegistry,
      //   valueTypeRegistry: valueTypeRegistry,
      //   entity: entity
      //   // 缺少 world 属性 - 应该导致编译错误
      // };
    });
  });
});
