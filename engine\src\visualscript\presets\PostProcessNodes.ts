/**
 * 后处理系统节点
 * 提供后处理效果控制功能
 */
import * as THREE from 'three';
import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory, SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 启用FXAA抗锯齿节点
 */
export class EnableFXAANode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 渲染器输入
    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'Renderer',
      direction: SocketDirection.INPUT,
      description: '渲染器',
      optional: false
    });

    // 启用状态输入
    this.addInput({
      name: 'enabled',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '启用',
      defaultValue: true
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 渲染器输出
    this.addOutput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'Renderer',
      direction: SocketDirection.OUTPUT,
      description: '渲染器'
    });
  }

  protected async executeFlow(): Promise<void> {
    const renderer = this.getInputValue('renderer');
    const enabled = this.getInputValue('enabled') !== false;

    if (!renderer) {
      throw new Error('渲染器参数不能为空');
    }

    try {
      // 启用或禁用FXAA抗锯齿
      if (renderer.postProcessing) {
        renderer.postProcessing.fxaa = enabled;
      } else {
        // 如果没有后处理管理器，创建一个简单的标记
        renderer.postProcessing = { fxaa: enabled };
      }

      // 输出渲染器
      this.setOutputValue('renderer', renderer);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('启用FXAA抗锯齿失败:', error);
      throw error;
    }
  }
}

/**
 * 启用SSAO环境光遮蔽节点
 */
export class EnableSSAONode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 渲染器输入
    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'Renderer',
      direction: SocketDirection.INPUT,
      description: '渲染器',
      optional: false
    });

    // 启用状态输入
    this.addInput({
      name: 'enabled',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '启用',
      defaultValue: true
    });

    // 强度输入
    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '强度',
      defaultValue: 0.5
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 渲染器输出
    this.addOutput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'Renderer',
      direction: SocketDirection.OUTPUT,
      description: '渲染器'
    });
  }

  protected async executeFlow(): Promise<void> {
    const renderer = this.getInputValue('renderer');
    const enabled = this.getInputValue('enabled') !== false;
    const intensity = this.getInputValue('intensity') || 0.5;

    if (!renderer) {
      throw new Error('渲染器参数不能为空');
    }

    if (typeof intensity !== 'number' || intensity < 0 || intensity > 1) {
      throw new Error('强度必须在0-1之间');
    }

    try {
      // 启用或禁用SSAO环境光遮蔽
      if (renderer.postProcessing) {
        renderer.postProcessing.ssao = enabled;
        renderer.postProcessing.ssaoIntensity = intensity;
      } else {
        renderer.postProcessing = { 
          ssao: enabled,
          ssaoIntensity: intensity
        };
      }

      // 输出渲染器
      this.setOutputValue('renderer', renderer);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('启用SSAO环境光遮蔽失败:', error);
      throw error;
    }
  }
}

/**
 * 启用辉光效果节点
 */
export class EnableBloomNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 渲染器输入
    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'Renderer',
      direction: SocketDirection.INPUT,
      description: '渲染器',
      optional: false
    });

    // 启用状态输入
    this.addInput({
      name: 'enabled',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '启用',
      defaultValue: true
    });

    // 强度输入
    this.addInput({
      name: 'strength',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '强度',
      defaultValue: 1.5
    });

    // 半径输入
    this.addInput({
      name: 'radius',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '半径',
      defaultValue: 0.4
    });

    // 阈值输入
    this.addInput({
      name: 'threshold',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '阈值',
      defaultValue: 0.85
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 渲染器输出
    this.addOutput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'Renderer',
      direction: SocketDirection.OUTPUT,
      description: '渲染器'
    });
  }

  protected async executeFlow(): Promise<void> {
    const renderer = this.getInputValue('renderer');
    const enabled = this.getInputValue('enabled') !== false;
    const strength = this.getInputValue('strength') || 1.5;
    const radius = this.getInputValue('radius') || 0.4;
    const threshold = this.getInputValue('threshold') || 0.85;

    if (!renderer) {
      throw new Error('渲染器参数不能为空');
    }

    try {
      // 启用或禁用辉光效果
      if (renderer.postProcessing) {
        renderer.postProcessing.bloom = enabled;
        renderer.postProcessing.bloomStrength = strength;
        renderer.postProcessing.bloomRadius = radius;
        renderer.postProcessing.bloomThreshold = threshold;
      } else {
        renderer.postProcessing = { 
          bloom: enabled,
          bloomStrength: strength,
          bloomRadius: radius,
          bloomThreshold: threshold
        };
      }

      // 输出渲染器
      this.setOutputValue('renderer', renderer);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('启用辉光效果失败:', error);
      throw error;
    }
  }
}
