/**
 * 载具控制器
 * 基于物理系统的简单载具控制器
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Component } from '../../core/Component';
import { PhysicsBodyComponent } from '../components/PhysicsBodyComponent';
import { Vector3 } from '../../math/Vector3';

/**
 * 载具配置
 */
export interface VehicleConfig {
  /** 最大引擎力 */
  maxEngineForce?: number;
  /** 最大制动力 */
  maxBrakeForce?: number;
  /** 最大转向角度（弧度） */
  maxSteeringAngle?: number;
  /** 车轮摩擦力 */
  wheelFriction?: number;
  /** 车轮半径 */
  wheelRadius?: number;
  /** 悬挂刚度 */
  suspensionStiffness?: number;
  /** 悬挂阻尼 */
  suspensionDamping?: number;
  /** 悬挂休息长度 */
  suspensionRestLength?: number;
  /** 底盘质量 */
  chassisMass?: number;
  /** 车轮质量 */
  wheelMass?: number;
}

/**
 * 载具控制器组件
 */
export class VehicleController extends Component {
  /** 组件类型 */
  public static readonly type: string = 'VehicleController';

  /** 载具配置 */
  private config: Required<VehicleConfig>;

  /** 当前引擎力 */
  private currentEngineForce: number = 0;

  /** 当前制动力 */
  private currentBrakeForce: number = 0;

  /** 当前转向值 */
  private currentSteeringValue: number = 0;

  /** 底盘物理体 */
  private chassisBody: PhysicsBodyComponent | null = null;

  /** 车轮实体列表 */
  private wheels: Entity[] = [];

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: Required<VehicleConfig> = {
    maxEngineForce: 1000,
    maxBrakeForce: 100,
    maxSteeringAngle: Math.PI / 6, // 30度
    wheelFriction: 30,
    wheelRadius: 0.4,
    suspensionStiffness: 30,
    suspensionDamping: 4.4,
    suspensionRestLength: 0.3,
    chassisMass: 800,
    wheelMass: 30
  };

  /**
   * 创建载具控制器
   * @param entity 实体
   * @param config 载具配置
   */
  constructor(entity: Entity, config: VehicleConfig = {}) {
    super(VehicleController.type);
    this.entity = entity;

    // 合并配置
    this.config = { ...VehicleController.DEFAULT_CONFIG, ...config };
  }

  /**
   * 初始化载具控制器
   */
  public initialize(): void {
    if (this.initialized || !this.entity) return;

    // 获取底盘物理体
    this.chassisBody = this.entity.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;
    if (!this.chassisBody) {
      console.warn('VehicleController: 实体没有物理体组件');
      return;
    }

    // 设置底盘质量
    this.chassisBody.mass = this.config.chassisMass;

    this.initialized = true;
  }

  /**
   * 设置引擎力
   * @param force 引擎力（-1到1之间）
   */
  public setEngineForce(force: number): void {
    if (!this.initialized) return;

    // 限制引擎力范围
    force = Math.max(-1, Math.min(1, force));
    this.currentEngineForce = force * this.config.maxEngineForce;

    // 应用引擎力
    this.applyEngineForce();
  }

  /**
   * 设置制动力
   * @param force 制动力（0到1之间）
   */
  public setBrakeForce(force: number): void {
    if (!this.initialized) return;

    // 限制制动力范围
    force = Math.max(0, Math.min(1, force));
    this.currentBrakeForce = force * this.config.maxBrakeForce;

    // 应用制动力
    this.applyBrakeForce();
  }

  /**
   * 设置转向值
   * @param value 转向值（-1到1之间）
   */
  public setSteeringValue(value: number): void {
    if (!this.initialized) return;

    // 限制转向值范围
    value = Math.max(-1, Math.min(1, value));
    this.currentSteeringValue = value * this.config.maxSteeringAngle;

    // 应用转向
    this.applySteering();
  }

  /**
   * 获取当前引擎力
   * @returns 引擎力
   */
  public getEngineForce(): number {
    return this.currentEngineForce;
  }

  /**
   * 获取当前制动力
   * @returns 制动力
   */
  public getBrakeForce(): number {
    return this.currentBrakeForce;
  }

  /**
   * 获取当前转向值
   * @returns 转向值
   */
  public getSteeringValue(): number {
    return this.currentSteeringValue;
  }

  /**
   * 获取载具配置
   * @returns 载具配置
   */
  public getConfig(): Required<VehicleConfig> {
    return { ...this.config };
  }

  /**
   * 添加车轮
   * @param wheel 车轮实体
   */
  public addWheel(wheel: Entity): void {
    this.wheels.push(wheel);
  }

  /**
   * 移除车轮
   * @param wheel 车轮实体
   */
  public removeWheel(wheel: Entity): void {
    const index = this.wheels.indexOf(wheel);
    if (index !== -1) {
      this.wheels.splice(index, 1);
    }
  }

  /**
   * 获取车轮列表
   * @returns 车轮实体列表
   */
  public getWheels(): Entity[] {
    return [...this.wheels];
  }

  /**
   * 应用引擎力
   * @private
   */
  private applyEngineForce(): void {
    if (!this.chassisBody) return;

    // 获取载具前进方向
    const transform = this.entity.getTransform();
    const forward = new THREE.Vector3(0, 0, 1);
    const rotation = transform.getRotation();

    // 转换为四元数
    const quaternion = new THREE.Quaternion();
    if (rotation && typeof rotation.x === 'number') {
      quaternion.setFromEuler(rotation as any);
    }
    forward.applyQuaternion(quaternion);

    // 应用引擎力
    const force = new THREE.Vector3(
      forward.x * this.currentEngineForce,
      0,
      forward.z * this.currentEngineForce
    );

    // 应用引擎力
    this.chassisBody.applyForce(force);
  }

  /**
   * 应用制动力
   * @private
   */
  private applyBrakeForce(): void {
    if (!this.chassisBody) return;

    // 获取当前速度
    const velocity = this.chassisBody.getLinearVelocity();
    
    // 计算制动力方向（与速度相反）
    const brakeDirection = new THREE.Vector3(-velocity.x, 0, -velocity.z);
    brakeDirection.normalize();

    // 应用制动力
    const brakeForce = new THREE.Vector3(
      brakeDirection.x * this.currentBrakeForce,
      0,
      brakeDirection.z * this.currentBrakeForce
    );

    // 应用制动力
    this.chassisBody.applyForce(brakeForce);
  }

  /**
   * 应用转向
   * @private
   */
  private applySteering(): void {
    if (!this.chassisBody) return;

    // 获取当前速度
    const velocity = this.chassisBody.getLinearVelocity();
    const speed = Math.sqrt(velocity.x * velocity.x + velocity.z * velocity.z);

    // 只有在移动时才应用转向
    if (speed > 0.1) {
      // 计算转向扭矩
      const torque = new THREE.Vector3(0, this.currentSteeringValue * speed * 0.1, 0);

      // 应用转向扭矩
      this.chassisBody.applyTorque(torque);
    }
  }

  /**
   * 更新载具控制器
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    if (!this.initialized) return;

    // 这里可以添加载具的持续更新逻辑
    // 例如：悬挂系统更新、车轮旋转等
  }

  /**
   * 销毁载具控制器
   */
  public dispose(): void {
    this.wheels.length = 0;
    this.chassisBody = null;
    this.initialized = false;
    super.dispose();
  }
}
