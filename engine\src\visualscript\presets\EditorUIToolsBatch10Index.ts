/**
 * 第10批次编辑器UI与工具系统节点统一导出
 * 节点271-300：完整的30个节点实现
 */

// 场景编辑节点 (271-280)
export {
  UngroupEntitiesNode,
  SetEntityParentNode,
  MoveEntityNode,
  RotateEntityNode,
  ScaleEntityNode,
  HideEntityNode,
  ShowEntityNode,
  LockEntityNode,
  UnlockEntityNode,
  FocusOnEntityNode
} from './EditorUIToolsBatch10';

// UI编辑节点 (281-300)
export {
  CreateUIElementNode,
  DeleteUIElementNode,
  SetUIPositionNode,
  SetUISizeNode,
  SetUITextNode,
  SetUIColorNode,
  SetUIFontNode,
  SetUIImageNode,
  AddUIEventNode,
  RemoveUIEventNode,
  SetUIVisibleNode,
  SetUIEnabledNode,
  SetUILayerNode,
  AlignUIElementsNode,
  DistributeUIElementsNode,
  EnableGizmoNode,
  SetGizmoModeNode,
  EnableGridNode,
  SetGridSizeNode,
  EnableSnapNode
} from './EditorUIToolsBatch10';

/**
 * 第10批次节点映射表
 * 用于节点注册和类型检查
 */
export const BATCH10_NODE_MAPPING = {
  // 场景编辑节点 (271-280)
  'editor/scene/ungroupEntities': 'UngroupEntitiesNode',
  'editor/scene/setEntityParent': 'SetEntityParentNode',
  'editor/scene/moveEntity': 'MoveEntityNode',
  'editor/scene/rotateEntity': 'RotateEntityNode',
  'editor/scene/scaleEntity': 'ScaleEntityNode',
  'editor/scene/hideEntity': 'HideEntityNode',
  'editor/scene/showEntity': 'ShowEntityNode',
  'editor/scene/lockEntity': 'LockEntityNode',
  'editor/scene/unlockEntity': 'UnlockEntityNode',
  'editor/scene/focusOnEntity': 'FocusOnEntityNode',

  // UI编辑节点 (281-295)
  'editor/ui/createUIElement': 'CreateUIElementNode',
  'editor/ui/deleteUIElement': 'DeleteUIElementNode',
  'editor/ui/setUIPosition': 'SetUIPositionNode',
  'editor/ui/setUISize': 'SetUISizeNode',
  'editor/ui/setUIText': 'SetUITextNode',
  'editor/ui/setUIColor': 'SetUIColorNode',
  'editor/ui/setUIFont': 'SetUIFontNode',
  'editor/ui/setUIImage': 'SetUIImageNode',
  'editor/ui/addUIEvent': 'AddUIEventNode',
  'editor/ui/removeUIEvent': 'RemoveUIEventNode',
  'editor/ui/setUIVisible': 'SetUIVisibleNode',
  'editor/ui/setUIEnabled': 'SetUIEnabledNode',
  'editor/ui/setUILayer': 'SetUILayerNode',
  'editor/ui/alignUIElements': 'AlignUIElementsNode',
  'editor/ui/distributeUIElements': 'DistributeUIElementsNode',

  // 工具和辅助节点 (296-300)
  'editor/tools/enableGizmo': 'EnableGizmoNode',
  'editor/tools/setGizmoMode': 'SetGizmoModeNode',
  'editor/tools/enableGrid': 'EnableGridNode',
  'editor/tools/setGridSize': 'SetGridSizeNode',
  'editor/tools/enableSnap': 'EnableSnapNode'
};

/**
 * 第10批次节点类别分组
 */
export const BATCH10_NODE_CATEGORIES = {
  SCENE_EDITING: [
    'editor/scene/ungroupEntities',
    'editor/scene/setEntityParent',
    'editor/scene/moveEntity',
    'editor/scene/rotateEntity',
    'editor/scene/scaleEntity',
    'editor/scene/hideEntity',
    'editor/scene/showEntity',
    'editor/scene/lockEntity',
    'editor/scene/unlockEntity',
    'editor/scene/focusOnEntity'
  ],
  UI_CREATION: [
    'editor/ui/createUIElement',
    'editor/ui/deleteUIElement'
  ],
  UI_LAYOUT: [
    'editor/ui/setUIPosition',
    'editor/ui/setUISize',
    'editor/ui/setUILayer',
    'editor/ui/alignUIElements',
    'editor/ui/distributeUIElements'
  ],
  UI_STYLING: [
    'editor/ui/setUIColor',
    'editor/ui/setUIFont',
    'editor/ui/setUIImage'
  ],
  UI_CONTENT: [
    'editor/ui/setUIText'
  ],
  UI_INTERACTION: [
    'editor/ui/addUIEvent',
    'editor/ui/removeUIEvent'
  ],
  UI_CONTROL: [
    'editor/ui/setUIVisible',
    'editor/ui/setUIEnabled'
  ],
  EDITING_TOOLS: [
    'editor/tools/enableGizmo',
    'editor/tools/setGizmoMode',
    'editor/tools/enableGrid',
    'editor/tools/setGridSize',
    'editor/tools/enableSnap'
  ]
};

/**
 * 获取第10批次所有节点类型
 */
export function getBatch10NodeTypes(): string[] {
  return Object.keys(BATCH10_NODE_MAPPING);
}

/**
 * 获取第10批次节点总数
 */
export function getBatch10NodeCount(): number {
  return Object.keys(BATCH10_NODE_MAPPING).length;
}

/**
 * 验证第10批次节点完整性
 */
export function validateBatch10Nodes(): boolean {
  const expectedCount = 30;
  const actualCount = getBatch10NodeCount();
  
  if (actualCount !== expectedCount) {
    console.error(`第10批次节点数量不匹配: 期望 ${expectedCount}, 实际 ${actualCount}`);
    return false;
  }
  
  console.log(`第10批次节点验证通过: ${actualCount} 个节点`);
  return true;
}

/**
 * 第10批次节点描述信息
 */
export const BATCH10_NODE_DESCRIPTIONS = {
  'editor/scene/ungroupEntities': '取消实体组合',
  'editor/scene/setEntityParent': '设置实体的父子关系',
  'editor/scene/moveEntity': '移动实体位置',
  'editor/scene/rotateEntity': '旋转实体角度',
  'editor/scene/scaleEntity': '缩放实体大小',
  'editor/scene/hideEntity': '隐藏场景实体',
  'editor/scene/showEntity': '显示场景实体',
  'editor/scene/lockEntity': '锁定实体编辑',
  'editor/scene/unlockEntity': '解锁实体编辑',
  'editor/scene/focusOnEntity': '相机聚焦到实体',
  'editor/ui/createUIElement': '创建用户界面元素',
  'editor/ui/deleteUIElement': '删除用户界面元素',
  'editor/ui/setUIPosition': '设置UI元素位置',
  'editor/ui/setUISize': '设置UI元素尺寸',
  'editor/ui/setUIText': '设置UI元素文本内容',
  'editor/ui/setUIColor': '设置UI元素颜色',
  'editor/ui/setUIFont': '设置UI元素字体',
  'editor/ui/setUIImage': '设置UI元素背景图像',
  'editor/ui/addUIEvent': '为UI元素添加事件',
  'editor/ui/removeUIEvent': '移除UI元素事件',
  'editor/ui/setUIVisible': '设置UI元素显示状态',
  'editor/ui/setUIEnabled': '设置UI元素交互状态',
  'editor/ui/setUILayer': '设置UI元素渲染层级',
  'editor/ui/alignUIElements': '对齐多个UI元素',
  'editor/ui/distributeUIElements': '均匀分布UI元素',
  'editor/tools/enableGizmo': '启用3D操作手柄',
  'editor/tools/setGizmoMode': '设置操作手柄模式',
  'editor/tools/enableGrid': '启用场景网格显示',
  'editor/tools/setGridSize': '设置网格间距',
  'editor/tools/enableSnap': '启用对象吸附功能'
};
