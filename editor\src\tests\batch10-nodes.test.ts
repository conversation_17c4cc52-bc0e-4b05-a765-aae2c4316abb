/**
 * 第10批次节点功能测试
 * 测试编辑器UI与工具系统节点的基本功能
 */

import { nodeRegistryService } from '../services/NodeRegistryService';

describe('第10批次节点测试 - 编辑器UI与工具系统（节点271-300）', () => {
  beforeAll(() => {
    // 确保节点注册服务已初始化
    expect(nodeRegistryService).toBeDefined();
  });

  describe('节点注册测试', () => {
    test('应该注册所有场景编辑节点 (271-280)', () => {
      const sceneEditingNodes = [
        'editor/scene/ungroupEntities',
        'editor/scene/setEntityParent',
        'editor/scene/moveEntity',
        'editor/scene/rotateEntity',
        'editor/scene/scaleEntity',
        'editor/scene/hideEntity',
        'editor/scene/showEntity',
        'editor/scene/lockEntity',
        'editor/scene/unlockEntity',
        'editor/scene/focusOnEntity'
      ];

      sceneEditingNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.type).toBe(nodeType);
      });
    });

    test('应该注册所有UI编辑节点 (281-295)', () => {
      const uiEditingNodes = [
        'editor/ui/createUIElement',
        'editor/ui/deleteUIElement',
        'editor/ui/setUIPosition',
        'editor/ui/setUISize',
        'editor/ui/setUIText',
        'editor/ui/setUIColor',
        'editor/ui/setUIFont',
        'editor/ui/setUIImage',
        'editor/ui/addUIEvent',
        'editor/ui/removeUIEvent',
        'editor/ui/setUIVisible',
        'editor/ui/setUIEnabled',
        'editor/ui/setUILayer',
        'editor/ui/alignUIElements',
        'editor/ui/distributeUIElements'
      ];

      uiEditingNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.type).toBe(nodeType);
      });
    });

    test('应该注册所有工具辅助节点 (296-300)', () => {
      const toolNodes = [
        'editor/tools/enableGizmo',
        'editor/tools/setGizmoMode',
        'editor/tools/enableGrid',
        'editor/tools/setGridSize',
        'editor/tools/enableSnap'
      ];

      toolNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.type).toBe(nodeType);
      });
    });

    test('应该注册总共30个第10批次节点', () => {
      const batch10Nodes = [
        // 场景编辑节点 (271-280)
        'editor/scene/ungroupEntities',
        'editor/scene/setEntityParent',
        'editor/scene/moveEntity',
        'editor/scene/rotateEntity',
        'editor/scene/scaleEntity',
        'editor/scene/hideEntity',
        'editor/scene/showEntity',
        'editor/scene/lockEntity',
        'editor/scene/unlockEntity',
        'editor/scene/focusOnEntity',
        // UI编辑节点 (281-295)
        'editor/ui/createUIElement',
        'editor/ui/deleteUIElement',
        'editor/ui/setUIPosition',
        'editor/ui/setUISize',
        'editor/ui/setUIText',
        'editor/ui/setUIColor',
        'editor/ui/setUIFont',
        'editor/ui/setUIImage',
        'editor/ui/addUIEvent',
        'editor/ui/removeUIEvent',
        'editor/ui/setUIVisible',
        'editor/ui/setUIEnabled',
        'editor/ui/setUILayer',
        'editor/ui/alignUIElements',
        'editor/ui/distributeUIElements',
        // 工具辅助节点 (296-300)
        'editor/tools/enableGizmo',
        'editor/tools/setGizmoMode',
        'editor/tools/enableGrid',
        'editor/tools/setGridSize',
        'editor/tools/enableSnap'
      ];

      expect(batch10Nodes).toHaveLength(30);

      batch10Nodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.type).toBe(nodeType);
      });
    });
  });

  describe('节点属性测试', () => {
    test('场景编辑节点应该有正确的属性', () => {
      const node = nodeRegistryService.getNode('editor/scene/moveEntity');
      expect(node).toBeDefined();
      expect(node?.label).toBe('移动实体');
      expect(node?.description).toBe('移动实体位置');
      expect(node?.tags).toContain('编辑器');
      expect(node?.tags).toContain('场景');
    });

    test('UI编辑节点应该有正确的属性', () => {
      const node = nodeRegistryService.getNode('editor/ui/createUIElement');
      expect(node).toBeDefined();
      expect(node?.label).toBe('创建UI元素');
      expect(node?.description).toBe('创建用户界面元素');
      expect(node?.tags).toContain('编辑器');
      expect(node?.tags).toContain('UI');
    });

    test('工具辅助节点应该有正确的属性', () => {
      const node = nodeRegistryService.getNode('editor/tools/enableGizmo');
      expect(node).toBeDefined();
      expect(node?.label).toBe('启用操作手柄');
      expect(node?.description).toBe('启用3D操作手柄');
      expect(node?.tags).toContain('编辑器');
      expect(node?.tags).toContain('工具');
    });
  });

  describe('节点分类测试', () => {
    test('场景编辑节点应该属于正确的分类', () => {
      const sceneNodes = [
        'editor/scene/moveEntity',
        'editor/scene/rotateEntity',
        'editor/scene/scaleEntity'
      ];

      sceneNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.category).toBe('ENTITY');
      });
    });

    test('UI编辑节点应该属于正确的分类', () => {
      const uiNodes = [
        'editor/ui/createUIElement',
        'editor/ui/setUIPosition',
        'editor/ui/setUIColor'
      ];

      uiNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.category).toBe('CUSTOM');
      });
    });

    test('工具节点应该属于正确的分类', () => {
      const toolNodes = [
        'editor/tools/enableGizmo',
        'editor/tools/enableGrid',
        'editor/tools/enableSnap'
      ];

      toolNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.category).toBe('CUSTOM');
      });
    });
  });

  describe('节点搜索测试', () => {
    test('应该能够通过标签搜索节点', () => {
      const sceneNodes = nodeRegistryService.getNodesByTag('场景');
      expect(sceneNodes.length).toBeGreaterThan(0);
      
      const uiNodes = nodeRegistryService.getNodesByTag('UI');
      expect(uiNodes.length).toBeGreaterThan(0);
      
      const toolNodes = nodeRegistryService.getNodesByTag('工具');
      expect(toolNodes.length).toBeGreaterThan(0);
    });

    test('应该能够获取所有第10批次节点', () => {
      const allNodes = nodeRegistryService.getAllNodes();
      const batch10NodeTypes = [
        'editor/scene/moveEntity',
        'editor/ui/createUIElement',
        'editor/tools/enableGizmo'
      ];

      batch10NodeTypes.forEach(nodeType => {
        const found = allNodes.some(node => node.type === nodeType);
        expect(found).toBeTruthy();
      });
    });
  });

  describe('节点完整性测试', () => {
    test('所有第10批次节点都应该有必需的属性', () => {
      const batch10NodeTypes = [
        'editor/scene/ungroupEntities',
        'editor/scene/setEntityParent',
        'editor/scene/moveEntity',
        'editor/scene/rotateEntity',
        'editor/scene/scaleEntity',
        'editor/scene/hideEntity',
        'editor/scene/showEntity',
        'editor/scene/lockEntity',
        'editor/scene/unlockEntity',
        'editor/scene/focusOnEntity',
        'editor/ui/createUIElement',
        'editor/ui/deleteUIElement',
        'editor/ui/setUIPosition',
        'editor/ui/setUISize',
        'editor/ui/setUIText',
        'editor/ui/setUIColor',
        'editor/ui/setUIFont',
        'editor/ui/setUIImage',
        'editor/ui/addUIEvent',
        'editor/ui/removeUIEvent',
        'editor/ui/setUIVisible',
        'editor/ui/setUIEnabled',
        'editor/ui/setUILayer',
        'editor/ui/alignUIElements',
        'editor/ui/distributeUIElements',
        'editor/tools/enableGizmo',
        'editor/tools/setGizmoMode',
        'editor/tools/enableGrid',
        'editor/tools/setGridSize',
        'editor/tools/enableSnap'
      ];

      batch10NodeTypes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.type).toBeTruthy();
        expect(node?.label).toBeTruthy();
        expect(node?.description).toBeTruthy();
        expect(node?.category).toBeTruthy();
        expect(node?.icon).toBeTruthy();
        expect(node?.color).toBeTruthy();
        expect(Array.isArray(node?.tags)).toBeTruthy();
        expect(node?.tags?.length).toBeGreaterThan(0);
      });
    });
  });
});
