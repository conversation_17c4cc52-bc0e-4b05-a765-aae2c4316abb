/**
 * 物理刚体系统节点
 * 提供刚体创建和控制功能
 */
import * as THREE from 'three';
import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory, SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 创建刚体节点
 */
export class CreateRigidBodyNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '实体',
      optional: false
    });

    // 刚体类型输入
    this.addInput({
      name: 'type',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '类型',
      defaultValue: 'dynamic'
    });

    // 形状输入
    this.addInput({
      name: 'shape',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '形状',
      defaultValue: 'box'
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 刚体输出
    this.addOutput({
      name: 'rigidBody',
      type: SocketType.DATA,
      dataType: 'RigidBody',
      direction: SocketDirection.OUTPUT,
      description: '刚体'
    });
  }

  protected async executeFlow(): Promise<void> {
    const entity = this.getInputValue('entity');
    const type = this.getInputValue('type') || 'dynamic';
    const shape = this.getInputValue('shape') || 'box';

    if (!entity) {
      throw new Error('实体参数不能为空');
    }

    try {
      // 创建刚体
      const rigidBody = {
        entity: entity,
        type: type,
        shape: shape,
        mass: 1,
        friction: 0.5,
        restitution: 0.3,
        isKinematic: type === 'kinematic',
        isStatic: type === 'static'
      };

      // 输出刚体
      this.setOutputValue('rigidBody', rigidBody);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('创建刚体失败:', error);
      throw error;
    }
  }
}

/**
 * 设置质量节点
 */
export class SetMassNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 刚体输入
    this.addInput({
      name: 'rigidBody',
      type: SocketType.DATA,
      dataType: 'RigidBody',
      direction: SocketDirection.INPUT,
      description: '刚体',
      optional: false
    });

    // 质量输入
    this.addInput({
      name: 'mass',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '质量',
      optional: false,
      defaultValue: 1
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 刚体输出
    this.addOutput({
      name: 'rigidBody',
      type: SocketType.DATA,
      dataType: 'RigidBody',
      direction: SocketDirection.OUTPUT,
      description: '刚体'
    });
  }

  protected async executeFlow(): Promise<void> {
    const rigidBody = this.getInputValue('rigidBody');
    const mass = this.getInputValue('mass');

    if (!rigidBody) {
      throw new Error('刚体参数不能为空');
    }

    if (typeof mass !== 'number' || mass < 0) {
      throw new Error('质量必须是非负数');
    }

    try {
      // 设置质量
      rigidBody.mass = mass;

      // 输出刚体
      this.setOutputValue('rigidBody', rigidBody);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('设置质量失败:', error);
      throw error;
    }
  }
}

/**
 * 设置摩擦力节点
 */
export class SetFrictionNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 刚体输入
    this.addInput({
      name: 'rigidBody',
      type: SocketType.DATA,
      dataType: 'RigidBody',
      direction: SocketDirection.INPUT,
      description: '刚体',
      optional: false
    });

    // 摩擦力输入
    this.addInput({
      name: 'friction',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '摩擦力',
      optional: false,
      defaultValue: 0.5
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 刚体输出
    this.addOutput({
      name: 'rigidBody',
      type: SocketType.DATA,
      dataType: 'RigidBody',
      direction: SocketDirection.OUTPUT,
      description: '刚体'
    });
  }

  protected async executeFlow(): Promise<void> {
    const rigidBody = this.getInputValue('rigidBody');
    const friction = this.getInputValue('friction');

    if (!rigidBody) {
      throw new Error('刚体参数不能为空');
    }

    if (typeof friction !== 'number' || friction < 0) {
      throw new Error('摩擦力必须是非负数');
    }

    try {
      // 设置摩擦力
      rigidBody.friction = friction;

      // 输出刚体
      this.setOutputValue('rigidBody', rigidBody);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('设置摩擦力失败:', error);
      throw error;
    }
  }
}

/**
 * 设置弹性节点
 */
export class SetRestitutionNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 刚体输入
    this.addInput({
      name: 'rigidBody',
      type: SocketType.DATA,
      dataType: 'RigidBody',
      direction: SocketDirection.INPUT,
      description: '刚体',
      optional: false
    });

    // 弹性输入
    this.addInput({
      name: 'restitution',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '弹性',
      optional: false,
      defaultValue: 0.3
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 刚体输出
    this.addOutput({
      name: 'rigidBody',
      type: SocketType.DATA,
      dataType: 'RigidBody',
      direction: SocketDirection.OUTPUT,
      description: '刚体'
    });
  }

  protected async executeFlow(): Promise<void> {
    const rigidBody = this.getInputValue('rigidBody');
    const restitution = this.getInputValue('restitution');

    if (!rigidBody) {
      throw new Error('刚体参数不能为空');
    }

    if (typeof restitution !== 'number' || restitution < 0 || restitution > 1) {
      throw new Error('弹性必须在0-1之间');
    }

    try {
      // 设置弹性
      rigidBody.restitution = restitution;

      // 输出刚体
      this.setOutputValue('rigidBody', rigidBody);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('设置弹性失败:', error);
      throw error;
    }
  }
}
