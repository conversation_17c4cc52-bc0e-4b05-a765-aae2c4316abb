/**
 * 节点集成修复 - 批次1实施示例
 * 修复日期: 2025年7月11日
 * 目标: 注册001-050号核心基础节点到编辑器
 */

import { NodeRegistryService, NodeCategory } from '../services/NodeRegistryService';

/**
 * 修复批次1：核心基础节点配置 (001-050)
 */
export const BATCH1_CORE_NODES = [
  // 核心事件节点 (001-005)
  {
    id: 1,
    type: 'core/events/onStart',
    label: '开始事件',
    description: '当视觉脚本开始执行时触发',
    category: NodeCategory.EVENTS,
    icon: 'play',
    color: '#52c41a',
    tags: ['事件', '生命周期', '核心', '批次1']
  },
  {
    id: 2,
    type: 'core/events/onUpdate',
    label: '更新事件',
    description: '每帧更新时触发',
    category: NodeCategory.EVENTS,
    icon: 'sync',
    color: '#1890ff',
    tags: ['事件', '生命周期', '核心', '批次1']
  },
  {
    id: 3,
    type: 'core/events/onEnd',
    label: '结束事件',
    description: '当视觉脚本结束时触发',
    category: NodeCategory.EVENTS,
    icon: 'stop',
    color: '#f5222d',
    tags: ['事件', '生命周期', '核心', '批次1']
  },
  {
    id: 4,
    type: 'core/events/onPause',
    label: '暂停事件',
    description: '当视觉脚本暂停时触发',
    category: NodeCategory.EVENTS,
    icon: 'pause',
    color: '#fa8c16',
    tags: ['事件', '生命周期', '核心', '批次1']
  },
  {
    id: 5,
    type: 'core/events/onResume',
    label: '恢复事件',
    description: '当视觉脚本恢复时触发',
    category: NodeCategory.EVENTS,
    icon: 'play-circle',
    color: '#52c41a',
    tags: ['事件', '生命周期', '核心', '批次1']
  },

  // 数学运算节点 (006-015)
  {
    id: 6,
    type: 'math/basic/add',
    label: '加法',
    description: '计算两个数的和',
    category: NodeCategory.MATH,
    icon: 'plus',
    color: '#fa8c16',
    tags: ['数学', '运算', '基础', '批次1']
  },
  {
    id: 7,
    type: 'math/basic/subtract',
    label: '减法',
    description: '计算两个数的差',
    category: NodeCategory.MATH,
    icon: 'minus',
    color: '#fa8c16',
    tags: ['数学', '运算', '基础', '批次1']
  },
  {
    id: 8,
    type: 'math/basic/multiply',
    label: '乘法',
    description: '计算两个数的积',
    category: NodeCategory.MATH,
    icon: 'close',
    color: '#fa8c16',
    tags: ['数学', '运算', '基础', '批次1']
  },
  {
    id: 9,
    type: 'math/basic/divide',
    label: '除法',
    description: '计算两个数的商',
    category: NodeCategory.MATH,
    icon: 'line',
    color: '#fa8c16',
    tags: ['数学', '运算', '基础', '批次1']
  },
  {
    id: 10,
    type: 'math/basic/modulo',
    label: '取模',
    description: '计算两个数的余数',
    category: NodeCategory.MATH,
    icon: 'percentage',
    color: '#fa8c16',
    tags: ['数学', '运算', '基础', '批次1']
  },

  // 逻辑比较节点 (016-025)
  {
    id: 16,
    type: 'logic/comparison/equal',
    label: '等于',
    description: '比较两个值是否相等',
    category: NodeCategory.LOGIC,
    icon: 'equal',
    color: '#722ed1',
    tags: ['逻辑', '比较', '条件', '批次1']
  },
  {
    id: 17,
    type: 'logic/comparison/notEqual',
    label: '不等于',
    description: '比较两个值是否不相等',
    category: NodeCategory.LOGIC,
    icon: 'not-equal',
    color: '#722ed1',
    tags: ['逻辑', '比较', '条件', '批次1']
  },
  {
    id: 18,
    type: 'logic/comparison/greater',
    label: '大于',
    description: '比较第一个值是否大于第二个值',
    category: NodeCategory.LOGIC,
    icon: 'greater',
    color: '#722ed1',
    tags: ['逻辑', '比较', '条件', '批次1']
  },
  {
    id: 19,
    type: 'logic/comparison/less',
    label: '小于',
    description: '比较第一个值是否小于第二个值',
    category: NodeCategory.LOGIC,
    icon: 'less',
    color: '#722ed1',
    tags: ['逻辑', '比较', '条件', '批次1']
  },
  {
    id: 20,
    type: 'logic/boolean/and',
    label: '逻辑与',
    description: '逻辑与运算',
    category: NodeCategory.LOGIC,
    icon: 'and',
    color: '#722ed1',
    tags: ['逻辑', '布尔', '运算', '批次1']
  },

  // 流程控制节点 (026-035)
  {
    id: 26,
    type: 'core/flow/branch',
    label: '分支',
    description: '根据条件选择执行路径',
    category: NodeCategory.FLOW,
    icon: 'branch',
    color: '#eb2f96',
    tags: ['流程', '控制', '分支', '批次1']
  },
  {
    id: 27,
    type: 'core/flow/sequence',
    label: '序列',
    description: '按顺序执行多个操作',
    category: NodeCategory.FLOW,
    icon: 'sequence',
    color: '#eb2f96',
    tags: ['流程', '控制', '序列', '批次1']
  },
  {
    id: 28,
    type: 'core/flow/delay',
    label: '延迟',
    description: '延迟指定时间后执行',
    category: NodeCategory.FLOW,
    icon: 'clock',
    color: '#eb2f96',
    tags: ['流程', '时间', '延迟', '批次1']
  },
  {
    id: 29,
    type: 'core/flow/loop',
    label: '循环',
    description: '重复执行指定次数',
    category: NodeCategory.FLOW,
    icon: 'loop',
    color: '#eb2f96',
    tags: ['流程', '控制', '循环', '批次1']
  },
  {
    id: 30,
    type: 'core/flow/while',
    label: '条件循环',
    description: '当条件为真时重复执行',
    category: NodeCategory.FLOW,
    icon: 'while',
    color: '#eb2f96',
    tags: ['流程', '控制', '循环', '批次1']
  },

  // 实体操作节点 (036-045)
  {
    id: 36,
    type: 'entity/create',
    label: '创建实体',
    description: '创建新的实体对象',
    category: NodeCategory.ENTITY,
    icon: 'plus-circle',
    color: '#13c2c2',
    tags: ['实体', '创建', '对象', '批次1']
  },
  {
    id: 37,
    type: 'entity/destroy',
    label: '销毁实体',
    description: '销毁指定的实体对象',
    category: NodeCategory.ENTITY,
    icon: 'delete',
    color: '#13c2c2',
    tags: ['实体', '销毁', '对象', '批次1']
  },
  {
    id: 38,
    type: 'entity/find',
    label: '查找实体',
    description: '根据条件查找实体',
    category: NodeCategory.ENTITY,
    icon: 'search',
    color: '#13c2c2',
    tags: ['实体', '查找', '搜索', '批次1']
  },
  {
    id: 39,
    type: 'entity/component/add',
    label: '添加组件',
    description: '为实体添加组件',
    category: NodeCategory.ENTITY,
    icon: 'plus-square',
    color: '#13c2c2',
    tags: ['实体', '组件', '添加', '批次1']
  },
  {
    id: 40,
    type: 'entity/component/remove',
    label: '移除组件',
    description: '从实体移除组件',
    category: NodeCategory.ENTITY,
    icon: 'minus-square',
    color: '#13c2c2',
    tags: ['实体', '组件', '移除', '批次1']
  },

  // 基础物理节点 (046-050)
  {
    id: 46,
    type: 'physics/gravity/set',
    label: '设置重力',
    description: '设置物理世界的重力',
    category: NodeCategory.PHYSICS,
    icon: 'gravity',
    color: '#9c27b0',
    tags: ['物理', '重力', '世界', '批次1']
  },
  {
    id: 47,
    type: 'physics/collision/detect',
    label: '碰撞检测',
    description: '检测两个物体是否碰撞',
    category: NodeCategory.PHYSICS,
    icon: 'collision',
    color: '#9c27b0',
    tags: ['物理', '碰撞', '检测', '批次1']
  },
  {
    id: 48,
    type: 'physics/rigidbody/create',
    label: '创建刚体',
    description: '为实体创建刚体组件',
    category: NodeCategory.PHYSICS,
    icon: 'rigidbody',
    color: '#9c27b0',
    tags: ['物理', '刚体', '创建', '批次1']
  },
  {
    id: 49,
    type: 'physics/force/apply',
    label: '施加力',
    description: '向刚体施加力',
    category: NodeCategory.PHYSICS,
    icon: 'force',
    color: '#9c27b0',
    tags: ['物理', '力', '施加', '批次1']
  },
  {
    id: 50,
    type: 'physics/velocity/set',
    label: '设置速度',
    description: '设置刚体的速度',
    category: NodeCategory.PHYSICS,
    icon: 'velocity',
    color: '#9c27b0',
    tags: ['物理', '速度', '设置', '批次1']
  }
];

/**
 * 修复批次1实施函数
 * 将50个核心基础节点注册到编辑器
 */
export function implementBatch1CoreNodes(): void {
  console.log('🚀 开始实施修复批次1：核心基础节点 (001-050)');
  
  const nodeRegistry = NodeRegistryService.getInstance();
  let successCount = 0;
  let failCount = 0;

  BATCH1_CORE_NODES.forEach((nodeConfig, index) => {
    try {
      nodeRegistry.registerNode({
        type: nodeConfig.type,
        label: nodeConfig.label,
        description: nodeConfig.description,
        category: nodeConfig.category,
        icon: nodeConfig.icon,
        color: nodeConfig.color,
        tags: nodeConfig.tags
      });
      
      successCount++;
      console.log(`✅ ${nodeConfig.id.toString().padStart(3, '0')}. ${nodeConfig.label} - 注册成功`);
    } catch (error) {
      failCount++;
      console.error(`❌ ${nodeConfig.id.toString().padStart(3, '0')}. ${nodeConfig.label} - 注册失败:`, error);
    }
  });

  console.log('\n📊 修复批次1执行结果:');
  console.log(`✅ 成功注册: ${successCount}个节点`);
  console.log(`❌ 注册失败: ${failCount}个节点`);
  console.log(`📈 成功率: ${((successCount / BATCH1_CORE_NODES.length) * 100).toFixed(1)}%`);
  
  if (successCount === BATCH1_CORE_NODES.length) {
    console.log('🎉 修复批次1完成！所有50个核心基础节点已成功注册到编辑器');
    console.log('👉 用户现在可以拖拽使用这些节点进行应用开发');
  } else {
    console.warn('⚠️ 修复批次1未完全成功，请检查失败的节点');
  }

  // 验证注册结果
  const totalNodes = nodeRegistry.getAllNodes().length;
  console.log(`📊 编辑器当前节点总数: ${totalNodes}个`);
}

/**
 * 验证批次1节点功能
 */
export function validateBatch1Nodes(): boolean {
  console.log('🧪 开始验证修复批次1节点功能...');
  
  const nodeRegistry = NodeRegistryService.getInstance();
  let validationPassed = true;

  // 验证节点数量
  const batch1Nodes = nodeRegistry.getNodesByTag('批次1');
  if (batch1Nodes.length !== 50) {
    console.error(`❌ 节点数量验证失败: 期望50个，实际${batch1Nodes.length}个`);
    validationPassed = false;
  }

  // 验证节点分类
  const categories = [
    NodeCategory.EVENTS,
    NodeCategory.MATH,
    NodeCategory.LOGIC,
    NodeCategory.FLOW,
    NodeCategory.ENTITY,
    NodeCategory.PHYSICS
  ];

  categories.forEach(category => {
    const categoryNodes = nodeRegistry.getNodesByCategory(category);
    const batch1CategoryNodes = categoryNodes.filter(node => 
      node.tags.includes('批次1')
    );
    
    if (batch1CategoryNodes.length === 0) {
      console.error(`❌ 分类验证失败: ${category} 分类下没有批次1节点`);
      validationPassed = false;
    } else {
      console.log(`✅ ${category}: ${batch1CategoryNodes.length}个节点`);
    }
  });

  if (validationPassed) {
    console.log('🎉 修复批次1验证通过！所有节点功能正常');
  } else {
    console.error('❌ 修复批次1验证失败，请检查问题');
  }

  return validationPassed;
}
