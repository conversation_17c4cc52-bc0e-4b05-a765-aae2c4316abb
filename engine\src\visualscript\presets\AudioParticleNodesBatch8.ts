/**
 * 音频与粒子系统节点
 * 第8批次：音频与粒子系统（节点211-240）
 * 包含高级音频节点和粒子系统节点
 */

import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Node } from '../nodes/Node';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// 高级音频节点（211-215）- 场景环境音频
// ============================================================================

/**
 * 设置天空盒节点 (211)
 * 设置场景天空盒
 */
export class SetSkyboxNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });

    this.addInput({
      name: 'skyboxTexture',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '天空盒纹理'
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的场景'
    });
  }

  public execute(): any {
    const scene = this.getInputValue('scene');
    const skyboxTexture = this.getInputValue('skyboxTexture');

    try {
      if (scene && skyboxTexture) {
        scene.background = skyboxTexture;
        scene.environment = skyboxTexture;
      }
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetSkyboxNode: 设置天空盒失败', error);
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 启用雾效节点 (212)
 * 启用场景雾效果
 */
export class EnableFogNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });

    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '雾颜色',
      defaultValue: 0xcccccc
    });

    this.addInput({
      name: 'near',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '近距离',
      defaultValue: 1
    });

    this.addInput({
      name: 'far',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '远距离',
      defaultValue: 1000
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的场景'
    });
  }

  public execute(): any {
    const scene = this.getInputValue('scene');
    const color = this.getInputValue('color');
    const near = this.getInputValue('near');
    const far = this.getInputValue('far');

    try {
      if (scene) {
        scene.fog = new THREE.Fog(color, near, far);
      }
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('EnableFogNode: 启用雾效失败', error);
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置雾颜色节点 (213)
 * 设置雾的颜色
 */
export class SetFogColorNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });

    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '雾颜色',
      defaultValue: 0xcccccc
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的场景'
    });
  }

  public execute(): any {
    const scene = this.getInputValue('scene');
    const color = this.getInputValue('color');

    try {
      if (scene && scene.fog) {
        scene.fog.color.setHex(color);
      }
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetFogColorNode: 设置雾颜色失败', error);
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置雾密度节点 (214)
 * 设置雾的浓度
 */
export class SetFogDensityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });

    this.addInput({
      name: 'density',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '雾密度',
      defaultValue: 0.01
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的场景'
    });
  }

  public execute(): any {
    const scene = this.getInputValue('scene');
    const density = this.getInputValue('density');

    try {
      if (scene) {
        // 创建指数雾效果
        scene.fog = new THREE.FogExp2(scene.fog?.color?.getHex() || 0xcccccc, density);
      }
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetFogDensityNode: 设置雾密度失败', error);
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置环境贴图节点 (215)
 * 设置IBL环境贴图
 */
export class SetEnvironmentMapNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });

    this.addInput({
      name: 'environmentMap',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '环境贴图'
    });

    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '环境光强度',
      defaultValue: 1.0
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的场景'
    });
  }

  public execute(): any {
    const scene = this.getInputValue('scene');
    const environmentMap = this.getInputValue('environmentMap');
    const intensity = this.getInputValue('intensity');

    try {
      if (scene && environmentMap) {
        scene.environment = environmentMap;
        scene.environmentIntensity = intensity;
      }
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetEnvironmentMapNode: 设置环境贴图失败', error);
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    }
  }
}

// ============================================================================
// 粒子系统节点（216-230）
// ============================================================================

/**
 * 创建粒子系统节点 (216)
 * 创建粒子效果系统
 */
export class CreateParticleSystemNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'maxParticles',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '最大粒子数',
      defaultValue: 1000
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '粒子系统'
    });
  }

  public execute(): any {
    const maxParticles = this.getInputValue('maxParticles');

    try {
      const geometry = new THREE.BufferGeometry();
      const positions = new Float32Array(maxParticles * 3);
      const colors = new Float32Array(maxParticles * 3);
      const sizes = new Float32Array(maxParticles);

      geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
      geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
      geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

      const material = new THREE.PointsMaterial({
        size: 1,
        vertexColors: true,
        transparent: true,
        opacity: 0.8
      });

      const particleSystem = new THREE.Points(geometry, material);
      (particleSystem as any).maxParticles = maxParticles;
      (particleSystem as any).particleCount = 0;
      (particleSystem as any).particles = [];

      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateParticleSystemNode: 创建粒子系统失败', error);
      this.setOutputValue('particleSystem', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 创建发射器节点 (217)
 * 创建粒子发射器
 */
export class CreateEmitterNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '发射器位置',
      defaultValue: new THREE.Vector3(0, 0, 0)
    });

    this.addInput({
      name: 'rate',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '发射速率',
      defaultValue: 10
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'emitter',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '粒子发射器'
    });
  }

  public execute(): any {
    const position = this.getInputValue('position');
    const rate = this.getInputValue('rate');

    try {
      const emitter = {
        position: position.clone(),
        rate: rate,
        shape: 'point',
        direction: new THREE.Vector3(0, 1, 0),
        spread: Math.PI / 6,
        speed: 5,
        lastEmitTime: 0
      };

      this.setOutputValue('emitter', emitter);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateEmitterNode: 创建发射器失败', error);
      this.setOutputValue('emitter', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置发射速率节点 (218)
 * 设置粒子发射频率
 */
export class SetEmissionRateNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'emitter',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子发射器'
    });

    this.addInput({
      name: 'rate',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '发射速率',
      defaultValue: 10
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'emitter',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的发射器'
    });
  }

  public execute(): any {
    const emitter = this.getInputValue('emitter');
    const rate = this.getInputValue('rate');

    try {
      if (emitter) {
        emitter.rate = rate;
      }
      this.setOutputValue('emitter', emitter);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetEmissionRateNode: 设置发射速率失败', error);
      this.setOutputValue('emitter', emitter);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置发射形状节点 (219)
 * 设置粒子发射形状
 */
export class SetEmissionShapeNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'emitter',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子发射器'
    });

    this.addInput({
      name: 'shape',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '发射形状',
      defaultValue: 'point'
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '形状大小',
      defaultValue: 1
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'emitter',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的发射器'
    });
  }

  public execute(): any {
    const emitter = this.getInputValue('emitter');
    const shape = this.getInputValue('shape');
    const size = this.getInputValue('size');

    try {
      if (emitter) {
        emitter.shape = shape;
        emitter.size = size;
      }
      this.setOutputValue('emitter', emitter);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetEmissionShapeNode: 设置发射形状失败', error);
      this.setOutputValue('emitter', emitter);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置粒子寿命节点 (220)
 * 设置粒子存活时间
 */
export class SetLifetimeNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'lifetime',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子寿命（秒）',
      defaultValue: 5.0
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const lifetime = this.getInputValue('lifetime');

    try {
      if (particleSystem) {
        (particleSystem as any).particleLifetime = lifetime;
      }
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetLifetimeNode: 设置粒子寿命失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置粒子速度节点 (221)
 * 设置粒子初始速度
 */
export class SetVelocityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'velocity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子速度',
      defaultValue: new THREE.Vector3(0, 1, 0)
    });

    this.addInput({
      name: 'randomness',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '随机性',
      defaultValue: 0.5
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const velocity = this.getInputValue('velocity');
    const randomness = this.getInputValue('randomness');

    try {
      if (particleSystem) {
        (particleSystem as any).particleVelocity = velocity;
        (particleSystem as any).velocityRandomness = randomness;
      }
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetVelocityNode: 设置粒子速度失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}
