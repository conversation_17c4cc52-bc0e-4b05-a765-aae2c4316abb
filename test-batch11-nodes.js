/**
 * 第11批次节点验证脚本
 * 验证服务器端功能节点（301-330）的注册和基本功能
 */

const fs = require('fs');

console.log('🔍 第11批次节点验证开始...\n');

// 验证节点实现文件
function verifyNodeImplementation() {
  console.log('=== 📁 节点实现文件验证 ===');
  
  try {
    // 检查主实现文件
    const mainFile = 'engine/src/visualscript/presets/ServerNodesBatch11.ts';
    if (fs.existsSync(mainFile)) {
      const content = fs.readFileSync(mainFile, 'utf8');
      const nodeClasses = content.match(/export class \w+Node/g) || [];
      console.log(`✅ 主实现文件存在: ${mainFile}`);
      console.log(`📦 导出节点类数量: ${nodeClasses.length}个`);
      
      // 检查索引文件
      const indexFile = 'engine/src/visualscript/presets/ServerNodesBatch11Index.ts';
      if (fs.existsSync(indexFile)) {
        const indexContent = fs.readFileSync(indexFile, 'utf8');
        console.log(`✅ 索引文件存在: ${indexFile}`);
        
        // 检查映射配置
        if (indexContent.includes('BATCH11_NODE_MAPPING')) {
          console.log('✅ 节点映射配置已定义');
        }
        
        if (indexContent.includes('BATCH11_NODE_CONFIGS')) {
          console.log('✅ 节点配置信息已定义');
        }
      } else {
        console.log('❌ 索引文件不存在');
      }
    } else {
      console.log('❌ 主实现文件不存在');
    }
  } catch (error) {
    console.error('❌ 文件验证失败:', error.message);
  }
  
  console.log('');
}

// 验证NodeRegistryService注册
function verifyNodeRegistryService() {
  console.log('=== 🔧 NodeRegistryService注册验证 ===');
  
  try {
    const registryFile = 'editor/src/services/NodeRegistryService.ts';
    if (fs.existsSync(registryFile)) {
      const content = fs.readFileSync(registryFile, 'utf8');
      
      // 检查构造函数中的批次11调用
      if (content.includes('this.initializeBatch11Nodes()')) {
        console.log('✅ 构造函数中已添加第11批次初始化调用');
      } else {
        console.log('❌ 构造函数中缺少第11批次初始化调用');
      }
      
      // 检查初始化方法
      if (content.includes('private initializeBatch11Nodes()')) {
        console.log('✅ 第11批次初始化方法已定义');
        
        // 统计注册的节点数量
        const batch11Section = content.match(/private initializeBatch11Nodes\(\)[\s\S]*?console\.log\('第11批次服务器端功能节点注册完成/);
        if (batch11Section) {
          const registerNodeCalls = (batch11Section[0].match(/this\.registerNode\(/g) || []).length;
          console.log(`📦 注册节点数量: ${registerNodeCalls}个`);
          
          if (registerNodeCalls === 30) {
            console.log('✅ 节点数量正确（30个）');
          } else {
            console.log(`⚠️ 节点数量不正确，期望30个，实际${registerNodeCalls}个`);
          }
        }
      } else {
        console.log('❌ 第11批次初始化方法未定义');
      }
    } else {
      console.log('❌ NodeRegistryService文件不存在');
    }
  } catch (error) {
    console.error('❌ NodeRegistryService验证失败:', error.message);
  }
  
  console.log('');
}

// 验证EngineNodeIntegration集成
function verifyEngineNodeIntegration() {
  console.log('=== ⚙️ EngineNodeIntegration集成验证 ===');
  
  try {
    const integrationFile = 'editor/src/services/EngineNodeIntegration.ts';
    if (fs.existsSync(integrationFile)) {
      const content = fs.readFileSync(integrationFile, 'utf8');
      
      // 检查初始化方法中的批次11调用
      if (content.includes('this.registerBatch11Nodes()')) {
        console.log('✅ 初始化方法中已添加第11批次注册调用');
      } else {
        console.log('❌ 初始化方法中缺少第11批次注册调用');
      }
      
      // 检查注册方法
      if (content.includes('private registerBatch11Nodes()')) {
        console.log('✅ 第11批次注册方法已定义');
        
        // 检查子方法
        if (content.includes('registerServerUserNodes')) {
          console.log('✅ 用户服务节点注册方法已定义');
        }
        
        if (content.includes('registerServerProjectNodes')) {
          console.log('✅ 项目服务节点注册方法已定义');
        }
        
        if (content.includes('registerServerAssetNodes')) {
          console.log('✅ 资产服务节点注册方法已定义');
        }
      } else {
        console.log('❌ 第11批次注册方法未定义');
      }
    } else {
      console.log('❌ EngineNodeIntegration文件不存在');
    }
  } catch (error) {
    console.error('❌ EngineNodeIntegration验证失败:', error.message);
  }
  
  console.log('');
}

// 验证节点类型定义
function verifyNodeTypes() {
  console.log('=== 📋 节点类型定义验证 ===');
  
  const expectedNodes = [
    // 用户服务节点 (301-310)
    'server/user/registerUser',
    'server/user/loginUser',
    'server/user/logoutUser',
    'server/user/updateUserProfile',
    'server/user/changePassword',
    'server/user/resetPassword',
    'server/user/getUserInfo',
    'server/user/deleteUser',
    'server/user/setUserRole',
    'server/user/validateToken',
    
    // 项目服务节点 (311-325)
    'server/project/createProject',
    'server/project/deleteProject',
    'server/project/updateProject',
    'server/project/getProjectList',
    'server/project/getProjectDetails',
    'server/project/shareProject',
    'server/project/unshareProject',
    'server/project/setProjectPermission',
    'server/project/forkProject',
    'server/project/archiveProject',
    'server/project/restoreProject',
    'server/project/exportProjectData',
    'server/project/importProjectData',
    'server/project/getProjectStats',
    'server/project/backupProject',
    
    // 资产服务节点 (326-330)
    'server/asset/uploadAsset',
    'server/asset/downloadAsset',
    'server/asset/deleteAsset',
    'server/asset/getAssetList',
    'server/asset/getAssetInfo'
  ];
  
  console.log(`📝 期望节点类型数量: ${expectedNodes.length}个`);
  
  // 按功能分组显示
  console.log('\n📂 节点分组:');
  console.log('  👤 用户服务节点 (301-310): 10个');
  console.log('  📁 项目服务节点 (311-325): 15个');
  console.log('  📦 资产服务节点 (326-330): 5个');
  
  // 验证节点类型格式
  const validTypes = expectedNodes.filter(type => type.startsWith('server/'));
  console.log(`✅ 有效节点类型: ${validTypes.length}个`);
  
  console.log('');
}

// 生成测试报告
function generateTestReport() {
  console.log('=== 📊 测试报告 ===');
  
  const report = {
    batchNumber: 11,
    nodeRange: '301-330',
    totalNodes: 30,
    categories: {
      userService: { range: '301-310', count: 10 },
      projectService: { range: '311-325', count: 15 },
      assetService: { range: '326-330', count: 5 }
    },
    files: {
      implementation: 'engine/src/visualscript/presets/ServerNodesBatch11.ts',
      index: 'engine/src/visualscript/presets/ServerNodesBatch11Index.ts',
      registry: 'editor/src/services/NodeRegistryService.ts',
      integration: 'editor/src/services/EngineNodeIntegration.ts'
    },
    features: [
      '用户注册登录认证',
      '项目管理和协作',
      '资产上传下载管理',
      '权限控制和安全',
      '数据导入导出'
    ]
  };
  
  console.log('📋 批次信息:');
  console.log(`  批次编号: ${report.batchNumber}`);
  console.log(`  节点范围: ${report.nodeRange}`);
  console.log(`  节点总数: ${report.totalNodes}个`);
  
  console.log('\n📂 功能分类:');
  Object.entries(report.categories).forEach(([key, info]) => {
    console.log(`  ${key}: ${info.range} (${info.count}个)`);
  });
  
  console.log('\n🎯 主要功能:');
  report.features.forEach(feature => {
    console.log(`  • ${feature}`);
  });
  
  console.log('\n📁 相关文件:');
  Object.entries(report.files).forEach(([key, path]) => {
    const exists = fs.existsSync(path);
    console.log(`  ${exists ? '✅' : '❌'} ${key}: ${path}`);
  });
  
  console.log('');
}

// 执行所有验证
function runAllVerifications() {
  verifyNodeImplementation();
  verifyNodeRegistryService();
  verifyEngineNodeIntegration();
  verifyNodeTypes();
  generateTestReport();
  
  console.log('🎉 第11批次节点验证完成！');
  console.log('\n📝 下一步操作:');
  console.log('  1. 运行编辑器，检查节点面板中是否显示新节点');
  console.log('  2. 测试拖拽创建节点功能');
  console.log('  3. 验证节点执行逻辑');
  console.log('  4. 更新文档状态');
}

// 运行验证
runAllVerifications();
