/**
 * 第9批次节点功能测试
 * 测试地形与环境系统、编辑器项目管理节点的基本功能
 */

import { nodeRegistryService } from '../services/NodeRegistryService';

describe('第9批次节点测试 - 地形与环境系统、编辑器项目管理（节点241-270）', () => {
  beforeAll(() => {
    // 确保节点注册服务已初始化
    expect(nodeRegistryService).toBeDefined();
  });

  describe('节点注册测试', () => {
    test('应该注册所有地形与环境系统节点 (241-250)', () => {
      const terrainEnvironmentNodes = [
        'water/refraction/enableRefraction',
        'vegetation/system/createVegetation',
        'vegetation/grass/addGrass',
        'vegetation/trees/addTrees',
        'weather/system/createWeatherSystem',
        'weather/rain/enableRain',
        'weather/snow/enableSnow',
        'weather/wind/setWindDirection',
        'weather/wind/setWindStrength',
        'environment/time/setTimeOfDay'
      ];

      terrainEnvironmentNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.type).toBe(nodeType);
      });
    });

    test('应该注册所有编辑器项目管理节点 (251-270)', () => {
      const editorProjectNodes = [
        'editor/project/createProject',
        'editor/project/openProject',
        'editor/project/saveProject',
        'editor/project/closeProject',
        'editor/project/exportProject',
        'editor/project/importProject',
        'editor/project/setProjectSettings',
        'editor/project/getProjectInfo',
        'editor/asset/importAsset',
        'editor/asset/deleteAsset',
        'editor/asset/renameAsset',
        'editor/asset/moveAsset',
        'editor/asset/createFolder',
        'editor/asset/getAssetInfo',
        'editor/asset/generateThumbnail',
        'editor/scene/createEntity',
        'editor/scene/deleteEntity',
        'editor/scene/selectEntity',
        'editor/scene/duplicateEntity',
        'editor/scene/groupEntities'
      ];

      editorProjectNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.type).toBe(nodeType);
      });
    });

    test('应该注册总共30个第9批次节点', () => {
      const batch9Nodes = [
        // 地形与环境系统节点 (241-250)
        'water/refraction/enableRefraction',
        'vegetation/system/createVegetation',
        'vegetation/grass/addGrass',
        'vegetation/trees/addTrees',
        'weather/system/createWeatherSystem',
        'weather/rain/enableRain',
        'weather/snow/enableSnow',
        'weather/wind/setWindDirection',
        'weather/wind/setWindStrength',
        'environment/time/setTimeOfDay',
        // 编辑器项目管理节点 (251-270)
        'editor/project/createProject',
        'editor/project/openProject',
        'editor/project/saveProject',
        'editor/project/closeProject',
        'editor/project/exportProject',
        'editor/project/importProject',
        'editor/project/setProjectSettings',
        'editor/project/getProjectInfo',
        'editor/asset/importAsset',
        'editor/asset/deleteAsset',
        'editor/asset/renameAsset',
        'editor/asset/moveAsset',
        'editor/asset/createFolder',
        'editor/asset/getAssetInfo',
        'editor/asset/generateThumbnail',
        'editor/scene/createEntity',
        'editor/scene/deleteEntity',
        'editor/scene/selectEntity',
        'editor/scene/duplicateEntity',
        'editor/scene/groupEntities'
      ];

      expect(batch9Nodes).toHaveLength(30);

      batch9Nodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.type).toBe(nodeType);
      });
    });
  });

  describe('节点属性测试', () => {
    test('水面折射节点应该有正确的属性', () => {
      const node = nodeRegistryService.getNode('water/refraction/enableRefraction');
      expect(node).toBeDefined();
      expect(node?.label).toBe('启用水面折射');
      expect(node?.description).toBe('启用水面折射效果');
      expect(node?.tags).toContain('水体');
      expect(node?.tags).toContain('折射');
    });

    test('植被系统节点应该有正确的属性', () => {
      const node = nodeRegistryService.getNode('vegetation/system/createVegetation');
      expect(node).toBeDefined();
      expect(node?.label).toBe('创建植被');
      expect(node?.description).toBe('创建植被系统');
      expect(node?.tags).toContain('植被');
      expect(node?.tags).toContain('系统');
    });

    test('天气系统节点应该有正确的属性', () => {
      const node = nodeRegistryService.getNode('weather/system/createWeatherSystem');
      expect(node).toBeDefined();
      expect(node?.label).toBe('创建天气系统');
      expect(node?.description).toBe('创建动态天气系统');
      expect(node?.tags).toContain('天气');
      expect(node?.tags).toContain('系统');
    });

    test('项目管理节点应该有正确的属性', () => {
      const node = nodeRegistryService.getNode('editor/project/createProject');
      expect(node).toBeDefined();
      expect(node?.label).toBe('创建项目');
      expect(node?.description).toBe('创建新的编辑器项目');
      expect(node?.tags).toContain('编辑器');
      expect(node?.tags).toContain('项目');
    });

    test('资产管理节点应该有正确的属性', () => {
      const node = nodeRegistryService.getNode('editor/asset/importAsset');
      expect(node).toBeDefined();
      expect(node?.label).toBe('导入资产');
      expect(node?.description).toBe('导入外部资产文件');
      expect(node?.tags).toContain('编辑器');
      expect(node?.tags).toContain('资产');
    });

    test('场景编辑节点应该有正确的属性', () => {
      const node = nodeRegistryService.getNode('editor/scene/createEntity');
      expect(node).toBeDefined();
      expect(node?.label).toBe('创建实体');
      expect(node?.description).toBe('在场景中创建新实体');
      expect(node?.tags).toContain('编辑器');
      expect(node?.tags).toContain('场景');
    });
  });

  describe('节点分类测试', () => {
    test('地形与环境系统节点应该属于正确的分类', () => {
      const terrainNodes = [
        'water/refraction/enableRefraction',
        'vegetation/system/createVegetation',
        'vegetation/grass/addGrass',
        'vegetation/trees/addTrees',
        'weather/system/createWeatherSystem',
        'weather/rain/enableRain',
        'weather/snow/enableSnow',
        'weather/wind/setWindDirection',
        'weather/wind/setWindStrength',
        'environment/time/setTimeOfDay'
      ];

      terrainNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(['ENTITY'].includes(node?.category || '')).toBeTruthy();
      });
    });

    test('编辑器节点应该属于正确的分类', () => {
      const editorNodes = [
        'editor/project/createProject',
        'editor/asset/importAsset',
        'editor/scene/createEntity'
      ];

      editorNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(['CUSTOM', 'ENTITY'].includes(node?.category || '')).toBeTruthy();
      });
    });
  });

  describe('节点搜索测试', () => {
    test('应该能够通过标签搜索节点', () => {
      const vegetationNodes = nodeRegistryService.getNodesByTag('植被');
      expect(vegetationNodes.length).toBeGreaterThan(0);
      
      const weatherNodes = nodeRegistryService.getNodesByTag('天气');
      expect(weatherNodes.length).toBeGreaterThan(0);
      
      const editorNodes = nodeRegistryService.getNodesByTag('编辑器');
      expect(editorNodes.length).toBeGreaterThan(0);
    });

    test('应该能够获取所有第9批次节点', () => {
      const allNodes = nodeRegistryService.getAllNodes();
      const batch9NodeTypes = [
        'water/refraction/enableRefraction',
        'vegetation/system/createVegetation',
        'editor/project/createProject',
        'editor/asset/importAsset',
        'editor/scene/createEntity'
      ];

      batch9NodeTypes.forEach(nodeType => {
        const found = allNodes.some(node => node.type === nodeType);
        expect(found).toBeTruthy();
      });
    });
  });

  describe('节点完整性测试', () => {
    test('所有第9批次节点都应该有必需的属性', () => {
      const batch9NodeTypes = [
        'water/refraction/enableRefraction',
        'vegetation/system/createVegetation',
        'vegetation/grass/addGrass',
        'vegetation/trees/addTrees',
        'weather/system/createWeatherSystem',
        'weather/rain/enableRain',
        'weather/snow/enableSnow',
        'weather/wind/setWindDirection',
        'weather/wind/setWindStrength',
        'environment/time/setTimeOfDay',
        'editor/project/createProject',
        'editor/project/openProject',
        'editor/project/saveProject',
        'editor/project/closeProject',
        'editor/project/exportProject',
        'editor/project/importProject',
        'editor/project/setProjectSettings',
        'editor/project/getProjectInfo',
        'editor/asset/importAsset',
        'editor/asset/deleteAsset',
        'editor/asset/renameAsset',
        'editor/asset/moveAsset',
        'editor/asset/createFolder',
        'editor/asset/getAssetInfo',
        'editor/asset/generateThumbnail',
        'editor/scene/createEntity',
        'editor/scene/deleteEntity',
        'editor/scene/selectEntity',
        'editor/scene/duplicateEntity',
        'editor/scene/groupEntities'
      ];

      batch9NodeTypes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.type).toBeTruthy();
        expect(node?.label).toBeTruthy();
        expect(node?.description).toBeTruthy();
        expect(node?.category).toBeTruthy();
        expect(node?.icon).toBeTruthy();
        expect(node?.color).toBeTruthy();
        expect(Array.isArray(node?.tags)).toBeTruthy();
        expect(node?.tags?.length).toBeGreaterThan(0);
      });
    });
  });
});
