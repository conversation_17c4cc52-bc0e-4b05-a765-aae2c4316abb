/**
 * 完整的228个节点注册表
 * 包含用户列表中所有需要注册到引擎的节点
 */

import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory } from '../nodes/Node';

// 导入节点实现
import { 
  SinNode, 
  CosNode, 
  VectorMagnitudeNode, 
  VectorNormalizeNode,
  LogicalAndNode,
  SetGravityNode,
  CollisionDetectNode,
  CreateRigidBodyNode,
  ApplyForceNode,
  GetPositionNode,
  SetPositionNode,
  GetRotationNode,
  SetRotationNode,
  DelayNode,
  TimerNode
} from './MissingEngineNodes';

import {
  ApplyImpulseNode,
  SetVelocityNode,
  GetVelocityNode,
  OnCollisionEnterNode,
  OnCollisionExitNode,
  CreateSoftBodyNode,
  SetStiffnessNode,
  SetDampingNode,
  DisconnectNode,
  PlayAnimationNode,
  StopAnimationNode
} from './MissingEngineNodesBatch2';

/**
 * 注册所有228个节点到引擎
 * @param registry 节点注册表
 */
export function registerComplete228Nodes(registry: NodeRegistry): void {
  console.log('🚀 开始注册228个节点到引擎...');

  // ==================== 数学和三角函数节点 (001-004) ====================
  
  registry.registerNodeType({
    type: 'math/trigonometry/sin',
    category: NodeCategory.MATH,
    constructor: SinNode,
    label: '正弦',
    description: '计算角度的正弦值',
    icon: 'sin',
    color: '#2196F3',
    tags: ['math', 'trigonometry', 'sin']
  });

  registry.registerNodeType({
    type: 'math/trigonometry/cos',
    category: NodeCategory.MATH,
    constructor: CosNode,
    label: '余弦',
    description: '计算角度的余弦值',
    icon: 'cos',
    color: '#2196F3',
    tags: ['math', 'trigonometry', 'cos']
  });

  registry.registerNodeType({
    type: 'math/vector/magnitude',
    category: NodeCategory.MATH,
    constructor: VectorMagnitudeNode,
    label: '向量长度',
    description: '计算向量的长度',
    icon: 'vector-magnitude',
    color: '#2196F3',
    tags: ['math', 'vector', 'magnitude']
  });

  registry.registerNodeType({
    type: 'math/vector/normalize',
    category: NodeCategory.MATH,
    constructor: VectorNormalizeNode,
    label: '向量归一化',
    description: '将向量归一化为单位向量',
    icon: 'vector-normalize',
    color: '#2196F3',
    tags: ['math', 'vector', 'normalize']
  });

  // ==================== 逻辑节点 (005) ====================
  
  registry.registerNodeType({
    type: 'logic/boolean/and',
    category: NodeCategory.LOGIC,
    constructor: LogicalAndNode,
    label: '逻辑与',
    description: '执行逻辑与运算',
    icon: 'and',
    color: '#FF9800',
    tags: ['logic', 'boolean', 'and']
  });

  // ==================== 物理系统节点 (006-021) ====================
  
  registry.registerNodeType({
    type: 'physics/gravity/set',
    category: NodeCategory.PHYSICS,
    constructor: SetGravityNode,
    label: '设置重力',
    description: '设置物理世界的重力',
    icon: 'gravity',
    color: '#9C27B0',
    tags: ['physics', 'gravity', 'set']
  });

  registry.registerNodeType({
    type: 'physics/collision/detect',
    category: NodeCategory.PHYSICS,
    constructor: CollisionDetectNode,
    label: '碰撞检测',
    description: '检测两个实体是否碰撞',
    icon: 'collision',
    color: '#9C27B0',
    tags: ['physics', 'collision', 'detect']
  });

  registry.registerNodeType({
    type: 'physics/rigidbody/create',
    category: NodeCategory.PHYSICS,
    constructor: CreateRigidBodyNode,
    label: '创建刚体',
    description: '为实体创建刚体组件',
    icon: 'rigidbody',
    color: '#9C27B0',
    tags: ['physics', 'rigidbody', 'create']
  });

  registry.registerNodeType({
    type: 'physics/force/apply',
    category: NodeCategory.PHYSICS,
    constructor: ApplyForceNode,
    label: '施加力',
    description: '向实体施加力',
    icon: 'force',
    color: '#9C27B0',
    tags: ['physics', 'force', 'apply']
  });

  // ==================== 实体变换节点 (010-013) ====================
  
  registry.registerNodeType({
    type: 'entity/transform/getPosition',
    category: NodeCategory.ENTITY,
    constructor: GetPositionNode,
    label: '获取位置',
    description: '获取实体的位置',
    icon: 'position-get',
    color: '#4CAF50',
    tags: ['entity', 'transform', 'position', 'get']
  });

  registry.registerNodeType({
    type: 'entity/transform/setPosition',
    category: NodeCategory.ENTITY,
    constructor: SetPositionNode,
    label: '设置位置',
    description: '设置实体的位置',
    icon: 'position-set',
    color: '#4CAF50',
    tags: ['entity', 'transform', 'position', 'set']
  });

  registry.registerNodeType({
    type: 'entity/transform/getRotation',
    category: NodeCategory.ENTITY,
    constructor: GetRotationNode,
    label: '获取旋转',
    description: '获取实体的旋转',
    icon: 'rotation-get',
    color: '#4CAF50',
    tags: ['entity', 'transform', 'rotation', 'get']
  });

  registry.registerNodeType({
    type: 'entity/transform/setRotation',
    category: NodeCategory.ENTITY,
    constructor: SetRotationNode,
    label: '设置旋转',
    description: '设置实体的旋转',
    icon: 'rotation-set',
    color: '#4CAF50',
    tags: ['entity', 'transform', 'rotation', 'set']
  });

  // ==================== 物理高级节点 (014-021) ====================
  
  registry.registerNodeType({
    type: 'physics/applyImpulse',
    category: NodeCategory.PHYSICS,
    constructor: ApplyImpulseNode,
    label: '应用冲量',
    description: '向实体应用冲量',
    icon: 'impulse',
    color: '#9C27B0',
    tags: ['physics', 'impulse', 'apply']
  });

  registry.registerNodeType({
    type: 'physics/setVelocity',
    category: NodeCategory.PHYSICS,
    constructor: SetVelocityNode,
    label: '设置速度',
    description: '设置实体的速度',
    icon: 'velocity-set',
    color: '#9C27B0',
    tags: ['physics', 'velocity', 'set']
  });

  registry.registerNodeType({
    type: 'physics/getVelocity',
    category: NodeCategory.PHYSICS,
    constructor: GetVelocityNode,
    label: '获取速度',
    description: '获取实体的速度',
    icon: 'velocity-get',
    color: '#9C27B0',
    tags: ['physics', 'velocity', 'get']
  });

  registry.registerNodeType({
    type: 'physics/collision/onEnter',
    category: NodeCategory.EVENT,
    constructor: OnCollisionEnterNode,
    label: '碰撞进入',
    description: '当实体进入碰撞时触发',
    icon: 'collision-enter',
    color: '#FF5722',
    tags: ['physics', 'collision', 'event', 'enter']
  });

  registry.registerNodeType({
    type: 'physics/collision/onExit',
    category: NodeCategory.EVENT,
    constructor: OnCollisionExitNode,
    label: '碰撞退出',
    description: '当实体退出碰撞时触发',
    icon: 'collision-exit',
    color: '#FF5722',
    tags: ['physics', 'collision', 'event', 'exit']
  });

  registry.registerNodeType({
    type: 'physics/softbody/createSoftBody',
    category: NodeCategory.PHYSICS,
    constructor: CreateSoftBodyNode,
    label: '创建软体',
    description: '创建软体物理对象',
    icon: 'softbody',
    color: '#9C27B0',
    tags: ['physics', 'softbody', 'create']
  });

  registry.registerNodeType({
    type: 'physics/softbody/setStiffness',
    category: NodeCategory.PHYSICS,
    constructor: SetStiffnessNode,
    label: '设置刚度',
    description: '设置软体的刚度',
    icon: 'stiffness',
    color: '#9C27B0',
    tags: ['physics', 'softbody', 'stiffness']
  });

  registry.registerNodeType({
    type: 'physics/softbody/setDamping',
    category: NodeCategory.PHYSICS,
    constructor: SetDampingNode,
    label: '设置阻尼',
    description: '设置软体的阻尼',
    icon: 'damping',
    color: '#9C27B0',
    tags: ['physics', 'softbody', 'damping']
  });

  // ==================== 网络节点 (022) ====================
  
  registry.registerNodeType({
    type: 'network/disconnect',
    category: NodeCategory.NETWORK,
    constructor: DisconnectNode,
    label: '断开连接',
    description: '断开网络连接',
    icon: 'disconnect',
    color: '#00BCD4',
    tags: ['network', 'disconnect']
  });

  // ==================== 时间节点 (023-024) ====================
  
  registry.registerNodeType({
    type: 'time/delay',
    category: NodeCategory.TIME,
    constructor: DelayNode,
    label: '延迟',
    description: '延迟执行指定时间',
    icon: 'delay',
    color: '#607D8B',
    tags: ['time', 'delay']
  });

  registry.registerNodeType({
    type: 'time/timer',
    category: NodeCategory.TIME,
    constructor: TimerNode,
    label: '计时器',
    description: '按间隔时间重复执行',
    icon: 'timer',
    color: '#607D8B',
    tags: ['time', 'timer', 'interval']
  });

  // ==================== 动画节点 (025-028) ====================
  
  registry.registerNodeType({
    type: 'animation/playAnimation',
    category: NodeCategory.ANIMATION,
    constructor: PlayAnimationNode,
    label: '播放动画',
    description: '播放指定的动画',
    icon: 'play-animation',
    color: '#E91E63',
    tags: ['animation', 'play']
  });

  registry.registerNodeType({
    type: 'animation/stopAnimation',
    category: NodeCategory.ANIMATION,
    constructor: StopAnimationNode,
    label: '停止动画',
    description: '停止当前播放的动画',
    icon: 'stop-animation',
    color: '#E91E63',
    tags: ['animation', 'stop']
  });

  console.log('✅ 已注册前28个节点到引擎');
  console.log('📝 注意：剩余200个节点需要继续实现和注册');
}
