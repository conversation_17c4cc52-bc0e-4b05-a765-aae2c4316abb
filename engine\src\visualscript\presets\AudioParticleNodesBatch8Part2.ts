/**
 * 音频与粒子系统节点 - 第二部分
 * 第8批次：音频与粒子系统（节点222-240）
 * 包含粒子属性、物理效果、地形和水体节点
 */

import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Node } from '../nodes/Node';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// 粒子系统节点（222-230）- 继续
// ============================================================================

/**
 * 设置粒子大小节点 (222)
 * 设置粒子尺寸
 */
export class SetSizeNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子大小',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'sizeVariation',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '大小变化',
      defaultValue: 0.2
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const size = this.getInputValue('size');
    const sizeVariation = this.getInputValue('sizeVariation');

    try {
      if (particleSystem) {
        (particleSystem as any).particleSize = size;
        (particleSystem as any).sizeVariation = sizeVariation;
        
        // 更新材质大小
        if (particleSystem.material) {
          particleSystem.material.size = size;
        }
      }
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetSizeNode: 设置粒子大小失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置粒子颜色节点 (223)
 * 设置粒子颜色
 */
export class SetColorNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子颜色',
      defaultValue: 0xffffff
    });

    this.addInput({
      name: 'opacity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '透明度',
      defaultValue: 1.0
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const color = this.getInputValue('color');
    const opacity = this.getInputValue('opacity');

    try {
      if (particleSystem) {
        (particleSystem as any).particleColor = color;
        (particleSystem as any).particleOpacity = opacity;
        
        // 更新材质颜色
        if (particleSystem.material) {
          particleSystem.material.color.setHex(color);
          particleSystem.material.opacity = opacity;
        }
      }
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetColorNode: 设置粒子颜色失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 添加重力节点 (224)
 * 为粒子添加重力影响
 */
export class AddGravityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'gravity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '重力强度',
      defaultValue: -9.8
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const gravity = this.getInputValue('gravity');

    try {
      if (particleSystem) {
        (particleSystem as any).gravity = new THREE.Vector3(0, gravity, 0);
      }
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('AddGravityNode: 添加重力失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 添加风力节点 (225)
 * 为粒子添加风力影响
 */
export class AddWindNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'windDirection',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '风向',
      defaultValue: new THREE.Vector3(1, 0, 0)
    });

    this.addInput({
      name: 'windStrength',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '风力强度',
      defaultValue: 2.0
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const windDirection = this.getInputValue('windDirection');
    const windStrength = this.getInputValue('windStrength');

    try {
      if (particleSystem) {
        const wind = windDirection.clone().normalize().multiplyScalar(windStrength);
        (particleSystem as any).wind = wind;
      }
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('AddWindNode: 添加风力失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 添加湍流节点 (226)
 * 为粒子添加湍流效果
 */
export class AddTurbulenceNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'turbulenceStrength',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '湍流强度',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'turbulenceFrequency',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '湍流频率',
      defaultValue: 0.1
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const turbulenceStrength = this.getInputValue('turbulenceStrength');
    const turbulenceFrequency = this.getInputValue('turbulenceFrequency');

    try {
      if (particleSystem) {
        (particleSystem as any).turbulenceStrength = turbulenceStrength;
        (particleSystem as any).turbulenceFrequency = turbulenceFrequency;
      }
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('AddTurbulenceNode: 添加湍流失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 启用粒子碰撞节点 (227)
 * 启用粒子与物体碰撞
 */
export class EnableCollisionNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'collisionObjects',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '碰撞对象数组'
    });

    this.addInput({
      name: 'bounciness',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '弹性系数',
      defaultValue: 0.5
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const collisionObjects = this.getInputValue('collisionObjects');
    const bounciness = this.getInputValue('bounciness');

    try {
      if (particleSystem) {
        (particleSystem as any).collisionEnabled = true;
        (particleSystem as any).collisionObjects = collisionObjects || [];
        (particleSystem as any).bounciness = bounciness;
      }
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('EnableCollisionNode: 启用粒子碰撞失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置粒子材质节点 (228)
 * 设置粒子渲染材质
 */
export class SetParticleMaterialNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'texture',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子纹理'
    });

    this.addInput({
      name: 'blendMode',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '混合模式',
      defaultValue: 'normal'
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const texture = this.getInputValue('texture');
    const blendMode = this.getInputValue('blendMode');

    try {
      if (particleSystem && particleSystem.material) {
        if (texture) {
          particleSystem.material.map = texture;
        }

        // 设置混合模式
        switch (blendMode) {
          case 'additive':
            particleSystem.material.blending = THREE.AdditiveBlending;
            break;
          case 'multiply':
            particleSystem.material.blending = THREE.MultiplyBlending;
            break;
          default:
            particleSystem.material.blending = THREE.NormalBlending;
        }

        particleSystem.material.needsUpdate = true;
      }
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetParticleMaterialNode: 设置粒子材质失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 动画粒子大小节点 (229)
 * 创建粒子大小动画
 */
export class AnimateSizeNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'startSize',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '起始大小',
      defaultValue: 0.1
    });

    this.addInput({
      name: 'endSize',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '结束大小',
      defaultValue: 2.0
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const startSize = this.getInputValue('startSize');
    const endSize = this.getInputValue('endSize');

    try {
      if (particleSystem) {
        (particleSystem as any).sizeAnimation = {
          enabled: true,
          startSize: startSize,
          endSize: endSize
        };
      }
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('AnimateSizeNode: 动画粒子大小失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 动画粒子颜色节点 (230)
 * 创建粒子颜色动画
 */
export class AnimateColorNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'startColor',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '起始颜色',
      defaultValue: 0xffffff
    });

    this.addInput({
      name: 'endColor',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '结束颜色',
      defaultValue: 0x000000
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const startColor = this.getInputValue('startColor');
    const endColor = this.getInputValue('endColor');

    try {
      if (particleSystem) {
        (particleSystem as any).colorAnimation = {
          enabled: true,
          startColor: startColor,
          endColor: endColor
        };
      }
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('AnimateColorNode: 动画粒子颜色失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}
