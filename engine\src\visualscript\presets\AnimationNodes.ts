/**
 * 视觉脚本动画节点
 * 提供动画系统相关的节点
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { Vector3 } from '../../math/Vector3';
import { VisualScriptNode } from '../VisualScriptNode';

/**
 * 播放动画节点
 */
export class PlayAnimationNode extends VisualScriptNode {
  constructor() {
    super('PlayAnimation', '播放动画');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('entity', 'entity', '实体');
    this.addInput('clipName', 'string', '动画名称');
    this.addOutput('completed', 'exec', '完成');
  }

  public execute(inputs: any): any {
    if (inputs.trigger && inputs.entity && inputs.clipName) {
      const animationComponent = inputs.entity.getComponent('AnimationComponent') as any as any;
      if (animationComponent) {
        animationComponent.play(inputs.clipName);
        return { completed: true };
      }
    }
    return {};
  }
}

/**
 * 停止动画节点
 */
export class StopAnimationNode extends VisualScriptNode {
  constructor() {
    super('StopAnimation', '停止动画');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('entity', 'entity', '实体');
    this.addOutput('completed', 'exec', '完成');
  }

  public execute(inputs: any): any {
    if (inputs.trigger && inputs.entity) {
      const animationComponent = inputs.entity.getComponent('AnimationComponent') as any as any;
      if (animationComponent) {
        animationComponent.stop();
        return { completed: true };
      }
    }
    return {};
  }
}

/**
 * 设置动画速度节点
 */
export class SetAnimationSpeedNode extends VisualScriptNode {
  constructor() {
    super('SetAnimationSpeed', '设置动画速度');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('entity', 'entity', '实体');
    this.addInput('speed', 'number', '速度');
    this.addOutput('completed', 'exec', '完成');
  }

  public execute(inputs: any): any {
    if (inputs.trigger && inputs.entity && typeof inputs.speed === 'number') {
      const animationComponent = inputs.entity.getComponent('AnimationComponent') as any as any;
      if (animationComponent) {
        const animator = animationComponent.getAnimator();
        if (animator) {
          animator.setTimeScale(inputs.speed);
          return { completed: true };
        }
      }
    }
    return {};
  }
}

/**
 * 获取动画状态节点
 */
export class GetAnimationStateNode extends VisualScriptNode {
  constructor() {
    super('GetAnimationState', '获取动画状态');
    this.addInput('entity', 'entity', '实体');
    this.addOutput('isPlaying', 'boolean', '是否播放中');
    this.addOutput('currentClip', 'string', '当前动画');
    this.addOutput('time', 'number', '播放时间');
  }

  public execute(inputs: any): any {
    if (inputs.entity) {
      const animationComponent = inputs.entity.getComponent('AnimationComponent') as any as any;
      if (animationComponent) {
        const animator = animationComponent.getAnimator();
        return {
          isPlaying: animationComponent.getIsPlaying(),
          currentClip: animationComponent.getCurrentClip(),
          time: animator ? animator.getTime() : 0
        };
      }
    }
    return {
      isPlaying: false,
      currentClip: null,
      time: 0
    };
  }
}

/**
 * 创建动画片段节点 (166)
 * 创建动画剪辑
 */
export class CreateAnimationClipNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加动画配置输入
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '动画名称',
      defaultValue: 'NewClip'
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '动画时长（秒）',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'loop',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否循环',
      defaultValue: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加动画片段输出
    this.addOutput({
      name: 'animationClip',
      type: SocketType.DATA,
      dataType: 'Object',
      direction: SocketDirection.OUTPUT,
      description: '动画片段'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取输入值
    const name = this.getInputValue('name') as string;
    const duration = this.getInputValue('duration') as number;
    const loop = this.getInputValue('loop') as boolean;

    // 创建动画片段配置
    const animationClip = {
      name,
      duration,
      loop,
      tracks: [],
      type: 'animationClip',
      created: Date.now()
    };

    console.log('创建动画片段:', animationClip);

    // 设置输出值
    this.setOutputValue('animationClip', animationClip);

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 添加关键帧节点 (167)
 * 在动画中添加关键帧
 */
export class AddKeyframeNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加动画片段输入
    this.addInput({
      name: 'animationClip',
      type: SocketType.DATA,
      dataType: 'Object',
      direction: SocketDirection.INPUT,
      description: '动画片段'
    });

    // 添加关键帧配置输入
    this.addInput({
      name: 'time',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '时间（秒）',
      defaultValue: 0
    });

    this.addInput({
      name: 'property',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '属性名称',
      defaultValue: 'position'
    });

    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '属性值',
      defaultValue: new Vector3(0, 0, 0)
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加更新后的动画片段输出
    this.addOutput({
      name: 'animationClip',
      type: SocketType.DATA,
      dataType: 'Object',
      direction: SocketDirection.OUTPUT,
      description: '更新后的动画片段'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取输入值
    const animationClip = this.getInputValue('animationClip') as any;
    const time = this.getInputValue('time') as number;
    const property = this.getInputValue('property') as string;
    const value = this.getInputValue('value') as Vector3;

    if (!animationClip) {
      console.warn('AddKeyframeNode: 动画片段无效');
      this.triggerFlow('flow');
      return;
    }

    // 添加关键帧到动画片段
    if (!animationClip.tracks) {
      animationClip.tracks = [];
    }

    // 查找或创建轨道
    let track = animationClip.tracks.find((t: any) => t.property === property);
    if (!track) {
      track = {
        property,
        keyframes: []
      };
      animationClip.tracks.push(track);
    }

    // 添加关键帧
    track.keyframes.push({
      time,
      value: { x: value.x, y: value.y, z: value.z }
    });

    // 按时间排序关键帧
    track.keyframes.sort((a: any, b: any) => a.time - b.time);

    console.log('添加关键帧:', { time, property, value });

    // 设置输出值
    this.setOutputValue('animationClip', animationClip);

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 设置插值方式节点 (168)
 * 设置关键帧插值方法
 */
export class SetInterpolationNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加动画片段输入
    this.addInput({
      name: 'animationClip',
      type: SocketType.DATA,
      dataType: 'Object',
      direction: SocketDirection.INPUT,
      description: '动画片段'
    });

    // 添加插值方式输入
    this.addInput({
      name: 'interpolationType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '插值方式',
      defaultValue: 'linear'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加更新后的动画片段输出
    this.addOutput({
      name: 'animationClip',
      type: SocketType.DATA,
      dataType: 'Object',
      direction: SocketDirection.OUTPUT,
      description: '更新后的动画片段'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取输入值
    const animationClip = this.getInputValue('animationClip') as any;
    const interpolationType = this.getInputValue('interpolationType') as string;

    if (!animationClip) {
      console.warn('SetInterpolationNode: 动画片段无效');
      this.triggerFlow('flow');
      return;
    }

    // 设置插值方式
    animationClip.interpolationType = interpolationType;

    console.log('设置插值方式:', interpolationType);

    // 设置输出值
    this.setOutputValue('animationClip', animationClip);

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 创建动画混合器节点 (169)
 * 创建动画控制器
 */
export class CreateAnimationMixerNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加动画混合器输出
    this.addOutput({
      name: 'animationMixer',
      type: SocketType.DATA,
      dataType: 'Object',
      direction: SocketDirection.OUTPUT,
      description: '动画混合器'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;

    if (!entity) {
      console.warn('CreateAnimationMixerNode: 实体无效');
      this.triggerFlow('flow');
      return;
    }

    // 创建动画混合器配置
    const animationMixer = {
      entity,
      clips: new Map(),
      actions: new Map(),
      currentTime: 0,
      timeScale: 1,
      type: 'animationMixer',
      created: Date.now()
    };

    console.log('创建动画混合器:', animationMixer);

    // 设置输出值
    this.setOutputValue('animationMixer', animationMixer);

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

/**
 * 播放动画动作节点 (170)
 * 在混合器中播放动画
 */
export class PlayAnimationActionNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加动画混合器输入
    this.addInput({
      name: 'animationMixer',
      type: SocketType.DATA,
      dataType: 'Object',
      direction: SocketDirection.INPUT,
      description: '动画混合器'
    });

    // 添加动画片段输入
    this.addInput({
      name: 'animationClip',
      type: SocketType.DATA,
      dataType: 'Object',
      direction: SocketDirection.INPUT,
      description: '动画片段'
    });

    // 添加播放配置输入
    this.addInput({
      name: 'loop',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否循环',
      defaultValue: true
    });

    this.addInput({
      name: 'weight',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '权重',
      defaultValue: 1.0
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加动画动作输出
    this.addOutput({
      name: 'animationAction',
      type: SocketType.DATA,
      dataType: 'Object',
      direction: SocketDirection.OUTPUT,
      description: '动画动作'
    });
  }

  /**
   * 执行节点
   */
  public execute(): void {
    // 获取输入值
    const animationMixer = this.getInputValue('animationMixer') as any;
    const animationClip = this.getInputValue('animationClip') as any;
    const loop = this.getInputValue('loop') as boolean;
    const weight = this.getInputValue('weight') as number;

    if (!animationMixer || !animationClip) {
      console.warn('PlayAnimationActionNode: 动画混合器或动画片段无效');
      this.triggerFlow('flow');
      return;
    }

    // 创建动画动作
    const animationAction = {
      mixer: animationMixer,
      clip: animationClip,
      loop,
      weight,
      time: 0,
      enabled: true,
      type: 'animationAction',
      created: Date.now()
    };

    // 添加到混合器
    animationMixer.actions.set(animationClip.name, animationAction);

    console.log('播放动画动作:', animationAction);

    // 设置输出值
    this.setOutputValue('animationAction', animationAction);

    // 触发输出流程
    this.triggerFlow('flow');
  }
}

// 简化版本的剩余动画节点实现 (171-180)

/**
 * 创建骨骼动画节点 (171)
 */
export class CreateSkeletalAnimationNode extends FlowNode {
  protected initializeSockets(): void {
    this.addInput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.INPUT, description: '输入流程' });
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'Entity', direction: SocketDirection.INPUT, description: '实体' });
    this.addOutput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.OUTPUT, description: '输出流程' });
    this.addOutput({ name: 'skeletalAnimation', type: SocketType.DATA, dataType: 'Object', direction: SocketDirection.OUTPUT, description: '骨骼动画' });
  }
  public execute(): void {
    const entity = this.getInputValue('entity') as Entity;
    const skeletalAnimation = { entity, type: 'skeletalAnimation', created: Date.now() };
    this.setOutputValue('skeletalAnimation', skeletalAnimation);
    this.triggerFlow('flow');
  }
}

/**
 * 设置骨骼变换节点 (172)
 */
export class SetBoneTransformNode extends FlowNode {
  protected initializeSockets(): void {
    this.addInput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.INPUT, description: '输入流程' });
    this.addInput({ name: 'skeletalAnimation', type: SocketType.DATA, dataType: 'Object', direction: SocketDirection.INPUT, description: '骨骼动画' });
    this.addInput({ name: 'boneName', type: SocketType.DATA, dataType: 'string', direction: SocketDirection.INPUT, description: '骨骼名称', defaultValue: 'Bone' });
    this.addInput({ name: 'transform', type: SocketType.DATA, dataType: 'Vector3', direction: SocketDirection.INPUT, description: '变换', defaultValue: new Vector3(0, 0, 0) });
    this.addOutput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.OUTPUT, description: '输出流程' });
  }
  public execute(): void {
    console.log('设置骨骼变换');
    this.triggerFlow('flow');
  }
}

/**
 * 创建IK约束节点 (173)
 */
export class CreateIKConstraintNode extends FlowNode {
  protected initializeSockets(): void {
    this.addInput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.INPUT, description: '输入流程' });
    this.addInput({ name: 'target', type: SocketType.DATA, dataType: 'Vector3', direction: SocketDirection.INPUT, description: '目标位置', defaultValue: new Vector3(0, 0, 0) });
    this.addOutput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.OUTPUT, description: '输出流程' });
    this.addOutput({ name: 'ikConstraint', type: SocketType.DATA, dataType: 'Object', direction: SocketDirection.OUTPUT, description: 'IK约束' });
  }
  public execute(): void {
    const target = this.getInputValue('target') as Vector3;
    const ikConstraint = { target, type: 'ikConstraint', created: Date.now() };
    this.setOutputValue('ikConstraint', ikConstraint);
    this.triggerFlow('flow');
  }
}

/**
 * 解算IK节点 (174)
 */
export class SolveIKNode extends FlowNode {
  protected initializeSockets(): void {
    this.addInput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.INPUT, description: '输入流程' });
    this.addInput({ name: 'ikConstraint', type: SocketType.DATA, dataType: 'Object', direction: SocketDirection.INPUT, description: 'IK约束' });
    this.addOutput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.OUTPUT, description: '输出流程' });
  }
  public execute(): void {
    console.log('解算IK');
    this.triggerFlow('flow');
  }
}

/**
 * 创建变形目标节点 (175)
 */
export class CreateMorphTargetNode extends FlowNode {
  protected initializeSockets(): void {
    this.addInput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.INPUT, description: '输入流程' });
    this.addInput({ name: 'name', type: SocketType.DATA, dataType: 'string', direction: SocketDirection.INPUT, description: '变形名称', defaultValue: 'MorphTarget' });
    this.addOutput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.OUTPUT, description: '输出流程' });
    this.addOutput({ name: 'morphTarget', type: SocketType.DATA, dataType: 'Object', direction: SocketDirection.OUTPUT, description: '变形目标' });
  }
  public execute(): void {
    const name = this.getInputValue('name') as string;
    const morphTarget = { name, type: 'morphTarget', created: Date.now() };
    this.setOutputValue('morphTarget', morphTarget);
    this.triggerFlow('flow');
  }
}

/**
 * 设置变形权重节点 (176)
 */
export class SetMorphWeightNode extends FlowNode {
  protected initializeSockets(): void {
    this.addInput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.INPUT, description: '输入流程' });
    this.addInput({ name: 'morphTarget', type: SocketType.DATA, dataType: 'Object', direction: SocketDirection.INPUT, description: '变形目标' });
    this.addInput({ name: 'weight', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.INPUT, description: '权重', defaultValue: 1.0 });
    this.addOutput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.OUTPUT, description: '输出流程' });
  }
  public execute(): void {
    console.log('设置变形权重');
    this.triggerFlow('flow');
  }
}

/**
 * 创建动画曲线节点 (177)
 */
export class CreateAnimationCurveNode extends FlowNode {
  protected initializeSockets(): void {
    this.addInput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.INPUT, description: '输入流程' });
    this.addInput({ name: 'curveType', type: SocketType.DATA, dataType: 'string', direction: SocketDirection.INPUT, description: '曲线类型', defaultValue: 'bezier' });
    this.addOutput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.OUTPUT, description: '输出流程' });
    this.addOutput({ name: 'animationCurve', type: SocketType.DATA, dataType: 'Object', direction: SocketDirection.OUTPUT, description: '动画曲线' });
  }
  public execute(): void {
    const curveType = this.getInputValue('curveType') as string;
    const animationCurve = { curveType, type: 'animationCurve', created: Date.now() };
    this.setOutputValue('animationCurve', animationCurve);
    this.triggerFlow('flow');
  }
}

/**
 * 评估曲线节点 (178)
 */
export class EvaluateCurveNode extends FlowNode {
  protected initializeSockets(): void {
    this.addInput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.INPUT, description: '输入流程' });
    this.addInput({ name: 'animationCurve', type: SocketType.DATA, dataType: 'Object', direction: SocketDirection.INPUT, description: '动画曲线' });
    this.addInput({ name: 'time', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.INPUT, description: '时间', defaultValue: 0 });
    this.addOutput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.OUTPUT, description: '输出流程' });
    this.addOutput({ name: 'value', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.OUTPUT, description: '曲线值' });
  }
  public execute(): void {
    const time = this.getInputValue('time') as number;
    this.setOutputValue('value', Math.sin(time)); // 简化的曲线评估
    this.triggerFlow('flow');
  }
}

/**
 * 创建状态机节点 (179)
 */
export class CreateStateMachineNode extends FlowNode {
  protected initializeSockets(): void {
    this.addInput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.INPUT, description: '输入流程' });
    this.addInput({ name: 'initialState', type: SocketType.DATA, dataType: 'string', direction: SocketDirection.INPUT, description: '初始状态', defaultValue: 'Idle' });
    this.addOutput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.OUTPUT, description: '输出流程' });
    this.addOutput({ name: 'stateMachine', type: SocketType.DATA, dataType: 'Object', direction: SocketDirection.OUTPUT, description: '状态机' });
  }
  public execute(): void {
    const initialState = this.getInputValue('initialState') as string;
    const stateMachine = { currentState: initialState, states: new Map(), transitions: new Map(), type: 'stateMachine', created: Date.now() };
    this.setOutputValue('stateMachine', stateMachine);
    this.triggerFlow('flow');
  }
}

/**
 * 状态转换节点 (180)
 */
export class TransitionStateNode extends FlowNode {
  protected initializeSockets(): void {
    this.addInput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.INPUT, description: '输入流程' });
    this.addInput({ name: 'stateMachine', type: SocketType.DATA, dataType: 'Object', direction: SocketDirection.INPUT, description: '状态机' });
    this.addInput({ name: 'targetState', type: SocketType.DATA, dataType: 'string', direction: SocketDirection.INPUT, description: '目标状态', defaultValue: 'Walk' });
    this.addOutput({ name: 'flow', type: SocketType.FLOW, direction: SocketDirection.OUTPUT, description: '输出流程' });
  }
  public execute(): void {
    const stateMachine = this.getInputValue('stateMachine') as any;
    const targetState = this.getInputValue('targetState') as string;
    if (stateMachine) {
      stateMachine.currentState = targetState;
      console.log('状态转换到:', targetState);
    }
    this.triggerFlow('flow');
  }
}

/**
 * 注册动画节点
 */
export function registerAnimationNodes(registry: NodeRegistry): void {
  // 注册旧版动画节点
  registry.registerNodeType({
    type: 'animation/legacy/playAnimation',
    category: NodeCategory.ANIMATION,
    constructor: PlayAnimationNode as any,
    label: '播放动画',
    description: '播放实体动画',
    icon: 'play',
    color: '#4CAF50',
    tags: ['animation', 'play', 'legacy']
  });

  registry.registerNodeType({
    type: 'animation/legacy/stopAnimation',
    category: NodeCategory.ANIMATION,
    constructor: StopAnimationNode as any,
    label: '停止动画',
    description: '停止实体动画',
    icon: 'stop',
    color: '#4CAF50',
    tags: ['animation', 'stop', 'legacy']
  });

  registry.registerNodeType({
    type: 'animation/legacy/setAnimationSpeed',
    category: NodeCategory.ANIMATION,
    constructor: SetAnimationSpeedNode as any,
    label: '设置动画速度',
    description: '设置动画播放速度',
    icon: 'speed',
    color: '#4CAF50',
    tags: ['animation', 'speed', 'legacy']
  });

  registry.registerNodeType({
    type: 'animation/legacy/getAnimationState',
    category: NodeCategory.ANIMATION,
    constructor: GetAnimationStateNode as any,
    label: '获取动画状态',
    description: '获取动画播放状态',
    icon: 'state',
    color: '#4CAF50',
    tags: ['animation', 'state', 'legacy']
  });
}

/**
 * 注册新的动画节点
 * @param registry 节点注册表
 */
export function registerNewAnimationNodes(registry: NodeRegistry): void {
  // 注册创建动画片段节点 (166)
  registry.registerNodeType({
    type: 'animation/clip/createAnimationClip',
    category: NodeCategory.ANIMATION,
    constructor: CreateAnimationClipNode,
    label: '创建动画片段',
    description: '创建动画剪辑',
    icon: 'animation-clip',
    color: '#4CAF50',
    tags: ['animation', 'clip', 'create']
  });

  // 注册添加关键帧节点 (167)
  registry.registerNodeType({
    type: 'animation/clip/addKeyframe',
    category: NodeCategory.ANIMATION,
    constructor: AddKeyframeNode,
    label: '添加关键帧',
    description: '在动画中添加关键帧',
    icon: 'keyframe',
    color: '#4CAF50',
    tags: ['animation', 'keyframe', 'add']
  });

  // 注册设置插值方式节点 (168)
  registry.registerNodeType({
    type: 'animation/clip/setInterpolation',
    category: NodeCategory.ANIMATION,
    constructor: SetInterpolationNode,
    label: '设置插值方式',
    description: '设置关键帧插值方法',
    icon: 'interpolation',
    color: '#4CAF50',
    tags: ['animation', 'interpolation', 'curve']
  });

  // 注册创建动画混合器节点 (169)
  registry.registerNodeType({
    type: 'animation/mixer/createAnimationMixer',
    category: NodeCategory.ANIMATION,
    constructor: CreateAnimationMixerNode,
    label: '创建动画混合器',
    description: '创建动画控制器',
    icon: 'animation-mixer',
    color: '#8BC34A',
    tags: ['animation', 'mixer', 'controller']
  });

  // 注册播放动画动作节点 (170)
  registry.registerNodeType({
    type: 'animation/mixer/playAnimationAction',
    category: NodeCategory.ANIMATION,
    constructor: PlayAnimationActionNode,
    label: '播放动画动作',
    description: '在混合器中播放动画',
    icon: 'play-action',
    color: '#8BC34A',
    tags: ['animation', 'action', 'play']
  });

  // 注册创建骨骼动画节点 (171)
  registry.registerNodeType({
    type: 'animation/skeleton/createSkeletalAnimation',
    category: NodeCategory.ANIMATION,
    constructor: CreateSkeletalAnimationNode,
    label: '创建骨骼动画',
    description: '创建骨骼动画系统',
    icon: 'skeleton',
    color: '#FF9800',
    tags: ['animation', 'skeleton', 'bone']
  });

  // 注册设置骨骼变换节点 (172)
  registry.registerNodeType({
    type: 'animation/skeleton/setBoneTransform',
    category: NodeCategory.ANIMATION,
    constructor: SetBoneTransformNode,
    label: '设置骨骼变换',
    description: '设置骨骼的变换矩阵',
    icon: 'bone-transform',
    color: '#FF9800',
    tags: ['animation', 'bone', 'transform']
  });

  // 注册创建IK约束节点 (173)
  registry.registerNodeType({
    type: 'animation/ik/createIKConstraint',
    category: NodeCategory.ANIMATION,
    constructor: CreateIKConstraintNode,
    label: '创建IK约束',
    description: '创建反向动力学约束',
    icon: 'ik-constraint',
    color: '#FF5722',
    tags: ['animation', 'ik', 'constraint']
  });

  // 注册解算IK节点 (174)
  registry.registerNodeType({
    type: 'animation/ik/solveIK',
    category: NodeCategory.ANIMATION,
    constructor: SolveIKNode,
    label: '解算IK',
    description: '执行反向动力学解算',
    icon: 'ik-solve',
    color: '#FF5722',
    tags: ['animation', 'ik', 'solve']
  });

  // 注册创建变形目标节点 (175)
  registry.registerNodeType({
    type: 'animation/morph/createMorphTarget',
    category: NodeCategory.ANIMATION,
    constructor: CreateMorphTargetNode,
    label: '创建变形目标',
    description: '创建顶点变形目标',
    icon: 'morph-target',
    color: '#9C27B0',
    tags: ['animation', 'morph', 'vertex']
  });

  // 注册设置变形权重节点 (176)
  registry.registerNodeType({
    type: 'animation/morph/setMorphWeight',
    category: NodeCategory.ANIMATION,
    constructor: SetMorphWeightNode,
    label: '设置变形权重',
    description: '设置变形目标权重',
    icon: 'morph-weight',
    color: '#9C27B0',
    tags: ['animation', 'morph', 'weight']
  });

  // 注册创建动画曲线节点 (177)
  registry.registerNodeType({
    type: 'animation/curve/createAnimationCurve',
    category: NodeCategory.ANIMATION,
    constructor: CreateAnimationCurveNode,
    label: '创建动画曲线',
    description: '创建自定义动画曲线',
    icon: 'animation-curve',
    color: '#607D8B',
    tags: ['animation', 'curve', 'custom']
  });

  // 注册评估曲线节点 (178)
  registry.registerNodeType({
    type: 'animation/curve/evaluateCurve',
    category: NodeCategory.ANIMATION,
    constructor: EvaluateCurveNode,
    label: '评估曲线',
    description: '在指定时间评估曲线值',
    icon: 'curve-evaluate',
    color: '#607D8B',
    tags: ['animation', 'curve', 'evaluate']
  });

  // 注册创建状态机节点 (179)
  registry.registerNodeType({
    type: 'animation/statemachine/createStateMachine',
    category: NodeCategory.ANIMATION,
    constructor: CreateStateMachineNode,
    label: '创建状态机',
    description: '创建动画状态机',
    icon: 'state-machine',
    color: '#795548',
    tags: ['animation', 'state', 'machine']
  });

  // 注册状态转换节点 (180)
  registry.registerNodeType({
    type: 'animation/statemachine/transitionState',
    category: NodeCategory.ANIMATION,
    constructor: TransitionStateNode,
    label: '状态转换',
    description: '在状态机中转换状态',
    icon: 'state-transition',
    color: '#795548',
    tags: ['animation', 'state', 'transition']
  });
}
