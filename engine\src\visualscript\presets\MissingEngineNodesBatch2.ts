/**
 * 缺失的引擎节点实现 - 第二批
 * 包含物理高级、动画、音频、输入等节点
 */

import { Node, NodeCategory, SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

// ==================== 物理高级节点 ====================

/**
 * 应用冲量节点
 */
export class ApplyImpulseNode extends Node {
  constructor() {
    super('physics/applyImpulse', NodeCategory.PHYSICS);
    
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });
    
    this.addInput({
      name: 'impulse',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.INPUT,
      description: '冲量向量'
    });
    
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const impulse = this.getInputValue('impulse', { x: 0, y: 0, z: 0 });
    
    if (entity && context.world && context.world.physicsWorld) {
      context.world.physicsWorld.applyImpulse(entity, impulse);
    }
    
    this.executeOutput('exec', context);
  }
}

/**
 * 设置速度节点
 */
export class SetVelocityNode extends Node {
  constructor() {
    super('physics/setVelocity', NodeCategory.PHYSICS);
    
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });
    
    this.addInput({
      name: 'velocity',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.INPUT,
      description: '速度向量'
    });
    
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const velocity = this.getInputValue('velocity', { x: 0, y: 0, z: 0 });
    
    if (entity && context.world && context.world.physicsWorld) {
      context.world.physicsWorld.setVelocity(entity, velocity);
    }
    
    this.executeOutput('exec', context);
  }
}

/**
 * 获取速度节点
 */
export class GetVelocityNode extends Node {
  constructor() {
    super('physics/getVelocity', NodeCategory.PHYSICS);
    
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });
    
    this.addOutput({
      name: 'velocity',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.OUTPUT,
      description: '速度向量'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    
    if (entity && context.world && context.world.physicsWorld) {
      const velocity = context.world.physicsWorld.getVelocity(entity);
      this.setOutputValue('velocity', velocity);
    } else {
      this.setOutputValue('velocity', { x: 0, y: 0, z: 0 });
    }
  }
}

// ==================== 碰撞事件节点 ====================

/**
 * 碰撞进入事件节点
 */
export class OnCollisionEnterNode extends Node {
  constructor() {
    super('physics/collision/onEnter', NodeCategory.EVENT);
    
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.INPUT,
      description: '监听实体'
    });
    
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '碰撞进入时执行'
    });
    
    this.addOutput({
      name: 'otherEntity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.OUTPUT,
      description: '碰撞的其他实体'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    
    if (entity && context.world && context.world.physicsWorld) {
      // 注册碰撞进入事件监听器
      context.world.physicsWorld.onCollisionEnter(entity, (otherEntity: any) => {
        this.setOutputValue('otherEntity', otherEntity);
        this.executeOutput('exec', context);
      });
    }
  }
}

/**
 * 碰撞退出事件节点
 */
export class OnCollisionExitNode extends Node {
  constructor() {
    super('physics/collision/onExit', NodeCategory.EVENT);
    
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.INPUT,
      description: '监听实体'
    });
    
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '碰撞退出时执行'
    });
    
    this.addOutput({
      name: 'otherEntity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.OUTPUT,
      description: '碰撞的其他实体'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    
    if (entity && context.world && context.world.physicsWorld) {
      // 注册碰撞退出事件监听器
      context.world.physicsWorld.onCollisionExit(entity, (otherEntity: any) => {
        this.setOutputValue('otherEntity', otherEntity);
        this.executeOutput('exec', context);
      });
    }
  }
}

// ==================== 软体物理节点 ====================

/**
 * 创建软体节点
 */
export class CreateSoftBodyNode extends Node {
  constructor() {
    super('physics/softbody/createSoftBody', NodeCategory.PHYSICS);
    
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });
    
    this.addInput({
      name: 'stiffness',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '刚度',
      defaultValue: 1.0
    });
    
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
    
    this.addOutput({
      name: 'softBody',
      type: SocketType.DATA,
      dataType: 'softBody',
      direction: SocketDirection.OUTPUT,
      description: '创建的软体'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const stiffness = this.getInputValue('stiffness', 1.0);
    
    if (entity && context.world && context.world.physicsWorld) {
      const softBody = context.world.physicsWorld.createSoftBody(entity, { stiffness });
      this.setOutputValue('softBody', softBody);
    }
    
    this.executeOutput('exec', context);
  }
}

/**
 * 设置阻尼节点
 */
export class SetDampingNode extends Node {
  constructor() {
    super('physics/softbody/setDamping', NodeCategory.PHYSICS);

    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'softBody',
      type: SocketType.DATA,
      dataType: 'softBody',
      direction: SocketDirection.INPUT,
      description: '软体对象'
    });

    this.addInput({
      name: 'damping',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '阻尼值'
    });

    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const softBody = this.getInputValue('softBody');
    const damping = this.getInputValue('damping', 0.1);

    if (softBody) {
      softBody.setDamping(damping);
    }

    this.executeOutput('exec', context);
  }
}

// ==================== 网络节点 ====================

/**
 * 断开连接节点
 */
export class DisconnectNode extends Node {
  constructor() {
    super('network/disconnect', NodeCategory.NETWORK);

    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'connection',
      direction: SocketDirection.INPUT,
      description: '网络连接'
    });

    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功断开'
    });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const connection = this.getInputValue('connection');

    let success = false;
    if (connection && connection.disconnect) {
      try {
        await connection.disconnect();
        success = true;
      } catch (error) {
        console.error('断开连接失败:', error);
      }
    }

    this.setOutputValue('success', success);
    this.executeOutput('exec', context);
  }
}

// ==================== 动画节点 ====================

/**
 * 播放动画节点
 */
export class PlayAnimationNode extends Node {
  constructor() {
    super('animation/playAnimation', NodeCategory.ANIMATION);

    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'animationName',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '动画名称'
    });

    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const animationName = this.getInputValue('animationName', '');

    if (entity && entity.animationComponent && animationName) {
      entity.animationComponent.play(animationName);
    }

    this.executeOutput('exec', context);
  }
}

/**
 * 停止动画节点
 */
export class StopAnimationNode extends Node {
  constructor() {
    super('animation/stopAnimation', NodeCategory.ANIMATION);

    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');

    if (entity && entity.animationComponent) {
      entity.animationComponent.stop();
    }

    this.executeOutput('exec', context);
  }
}

/**
 * 设置刚度节点
 */
export class SetStiffnessNode extends Node {
  constructor() {
    super('physics/softbody/setStiffness', NodeCategory.PHYSICS);
    
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    this.addInput({
      name: 'softBody',
      type: SocketType.DATA,
      dataType: 'softBody',
      direction: SocketDirection.INPUT,
      description: '软体对象'
    });
    
    this.addInput({
      name: 'stiffness',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '刚度值'
    });
    
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const softBody = this.getInputValue('softBody');
    const stiffness = this.getInputValue('stiffness', 1.0);
    
    if (softBody) {
      softBody.setStiffness(stiffness);
    }
    
    this.executeOutput('exec', context);
  }
}
