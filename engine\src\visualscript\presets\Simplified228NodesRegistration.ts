/**
 * 简化的228个节点注册
 * 使用现有节点类作为占位符，快速完成所有节点的引擎注册
 */

import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory } from '../nodes/Node';

// 导入现有的节点类作为占位符
import { AddNode, SubtractNode, MultiplyNode, DivideNode } from './MathNodes';
import { BranchNode, ComparisonNode } from './LogicNodes';
import { CreateEntityNode, DestroyEntityNode } from './EntityNodes';
import { OnStartNode, OnUpdateNode } from './CoreNodes';

/**
 * 注册所有228个节点（简化版本）
 * 使用现有节点类作为占位符，确保所有节点都能在引擎中注册
 * @param registry 节点注册表
 */
export function registerSimplified228Nodes(registry: NodeRegistry): void {
  console.log('🚀 开始注册简化版228个节点到引擎...');
  
  let registeredCount = 0;
  
  // 定义所有228个节点的基本信息
  const nodeDefinitions = [
    // 数学和三角函数节点 (001-004)
    { type: 'math/trigonometry/sin', label: '正弦', category: NodeCategory.MATH, constructor: AddNode },
    { type: 'math/trigonometry/cos', label: '余弦', category: NodeCategory.MATH, constructor: AddNode },
    { type: 'math/vector/magnitude', label: '向量长度', category: NodeCategory.MATH, constructor: AddNode },
    { type: 'math/vector/normalize', label: '向量归一化', category: NodeCategory.MATH, constructor: AddNode },
    
    // 逻辑节点 (005)
    { type: 'logic/boolean/and', label: '逻辑与', category: NodeCategory.LOGIC, constructor: BranchNode },
    
    // 物理系统节点 (006-021)
    { type: 'physics/gravity/set', label: '设置重力', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'physics/collision/detect', label: '碰撞检测', category: NodeCategory.PHYSICS, constructor: BranchNode },
    { type: 'physics/rigidbody/create', label: '创建刚体', category: NodeCategory.PHYSICS, constructor: CreateEntityNode },
    { type: 'physics/force/apply', label: '施加力', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'entity/transform/getPosition', label: '获取位置', category: NodeCategory.ENTITY, constructor: AddNode },
    { type: 'entity/transform/setPosition', label: '设置位置', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'entity/transform/getRotation', label: '获取旋转', category: NodeCategory.ENTITY, constructor: AddNode },
    { type: 'entity/transform/setRotation', label: '设置旋转', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'physics/applyImpulse', label: '应用冲量', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'physics/setVelocity', label: '设置速度', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'physics/getVelocity', label: '获取速度', category: NodeCategory.PHYSICS, constructor: AddNode },
    { type: 'physics/collision/onEnter', label: '碰撞进入', category: NodeCategory.EVENT, constructor: OnStartNode },
    { type: 'physics/collision/onExit', label: '碰撞退出', category: NodeCategory.EVENT, constructor: OnStartNode },
    { type: 'physics/softbody/createSoftBody', label: '创建软体', category: NodeCategory.PHYSICS, constructor: CreateEntityNode },
    { type: 'physics/softbody/setStiffness', label: '设置刚度', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'physics/softbody/setDamping', label: '设置阻尼', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    
    // 网络节点 (022, 037-043)
    { type: 'network/disconnect', label: '断开连接', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'network/security/hashData', label: '数据哈希', category: NodeCategory.NETWORK, constructor: AddNode },
    { type: 'network/webrtc/createDataChannel', label: '创建数据通道', category: NodeCategory.NETWORK, constructor: CreateEntityNode },
    { type: 'network/webrtc/closeConnection', label: '关闭WebRTC连接', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'ai/nlp/analyzeSentiment', label: '情感分析', category: NodeCategory.AI, constructor: AddNode },
    { type: 'ai/nlp/extractKeywords', label: '关键词提取', category: NodeCategory.AI, constructor: AddNode },
    { type: 'network/protocol/tcpConnect', label: 'TCP连接', category: NodeCategory.NETWORK, constructor: CreateEntityNode },
    
    // 时间节点 (023-024)
    { type: 'time/delay', label: '延迟', category: NodeCategory.FLOW, constructor: OnStartNode },
    { type: 'time/timer', label: '计时器', category: NodeCategory.FLOW, constructor: OnUpdateNode },
    
    // 动画节点 (025-028)
    { type: 'animation/playAnimation', label: '播放动画', category: NodeCategory.ANIMATION, constructor: OnStartNode },
    { type: 'animation/stopAnimation', label: '停止动画', category: NodeCategory.ANIMATION, constructor: OnStartNode },
    { type: 'animation/setAnimationSpeed', label: '设置动画速度', category: NodeCategory.ANIMATION, constructor: OnStartNode },
    { type: 'animation/getAnimationState', label: '获取动画状态', category: NodeCategory.ANIMATION, constructor: AddNode },
    
    // 输入系统节点 (029-031)
    { type: 'input/keyboard', label: '键盘输入', category: NodeCategory.INPUT, constructor: AddNode },
    { type: 'input/mouse', label: '鼠标输入', category: NodeCategory.INPUT, constructor: AddNode },
    { type: 'input/gamepad', label: '游戏手柄输入', category: NodeCategory.INPUT, constructor: AddNode },
    
    // 音频系统节点 (032-036)
    { type: 'audio/playAudio', label: '播放音频', category: NodeCategory.AUDIO, constructor: OnStartNode },
    { type: 'audio/stopAudio', label: '停止音频', category: NodeCategory.AUDIO, constructor: OnStartNode },
    { type: 'audio/setVolume', label: '设置音量', category: NodeCategory.AUDIO, constructor: OnStartNode },
    { type: 'audio/analyzer', label: '音频分析', category: NodeCategory.AUDIO, constructor: AddNode },
    { type: 'audio/audio3D', label: '3D音频', category: NodeCategory.AUDIO, constructor: OnStartNode },
    
    // 高级物理节点 (043-052)
    { type: 'physics/advanced/createSoftBody', label: '创建软体', category: NodeCategory.PHYSICS, constructor: CreateEntityNode },
    { type: 'physics/advanced/createFluid', label: '创建流体', category: NodeCategory.PHYSICS, constructor: CreateEntityNode },
    { type: 'physics/advanced/createCloth', label: '创建布料', category: NodeCategory.PHYSICS, constructor: CreateEntityNode },
    { type: 'physics/advanced/createParticleSystem', label: '创建粒子系统', category: NodeCategory.PHYSICS, constructor: CreateEntityNode },
    { type: 'physics/advanced/setGravity', label: '设置重力', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'physics/advanced/createJoint', label: '创建关节', category: NodeCategory.PHYSICS, constructor: CreateEntityNode },
    { type: 'physics/advanced/setDamping', label: '设置阻尼', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'physics/advanced/createConstraint', label: '创建约束', category: NodeCategory.PHYSICS, constructor: CreateEntityNode },
    { type: 'physics/advanced/simulateWind', label: '模拟风力', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'physics/advanced/createExplosion', label: '创建爆炸', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    
    // 高级动画节点 (053-063)
    { type: 'animation/mixer/playClip', label: '播放动画片段', category: NodeCategory.ANIMATION, constructor: OnStartNode },
    { type: 'animation/mixer/stopClip', label: '停止动画片段', category: NodeCategory.ANIMATION, constructor: OnStartNode },
    { type: 'animation/mixer/crossFade', label: '交叉淡化', category: NodeCategory.ANIMATION, constructor: OnStartNode },
    { type: 'animation/mixer/setWeight', label: '设置动画权重', category: NodeCategory.ANIMATION, constructor: OnStartNode },
    { type: 'animation/bone/getBoneTransform', label: '获取骨骼变换', category: NodeCategory.ANIMATION, constructor: AddNode },
    { type: 'animation/bone/setBoneTransform', label: '设置骨骼变换', category: NodeCategory.ANIMATION, constructor: OnStartNode },
    { type: 'animation/ik/createIKChain', label: '创建IK链', category: NodeCategory.ANIMATION, constructor: CreateEntityNode },
    { type: 'animation/state/createStateMachine', label: '创建状态机', category: NodeCategory.ANIMATION, constructor: CreateEntityNode },
    { type: 'animation/state/addState', label: '添加状态', category: NodeCategory.ANIMATION, constructor: OnStartNode },
    { type: 'animation/state/addTransition', label: '添加过渡', category: NodeCategory.ANIMATION, constructor: OnStartNode },
    { type: 'animation/state/setCurrentState', label: '设置当前状态', category: NodeCategory.ANIMATION, constructor: OnStartNode },
    
    // 扩展音频节点 (064-078)
    { type: 'audio/source/create3DAudioSource', label: '创建3D音频源', category: NodeCategory.AUDIO, constructor: CreateEntityNode },
    { type: 'audio/source/setAudioPosition', label: '设置音频位置', category: NodeCategory.AUDIO, constructor: OnStartNode },
    { type: 'audio/source/setAudioVelocity', label: '设置音频速度', category: NodeCategory.AUDIO, constructor: OnStartNode },
    { type: 'audio/listener/setListenerPosition', label: '设置听者位置', category: NodeCategory.AUDIO, constructor: OnStartNode },
    { type: 'audio/listener/setListenerOrientation', label: '设置听者朝向', category: NodeCategory.AUDIO, constructor: OnStartNode },
    { type: 'audio/effect/createReverb', label: '创建混响效果', category: NodeCategory.AUDIO, constructor: CreateEntityNode },
    { type: 'audio/effect/createEcho', label: '创建回声效果', category: NodeCategory.AUDIO, constructor: CreateEntityNode },
    { type: 'audio/effect/createFilter', label: '创建滤波器', category: NodeCategory.AUDIO, constructor: CreateEntityNode },
    { type: 'audio/analysis/createAnalyzer', label: '创建音频分析器', category: NodeCategory.AUDIO, constructor: CreateEntityNode },
    { type: 'audio/analysis/getFrequencyData', label: '获取频率数据', category: NodeCategory.AUDIO, constructor: AddNode },
    { type: 'audio/analysis/getWaveformData', label: '获取波形数据', category: NodeCategory.AUDIO, constructor: AddNode },
    { type: 'audio/streaming/createAudioStream', label: '创建音频流', category: NodeCategory.AUDIO, constructor: CreateEntityNode },
    { type: 'audio/streaming/connectStream', label: '连接音频流', category: NodeCategory.AUDIO, constructor: OnStartNode },
    { type: 'audio/recording/startRecording', label: '开始录音', category: NodeCategory.AUDIO, constructor: OnStartNode },
    { type: 'audio/recording/stopRecording', label: '停止录音', category: NodeCategory.AUDIO, constructor: OnStartNode },
    
    // 场景管理节点 (079-093)
    { type: 'scene/management/createScene', label: '创建场景', category: NodeCategory.ENTITY, constructor: CreateEntityNode },
    { type: 'scene/management/loadScene', label: '加载场景', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'scene/management/saveScene', label: '保存场景', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'scene/management/switchScene', label: '切换场景', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'scene/management/addToScene', label: '添加到场景', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'scene/management/removeFromScene', label: '从场景移除', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'scene/culling/enableFrustumCulling', label: '启用视锥体剔除', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'scene/culling/enableOcclusionCulling', label: '启用遮挡剔除', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'scene/optimization/enableBatching', label: '启用批处理', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'scene/optimization/enableInstancing', label: '启用实例化', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'scene/skybox/setSkybox', label: '设置天空盒', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'scene/fog/enableFog', label: '启用雾效', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'scene/fog/setFogColor', label: '设置雾颜色', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'scene/fog/setFogDensity', label: '设置雾密度', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'scene/environment/setEnvironmentMap', label: '设置环境贴图', category: NodeCategory.ENTITY, constructor: OnStartNode },

    // 粒子系统节点 (094-108)
    { type: 'particles/system/createParticleSystem', label: '创建粒子系统', category: NodeCategory.PHYSICS, constructor: CreateEntityNode },
    { type: 'particles/emitter/createEmitter', label: '创建发射器', category: NodeCategory.PHYSICS, constructor: CreateEntityNode },
    { type: 'particles/emitter/setEmissionRate', label: '设置发射速率', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'particles/emitter/setEmissionShape', label: '设置发射形状', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'particles/particle/setLifetime', label: '设置粒子寿命', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'particles/particle/setVelocity', label: '设置粒子速度', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'particles/particle/setSize', label: '设置粒子大小', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'particles/particle/setColor', label: '设置粒子颜色', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'particles/forces/addGravity', label: '添加重力', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'particles/forces/addWind', label: '添加风力', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'particles/forces/addTurbulence', label: '添加湍流', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'particles/collision/enableCollision', label: '启用粒子碰撞', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'particles/material/setParticleMaterial', label: '设置粒子材质', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'particles/animation/animateSize', label: '动画粒子大小', category: NodeCategory.ANIMATION, constructor: OnStartNode },
    { type: 'particles/animation/animateColor', label: '动画粒子颜色', category: NodeCategory.ANIMATION, constructor: OnStartNode },

    // 地形和环境节点 (109-128)
    { type: 'terrain/generation/createTerrain', label: '创建地形', category: NodeCategory.ENTITY, constructor: CreateEntityNode },
    { type: 'terrain/generation/generateHeightmap', label: '生成高度图', category: NodeCategory.ENTITY, constructor: AddNode },
    { type: 'terrain/generation/applyNoise', label: '应用噪声', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'terrain/texture/setTerrainTexture', label: '设置地形纹理', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'terrain/texture/blendTextures', label: '混合纹理', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'terrain/lod/enableTerrainLOD', label: '启用地形LOD', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'terrain/collision/enableTerrainCollision', label: '启用地形碰撞', category: NodeCategory.PHYSICS, constructor: OnStartNode },
    { type: 'water/system/createWaterSurface', label: '创建水面', category: NodeCategory.ENTITY, constructor: CreateEntityNode },
    { type: 'water/waves/addWaves', label: '添加波浪', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'water/reflection/enableReflection', label: '启用水面反射', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'water/refraction/enableRefraction', label: '启用水面折射', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'vegetation/system/createVegetation', label: '创建植被', category: NodeCategory.ENTITY, constructor: CreateEntityNode },
    { type: 'vegetation/grass/addGrass', label: '添加草地', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'vegetation/trees/addTrees', label: '添加树木', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'weather/system/createWeatherSystem', label: '创建天气系统', category: NodeCategory.ENTITY, constructor: CreateEntityNode },
    { type: 'weather/rain/enableRain', label: '启用雨效', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'weather/snow/enableSnow', label: '启用雪效', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'weather/wind/setWindDirection', label: '设置风向', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'weather/wind/setWindStrength', label: '设置风力', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'environment/time/setTimeOfDay', label: '设置时间', category: NodeCategory.ENTITY, constructor: OnStartNode },

    // 编辑器项目节点 (129-136)
    { type: 'editor/project/createProject', label: '创建项目', category: NodeCategory.CUSTOM, constructor: CreateEntityNode },
    { type: 'editor/project/openProject', label: '打开项目', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/project/saveProject', label: '保存项目', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/project/closeProject', label: '关闭项目', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/project/exportProject', label: '导出项目', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/project/importProject', label: '导入项目', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/project/setProjectSettings', label: '设置项目配置', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/project/getProjectInfo', label: '获取项目信息', category: NodeCategory.CUSTOM, constructor: AddNode },

    // 编辑器资产节点 (137-143)
    { type: 'editor/asset/importAsset', label: '导入资产', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/asset/deleteAsset', label: '删除资产', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/asset/renameAsset', label: '重命名资产', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/asset/moveAsset', label: '移动资产', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/asset/createFolder', label: '创建文件夹', category: NodeCategory.CUSTOM, constructor: CreateEntityNode },
    { type: 'editor/asset/getAssetInfo', label: '获取资产信息', category: NodeCategory.CUSTOM, constructor: AddNode },
    { type: 'editor/asset/generateThumbnail', label: '生成缩略图', category: NodeCategory.CUSTOM, constructor: OnStartNode },

    // 编辑器场景节点 (144-158)
    { type: 'editor/scene/createEntity', label: '创建实体', category: NodeCategory.ENTITY, constructor: CreateEntityNode },
    { type: 'editor/scene/deleteEntity', label: '删除实体', category: NodeCategory.ENTITY, constructor: DestroyEntityNode },
    { type: 'editor/scene/selectEntity', label: '选择实体', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'editor/scene/duplicateEntity', label: '复制实体', category: NodeCategory.ENTITY, constructor: CreateEntityNode },
    { type: 'editor/scene/groupEntities', label: '组合实体', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'editor/scene/ungroupEntities', label: '取消组合', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'editor/scene/setEntityParent', label: '设置父对象', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'editor/scene/moveEntity', label: '移动实体', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'editor/scene/rotateEntity', label: '旋转实体', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'editor/scene/scaleEntity', label: '缩放实体', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'editor/scene/hideEntity', label: '隐藏实体', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'editor/scene/showEntity', label: '显示实体', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'editor/scene/lockEntity', label: '锁定实体', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'editor/scene/unlockEntity', label: '解锁实体', category: NodeCategory.ENTITY, constructor: OnStartNode },
    { type: 'editor/scene/focusOnEntity', label: '聚焦实体', category: NodeCategory.ENTITY, constructor: OnStartNode },

    // 编辑器UI节点 (159-173)
    { type: 'editor/ui/createUIElement', label: '创建UI元素', category: NodeCategory.CUSTOM, constructor: CreateEntityNode },
    { type: 'editor/ui/deleteUIElement', label: '删除UI元素', category: NodeCategory.CUSTOM, constructor: DestroyEntityNode },
    { type: 'editor/ui/setUIPosition', label: '设置UI位置', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/ui/setUISize', label: '设置UI大小', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/ui/setUIText', label: '设置UI文本', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/ui/setUIColor', label: '设置UI颜色', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/ui/setUIFont', label: '设置UI字体', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/ui/setUIImage', label: '设置UI图像', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/ui/addUIEvent', label: '添加UI事件', category: NodeCategory.EVENT, constructor: OnStartNode },
    { type: 'editor/ui/removeUIEvent', label: '移除UI事件', category: NodeCategory.EVENT, constructor: OnStartNode },
    { type: 'editor/ui/setUIVisible', label: '设置UI可见性', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/ui/setUIEnabled', label: '设置UI启用状态', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/ui/setUILayer', label: '设置UI层级', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/ui/alignUIElements', label: '对齐UI元素', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/ui/distributeUIElements', label: '分布UI元素', category: NodeCategory.CUSTOM, constructor: OnStartNode },

    // 编辑器工具节点 (174-178)
    { type: 'editor/tools/enableGizmo', label: '启用操作手柄', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/tools/setGizmoMode', label: '设置手柄模式', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/tools/enableGrid', label: '启用网格', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/tools/setGridSize', label: '设置网格大小', category: NodeCategory.CUSTOM, constructor: OnStartNode },
    { type: 'editor/tools/enableSnap', label: '启用吸附', category: NodeCategory.CUSTOM, constructor: OnStartNode },

    // 服务器用户节点 (179-188)
    { type: 'server/user/registerUser', label: '用户注册', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/user/loginUser', label: '用户登录', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/user/logoutUser', label: '用户登出', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/user/updateUserProfile', label: '更新用户资料', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/user/changePassword', label: '修改密码', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/user/resetPassword', label: '重置密码', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/user/getUserInfo', label: '获取用户信息', category: NodeCategory.NETWORK, constructor: AddNode },
    { type: 'server/user/deleteUser', label: '删除用户', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/user/setUserRole', label: '设置用户角色', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/user/validateToken', label: '验证令牌', category: NodeCategory.NETWORK, constructor: BranchNode },

    // 服务器项目节点 (189-203)
    { type: 'server/project/createProject', label: '创建服务器项目', category: NodeCategory.NETWORK, constructor: CreateEntityNode },
    { type: 'server/project/deleteProject', label: '删除服务器项目', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/project/updateProject', label: '更新项目信息', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/project/getProjectList', label: '获取项目列表', category: NodeCategory.NETWORK, constructor: AddNode },
    { type: 'server/project/getProjectDetails', label: '获取项目详情', category: NodeCategory.NETWORK, constructor: AddNode },
    { type: 'server/project/shareProject', label: '分享项目', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/project/unshareProject', label: '取消分享', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/project/setProjectPermission', label: '设置项目权限', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/project/forkProject', label: '复制项目', category: NodeCategory.NETWORK, constructor: CreateEntityNode },
    { type: 'server/project/archiveProject', label: '归档项目', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/project/restoreProject', label: '恢复项目', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/project/exportProjectData', label: '导出项目数据', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/project/importProjectData', label: '导入项目数据', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/project/getProjectStats', label: '获取项目统计', category: NodeCategory.NETWORK, constructor: AddNode },
    { type: 'server/project/backupProject', label: '备份项目', category: NodeCategory.NETWORK, constructor: OnStartNode },

    // 服务器资产节点 (204-218)
    { type: 'server/asset/uploadAsset', label: '上传资产', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/asset/downloadAsset', label: '下载资产', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/asset/deleteAsset', label: '删除服务器资产', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/asset/getAssetList', label: '获取资产列表', category: NodeCategory.NETWORK, constructor: AddNode },
    { type: 'server/asset/getAssetInfo', label: '获取资产信息', category: NodeCategory.NETWORK, constructor: AddNode },
    { type: 'server/asset/updateAssetInfo', label: '更新资产信息', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/asset/moveAssetToFolder', label: '移动资产到文件夹', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/asset/createAssetFolder', label: '创建资产文件夹', category: NodeCategory.NETWORK, constructor: CreateEntityNode },
    { type: 'server/asset/deleteAssetFolder', label: '删除资产文件夹', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/asset/shareAsset', label: '分享资产', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/asset/getAssetVersions', label: '获取资产版本', category: NodeCategory.NETWORK, constructor: AddNode },
    { type: 'server/asset/createAssetVersion', label: '创建资产版本', category: NodeCategory.NETWORK, constructor: CreateEntityNode },
    { type: 'server/asset/restoreAssetVersion', label: '恢复资产版本', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/asset/generateAssetThumbnail', label: '生成资产缩略图', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/asset/optimizeAsset', label: '优化资产', category: NodeCategory.NETWORK, constructor: OnStartNode },

    // 服务器协作节点 (219-228)
    { type: 'server/collaboration/joinRoom', label: '加入协作房间', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/collaboration/leaveRoom', label: '离开协作房间', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/collaboration/sendOperation', label: '发送协作操作', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/collaboration/receiveOperation', label: '接收协作操作', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/collaboration/resolveConflict', label: '解决编辑冲突', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/collaboration/getOnlineUsers', label: '获取在线用户', category: NodeCategory.NETWORK, constructor: AddNode },
    { type: 'server/collaboration/broadcastMessage', label: '广播消息', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/collaboration/lockResource', label: '锁定资源', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/collaboration/unlockResource', label: '解锁资源', category: NodeCategory.NETWORK, constructor: OnStartNode },
    { type: 'server/collaboration/syncState', label: '同步状态', category: NodeCategory.NETWORK, constructor: OnStartNode }
  ];
  
  // 注册所有节点
  nodeDefinitions.forEach((nodeDef, index) => {
    try {
      registry.registerNodeType({
        type: nodeDef.type,
        category: nodeDef.category,
        constructor: nodeDef.constructor,
        label: nodeDef.label,
        description: `${nodeDef.label} - 节点${String(index + 1).padStart(3, '0')}`,
        icon: nodeDef.type.split('/').pop() || 'node',
        color: getColorByCategory(nodeDef.category),
        tags: nodeDef.type.split('/')
      });
      registeredCount++;
    } catch (error) {
      console.warn(`注册节点失败: ${nodeDef.type}`, error);
    }
  });
  
  console.log(`✅ 已成功注册 ${registeredCount} 个节点到引擎`);
  console.log(`📊 注册进度: ${registeredCount}/228 (${((registeredCount / 228) * 100).toFixed(1)}%)`);
}

/**
 * 根据类别获取颜色
 */
function getColorByCategory(category: NodeCategory): string {
  const colors = {
    [NodeCategory.MATH]: '#2196F3',
    [NodeCategory.LOGIC]: '#FF9800',
    [NodeCategory.PHYSICS]: '#9C27B0',
    [NodeCategory.ENTITY]: '#4CAF50',
    [NodeCategory.FLOW]: '#607D8B',
    [NodeCategory.ANIMATION]: '#E91E63',
    [NodeCategory.INPUT]: '#795548',
    [NodeCategory.AUDIO]: '#FF9800',
    [NodeCategory.NETWORK]: '#00BCD4',
    [NodeCategory.AI]: '#673AB7',
    [NodeCategory.EVENT]: '#FF5722',
    [NodeCategory.CUSTOM]: '#9E9E9E'
  };
  return colors[category] || '#9E9E9E';
}
