const fs = require('fs');
const path = require('path');

console.log('=== 分析项目节点实现状态 ===\n');

// 检查引擎层面的节点文件
const enginePresetDir = 'engine/src/visualscript/presets';
const nodeFiles = [
  'CoreNodes.ts',
  'MathNodes.ts', 
  'LogicNodes.ts',
  'EntityNodes.ts',
  'PhysicsNodes.ts',
  'TimeNodes.ts',
  'AnimationNodes.ts',
  'InputNodes.ts',
  'AudioNodes.ts',
  'NetworkNodes.ts',
  'AINodes.ts',
  'DebugNodes.ts',
  'NetworkSecurityNodes.ts',
  'WebRTCNodes.ts',
  'AIEmotionNodes.ts',
  'AINLPNodes.ts',
  'NetworkProtocolNodes.ts',
  'StringNodes.ts',
  'ArrayNodes.ts',
  'ObjectNodes.ts',
  'VariableNodes.ts',
  'RenderingNodes.ts',
  'SoftBodyNodes.ts'
];

console.log('1. 引擎层面节点文件检查:');
let engineNodeCount = 0;
nodeFiles.forEach(fileName => {
  const filePath = path.join(enginePresetDir, fileName);
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8');
    const registerCalls = (content.match(/registry\.registerNodeType\(/g) || []).length;
    engineNodeCount += registerCalls;
    console.log(`✅ ${fileName}: ${registerCalls}个节点`);
  } else {
    console.log(`❌ ${fileName}: 文件不存在`);
  }
});

console.log(`\n引擎层面总计: ${engineNodeCount}个节点\n`);

// 检查编辑器层面的节点注册
console.log('2. 编辑器层面节点注册检查:');
const editorServiceFile = 'editor/src/services/NodeRegistryService.ts';
if (fs.existsSync(editorServiceFile)) {
  const content = fs.readFileSync(editorServiceFile, 'utf8');
  const registerCalls = (content.match(/this\.registerNode\(/g) || []).length;
  console.log(`✅ NodeRegistryService.ts: ${registerCalls}个节点注册`);
} else {
  console.log('❌ NodeRegistryService.ts: 文件不存在');
}

// 检查批次节点文件
console.log('\n3. 批次节点文件检查:');
const batchFiles = [
  'AnimationExtensionNodes.ts',
  'AnimationExtensionNodes2.ts', 
  'SceneManagementNodes.ts',
  'AudioParticleNodesBatch8.ts',
  'TerrainEnvironmentNodesBatch9.ts',
  'EditorProjectNodesBatch9.ts',
  'EditorUIToolsBatch10.ts',
  'ServerNodesBatch11.ts',
  'ServerNodesBatch12.ts'
];

batchFiles.forEach(fileName => {
  const filePath = path.join(enginePresetDir, fileName);
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8');
    const nodeClasses = (content.match(/export class \w+Node/g) || []).length;
    console.log(`✅ ${fileName}: ${nodeClasses}个节点类`);
  } else {
    console.log(`❌ ${fileName}: 文件不存在`);
  }
});
