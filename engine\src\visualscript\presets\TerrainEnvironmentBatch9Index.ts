/**
 * 第9批次地形与环境系统节点统一导出
 * 节点241-270：完整的30个节点实现
 */

// 地形与环境系统节点 (241-250)
export {
  EnableRefractionNode,
  CreateVegetationNode,
  AddGrassNode,
  AddTreesNode,
  CreateWeatherSystemNode,
  EnableRainNode,
  EnableSnowNode,
  SetWindDirectionNode,
  SetWindStrengthNode,
  SetTimeOfDayNode
} from './TerrainEnvironmentNodesBatch9';

// 编辑器项目管理节点 (251-270)
export {
  CreateProjectNode,
  OpenProjectNode,
  SaveProjectNode,
  CloseProjectNode,
  CreateProjectTemplateNode,
  ImportProjectNode,
  SetProjectSettingsNode,
  GetProjectInfoNode,
  AddAssetNode,
  RemoveAssetNode,
  GetAssetListNode,
  CreateSceneNode,
  DeleteSceneNode,
  GetSceneListNode,
  SetActiveSceneNode,
  GetActiveSceneNode,
  BuildProjectNode,
  PublishProjectNode,
  BackupProjectNode,
  RestoreProjectNode
} from './EditorProjectNodesBatch9';

/**
 * 第9批次节点映射表
 * 用于节点注册和类型检查
 */
export const BATCH9_NODE_MAPPING = {
  // 地形与环境系统节点 (241-250)
  'water/refraction/enableRefraction': 'EnableRefractionNode',
  'vegetation/system/createVegetation': 'CreateVegetationNode',
  'vegetation/grass/addGrass': 'AddGrassNode',
  'vegetation/trees/addTrees': 'AddTreesNode',
  'weather/system/createWeatherSystem': 'CreateWeatherSystemNode',
  'weather/rain/enableRain': 'EnableRainNode',
  'weather/snow/enableSnow': 'EnableSnowNode',
  'weather/wind/setWindDirection': 'SetWindDirectionNode',
  'weather/wind/setWindStrength': 'SetWindStrengthNode',
  'environment/time/setTimeOfDay': 'SetTimeOfDayNode',

  // 编辑器项目管理节点 (251-270)
  'editor/project/createProject': 'CreateProjectNode',
  'editor/project/openProject': 'OpenProjectNode',
  'editor/project/saveProject': 'SaveProjectNode',
  'editor/project/closeProject': 'CloseProjectNode',
  'editor/project/createProjectTemplate': 'CreateProjectTemplateNode',
  'editor/project/importProject': 'ImportProjectNode',
  'editor/project/setProjectSettings': 'SetProjectSettingsNode',
  'editor/project/getProjectInfo': 'GetProjectInfoNode',
  'editor/asset/addAsset': 'AddAssetNode',
  'editor/asset/removeAsset': 'RemoveAssetNode',
  'editor/asset/getAssetList': 'GetAssetListNode',
  'editor/scene/createScene': 'CreateSceneNode',
  'editor/scene/deleteScene': 'DeleteSceneNode',
  'editor/scene/getSceneList': 'GetSceneListNode',
  'editor/scene/setActiveScene': 'SetActiveSceneNode',
  'editor/scene/getActiveScene': 'GetActiveSceneNode',
  'editor/project/buildProject': 'BuildProjectNode',
  'editor/project/publishProject': 'PublishProjectNode',
  'editor/project/backupProject': 'BackupProjectNode',
  'editor/project/restoreProject': 'RestoreProjectNode'
};

/**
 * 第9批次节点类别分组
 */
export const BATCH9_NODE_CATEGORIES = {
  WATER_EFFECTS: [
    'water/refraction/enableRefraction'
  ],
  VEGETATION_SYSTEM: [
    'vegetation/system/createVegetation',
    'vegetation/grass/addGrass',
    'vegetation/trees/addTrees'
  ],
  WEATHER_SYSTEM: [
    'weather/system/createWeatherSystem',
    'weather/rain/enableRain',
    'weather/snow/enableSnow',
    'weather/wind/setWindDirection',
    'weather/wind/setWindStrength'
  ],
  ENVIRONMENT_CONTROL: [
    'environment/time/setTimeOfDay'
  ],
  PROJECT_MANAGEMENT: [
    'editor/project/createProject',
    'editor/project/openProject',
    'editor/project/saveProject',
    'editor/project/closeProject',
    'editor/project/createProjectTemplate',
    'editor/project/importProject',
    'editor/project/setProjectSettings',
    'editor/project/getProjectInfo',
    'editor/project/buildProject',
    'editor/project/publishProject',
    'editor/project/backupProject',
    'editor/project/restoreProject'
  ],
  ASSET_MANAGEMENT: [
    'editor/asset/addAsset',
    'editor/asset/removeAsset',
    'editor/asset/getAssetList'
  ],
  SCENE_MANAGEMENT: [
    'editor/scene/createScene',
    'editor/scene/deleteScene',
    'editor/scene/getSceneList',
    'editor/scene/setActiveScene',
    'editor/scene/getActiveScene'
  ]
};

/**
 * 获取第9批次所有节点类型
 */
export function getBatch9NodeTypes(): string[] {
  return Object.keys(BATCH9_NODE_MAPPING);
}

/**
 * 获取第9批次节点总数
 */
export function getBatch9NodeCount(): number {
  return Object.keys(BATCH9_NODE_MAPPING).length;
}

/**
 * 验证第9批次节点完整性
 */
export function validateBatch9Nodes(): boolean {
  const expectedCount = 30;
  const actualCount = getBatch9NodeCount();
  
  if (actualCount !== expectedCount) {
    console.error(`第9批次节点数量不匹配: 期望 ${expectedCount}, 实际 ${actualCount}`);
    return false;
  }
  
  console.log(`第9批次节点验证通过: ${actualCount} 个节点`);
  return true;
}

/**
 * 第9批次节点描述信息
 */
export const BATCH9_NODE_DESCRIPTIONS = {
  'water/refraction/enableRefraction': '启用水面折射效果',
  'vegetation/system/createVegetation': '创建植被系统',
  'vegetation/grass/addGrass': '添加草地植被',
  'vegetation/trees/addTrees': '添加树木植被',
  'weather/system/createWeatherSystem': '创建动态天气系统',
  'weather/rain/enableRain': '启用雨天效果',
  'weather/snow/enableSnow': '启用雪天效果',
  'weather/wind/setWindDirection': '设置环境风向',
  'weather/wind/setWindStrength': '设置环境风力强度',
  'environment/time/setTimeOfDay': '设置环境时间',
  'editor/project/createProject': '创建新的编辑器项目',
  'editor/project/openProject': '打开现有项目',
  'editor/project/saveProject': '保存当前项目',
  'editor/project/closeProject': '关闭当前项目',
  'editor/project/createProjectTemplate': '创建项目模板',
  'editor/project/importProject': '导入项目文件',
  'editor/project/setProjectSettings': '配置项目参数',
  'editor/project/getProjectInfo': '获取项目基本信息',
  'editor/asset/addAsset': '向项目添加资产',
  'editor/asset/removeAsset': '从项目移除资产',
  'editor/asset/getAssetList': '获取项目中的所有资产',
  'editor/scene/createScene': '在项目中创建新场景',
  'editor/scene/deleteScene': '从项目中删除场景',
  'editor/scene/getSceneList': '获取项目中的所有场景',
  'editor/scene/setActiveScene': '设置项目的活动场景',
  'editor/scene/getActiveScene': '获取项目的活动场景',
  'editor/project/buildProject': '构建项目为可部署格式',
  'editor/project/publishProject': '发布项目到指定平台',
  'editor/project/backupProject': '创建项目备份',
  'editor/project/restoreProject': '从备份恢复项目'
};
