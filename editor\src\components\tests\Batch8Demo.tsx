/**
 * 第8批次节点演示组件
 * 展示音频与粒子系统节点的功能
 */

import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Divider, Tag, Typography, Row, Col, Statistic } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  ReloadOutlined,
  <PERSON>boltOutlined,
  CloudOutlined,
  EnvironmentOutlined
} from '@ant-design/icons';
import { nodeRegistryService } from '../../services/NodeRegistryService';
import { engineNodeIntegration } from '../../services/EngineNodeIntegration';

const { Title, Paragraph, Text } = Typography;

interface NodeDemoProps {
  nodeType: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  onExecute: () => void;
}

const NodeDemo: React.FC<NodeDemoProps> = ({ 
  nodeType, 
  title, 
  description, 
  icon, 
  color, 
  onExecute 
}) => {
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionCount, setExecutionCount] = useState(0);

  const handleExecute = async () => {
    setIsExecuting(true);
    try {
      await onExecute();
      setExecutionCount(prev => prev + 1);
    } catch (error) {
      console.error(`执行节点 ${nodeType} 失败:`, error);
    } finally {
      setIsExecuting(false);
    }
  };

  return (
    <Card
      size="small"
      title={
        <Space>
          {icon}
          <Text strong>{title}</Text>
          <Tag color={color}>{nodeType}</Tag>
        </Space>
      }
      extra={
        <Button
          type="primary"
          size="small"
          icon={isExecuting ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
          loading={isExecuting}
          onClick={handleExecute}
        >
          {isExecuting ? '执行中' : '执行'}
        </Button>
      }
      style={{ marginBottom: 16 }}
    >
      <Paragraph style={{ margin: 0, fontSize: '12px' }}>
        {description}
      </Paragraph>
      <div style={{ marginTop: 8 }}>
        <Text type="secondary" style={{ fontSize: '11px' }}>
          执行次数: {executionCount}
        </Text>
      </div>
    </Card>
  );
};

export const Batch8Demo: React.FC = () => {
  const [nodeStats, setNodeStats] = useState<any>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // 初始化统计信息
    const stats = nodeRegistryService.getNodeStatistics();
    setNodeStats(stats);
    setIsInitialized(engineNodeIntegration.getInitializationStatus());
  }, []);

  const executeSceneNode = async (nodeType: string, inputs: any = {}) => {
    console.log(`执行场景节点: ${nodeType}`, inputs);
    // 这里可以添加实际的节点执行逻辑
    return new Promise(resolve => setTimeout(resolve, 500));
  };

  const executeParticleNode = async (nodeType: string, inputs: any = {}) => {
    console.log(`执行粒子节点: ${nodeType}`, inputs);
    // 这里可以添加实际的节点执行逻辑
    return new Promise(resolve => setTimeout(resolve, 800));
  };

  const executeTerrainNode = async (nodeType: string, inputs: any = {}) => {
    console.log(`执行地形节点: ${nodeType}`, inputs);
    // 这里可以添加实际的节点执行逻辑
    return new Promise(resolve => setTimeout(resolve, 1000));
  };

  const refreshStats = () => {
    const stats = nodeRegistryService.getNodeStatistics();
    setNodeStats(stats);
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>第8批次节点演示 - 音频与粒子系统</Title>
      
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Statistic 
            title="总节点数" 
            value={nodeStats?.totalNodes || 0} 
            prefix={<ThunderboltOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic 
            title="粒子节点" 
            value={nodeStats?.tagCounts?.['粒子'] || 0}
            valueStyle={{ color: '#FF6347' }}
          />
        </Col>
        <Col span={6}>
          <Statistic 
            title="场景节点" 
            value={nodeStats?.tagCounts?.['场景'] || 0}
            valueStyle={{ color: '#87CEEB' }}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="地形水体节点"
            value={(nodeStats?.tagCounts?.['地形'] || 0) + (nodeStats?.tagCounts?.['水体'] || 0)}
            valueStyle={{ color: '#8B4513' }}
          />
        </Col>
      </Row>

      <Space style={{ marginBottom: 16 }}>
        <Button 
          icon={<ReloadOutlined />} 
          onClick={refreshStats}
        >
          刷新统计
        </Button>
        <Tag color={isInitialized ? 'green' : 'red'}>
          引擎集成状态: {isInitialized ? '已初始化' : '未初始化'}
        </Tag>
      </Space>

      <Divider>场景环境节点 (211-215)</Divider>
      
      <Row gutter={16}>
        <Col span={12}>
          <NodeDemo
            nodeType="scene/skybox/setSkybox"
            title="设置天空盒"
            description="设置场景天空盒，创建逼真的环境背景"
            icon={<CloudOutlined />}
            color="#87CEEB"
            onExecute={() => executeSceneNode('scene/skybox/setSkybox', {
              scene: { name: 'demo-scene' },
              skyboxTexture: 'default-skybox'
            })}
          />
        </Col>
        <Col span={12}>
          <NodeDemo
            nodeType="scene/fog/enableFog"
            title="启用雾效"
            description="启用场景雾效果，增强视觉深度感"
            icon={<CloudOutlined />}
            color="#B0C4DE"
            onExecute={() => executeSceneNode('scene/fog/enableFog', {
              scene: { name: 'demo-scene' },
              color: 0xcccccc,
              near: 1,
              far: 1000
            })}
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <NodeDemo
            nodeType="scene/environment/setEnvironmentMap"
            title="设置环境贴图"
            description="设置IBL环境贴图，提供真实的环境光照"
            icon={<EnvironmentOutlined />}
            color="#32CD32"
            onExecute={() => executeSceneNode('scene/environment/setEnvironmentMap', {
              scene: { name: 'demo-scene' },
              environmentMap: 'hdr-environment',
              intensity: 1.0
            })}
          />
        </Col>
      </Row>

      <Divider>粒子系统节点 (216-230)</Divider>
      
      <Row gutter={16}>
        <Col span={8}>
          <NodeDemo
            nodeType="particles/system/createParticleSystem"
            title="创建粒子系统"
            description="创建粒子效果系统，支持大量粒子渲染"
            icon={<ThunderboltOutlined />}
            color="#FF6347"
            onExecute={() => executeParticleNode('particles/system/createParticleSystem', {
              maxParticles: 1000
            })}
          />
        </Col>
        <Col span={8}>
          <NodeDemo
            nodeType="particles/emitter/createEmitter"
            title="创建发射器"
            description="创建粒子发射器，控制粒子生成"
            icon={<ThunderboltOutlined />}
            color="#FF6347"
            onExecute={() => executeParticleNode('particles/emitter/createEmitter', {
              position: { x: 0, y: 0, z: 0 },
              rate: 10
            })}
          />
        </Col>
        <Col span={8}>
          <NodeDemo
            nodeType="particles/forces/addGravity"
            title="添加重力"
            description="为粒子添加重力影响，模拟真实物理"
            icon={<ThunderboltOutlined />}
            color="#8A2BE2"
            onExecute={() => executeParticleNode('particles/forces/addGravity', {
              particleSystem: { name: 'demo-particles' },
              gravity: { x: 0, y: -9.8, z: 0 }
            })}
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={8}>
          <NodeDemo
            nodeType="particles/animation/animateSize"
            title="动画粒子大小"
            description="创建粒子大小动画，实现动态效果"
            icon={<ThunderboltOutlined />}
            color="#FF1493"
            onExecute={() => executeParticleNode('particles/animation/animateSize', {
              particleSystem: { name: 'demo-particles' },
              startSize: 0.1,
              endSize: 2.0,
              duration: 1.0
            })}
          />
        </Col>
        <Col span={8}>
          <NodeDemo
            nodeType="particles/animation/animateColor"
            title="动画粒子颜色"
            description="创建粒子颜色动画，丰富视觉效果"
            icon={<ThunderboltOutlined />}
            color="#FF1493"
            onExecute={() => executeParticleNode('particles/animation/animateColor', {
              particleSystem: { name: 'demo-particles' },
              startColor: 0xffffff,
              endColor: 0x000000,
              duration: 1.0
            })}
          />
        </Col>
      </Row>

      <Divider>地形和水体系统节点 (231-240)</Divider>
      
      <Row gutter={16}>
        <Col span={12}>
          <NodeDemo
            nodeType="terrain/generation/createTerrain"
            title="创建地形"
            description="创建地形网格，构建3D世界基础"
            icon={<EnvironmentOutlined />}
            color="#8B4513"
            onExecute={() => executeTerrainNode('terrain/generation/createTerrain', {
              width: 100,
              height: 100,
              segments: 64
            })}
          />
        </Col>
        <Col span={12}>
          <NodeDemo
            nodeType="terrain/generation/generateHeightmap"
            title="生成高度图"
            description="生成地形高度图，创建起伏地形"
            icon={<EnvironmentOutlined />}
            color="#8B4513"
            onExecute={() => executeTerrainNode('terrain/generation/generateHeightmap', {
              terrain: { name: 'demo-terrain' },
              scale: 10,
              octaves: 4
            })}
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={8}>
          <NodeDemo
            nodeType="water/system/createWaterSurface"
            title="创建水面"
            description="创建水体表面，构建逼真的水体效果"
            icon={<EnvironmentOutlined />}
            color="#006994"
            onExecute={() => executeTerrainNode('water/system/createWaterSurface', {
              width: 100,
              height: 100,
              color: 0x006994
            })}
          />
        </Col>
        <Col span={8}>
          <NodeDemo
            nodeType="water/waves/addWaves"
            title="添加波浪"
            description="为水面添加波浪效果，增强动态感"
            icon={<EnvironmentOutlined />}
            color="#006994"
            onExecute={() => executeTerrainNode('water/waves/addWaves', {
              waterSurface: { name: 'demo-water' },
              waveHeight: 1.0,
              waveSpeed: 1.0,
              waveFrequency: 0.1
            })}
          />
        </Col>
        <Col span={8}>
          <NodeDemo
            nodeType="water/reflection/enableReflection"
            title="启用水面反射"
            description="启用水面反射效果，提升视觉真实感"
            icon={<EnvironmentOutlined />}
            color="#006994"
            onExecute={() => executeTerrainNode('water/reflection/enableReflection', {
              waterSurface: { name: 'demo-water' },
              reflectionIntensity: 0.8,
              reflectionQuality: 'medium'
            })}
          />
        </Col>
      </Row>

      <Divider />
      
      <Paragraph>
        <Text strong>第8批次节点开发完成情况：</Text>
        <br />
        ✅ 高级音频节点 (211-215): 5个节点 - 场景环境音频控制
        <br />
        ✅ 粒子系统节点 (216-230): 15个节点 - 完整的粒子特效系统
        <br />
        ✅ 地形系统节点 (231-237): 7个节点 - 完整的地形生成和处理功能
        <br />
        ✅ 水体系统节点 (238-240): 3个节点 - 水面创建和效果系统
        <br />
        <Text type="success">总计: 30个节点已完成开发和集成</Text>
      </Paragraph>
    </div>
  );
};
