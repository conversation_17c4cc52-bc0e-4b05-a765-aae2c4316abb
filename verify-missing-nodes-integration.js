/**
 * 验证缺失节点集成脚本
 * 检查40个仅在引擎中注册的节点是否已成功集成到编辑器
 */

const fs = require('fs');

// 需要验证的40个节点列表
const missingNodes = [
  // 高级数学节点 (001-005)
  'math/advanced/power',
  'math/advanced/sqrt', 
  'math/trigonometric/sin',
  'math/trigonometric/cos',
  'math/trigonometric/tan',
  
  // 高级逻辑节点 (006-012)
  'logic/flow/branch',
  'logic/comparison/greaterEqual',
  'logic/comparison/lessEqual',
  'logic/operation/and',
  'logic/operation/or',
  'logic/operation/not',
  'logic/flow/toggle',
  
  // 实体组件节点 (013)
  'entity/component/has',
  
  // 物理系统节点 (014-019)
  'physics/collisionDetection',
  'physics/createConstraint',
  'physics/createMaterial',
  'physics/softbody/createBalloon',
  'physics/softbody/createJelly',
  'physics/softbody/cut',
  
  // 动画系统节点 (020-029)
  'animation/legacy/playAnimation',
  'animation/legacy/stopAnimation',
  'animation/legacy/setAnimationSpeed',
  'animation/legacy/getAnimationState',
  'animation/mixer/playAnimationAction',
  'animation/skeleton/createSkeletalAnimation',
  'animation/skeleton/setBoneTransform',
  'animation/ik/createIKConstraint',
  'animation/statemachine/createStateMachine',
  'animation/statemachine/transitionState',
  
  // 网络安全节点 (030-034)
  'network/security/computeHash',
  'network/security/generateSignature',
  'network/security/verifySignature',
  'network/security/createSession',
  'network/webrtc/onDataChannelMessage',
  
  // AI功能节点 (035-037)
  'ai/nlp/generateSummary',
  'ai/nlp/translateText',
  'ai/model/generateImage',
  
  // 数组操作节点 (038-040)
  'array/indexOf',
  'array/slice',
  'array/sort'
];

/**
 * 验证编辑器中的节点注册
 */
function verifyEditorIntegration() {
  console.log('🔍 验证编辑器节点集成状态...');
  
  const editorFile = 'editor/src/services/NodeRegistryService.ts';
  
  if (!fs.existsSync(editorFile)) {
    console.error('❌ 编辑器文件不存在:', editorFile);
    return false;
  }
  
  const content = fs.readFileSync(editorFile, 'utf8');
  
  let foundNodes = [];
  let missingInEditor = [];
  
  missingNodes.forEach(nodeType => {
    // 检查是否在编辑器中注册
    const registerPattern = new RegExp(`type:\\s*['"\`]${nodeType.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
    
    if (registerPattern.test(content)) {
      foundNodes.push(nodeType);
    } else {
      missingInEditor.push(nodeType);
    }
  });
  
  console.log(`✅ 在编辑器中找到的节点: ${foundNodes.length}/${missingNodes.length}`);
  console.log(`❌ 仍然缺失的节点: ${missingInEditor.length}/${missingNodes.length}`);
  
  if (foundNodes.length > 0) {
    console.log('\n📋 已集成的节点列表:');
    foundNodes.forEach((node, index) => {
      console.log(`   ${(index + 1).toString().padStart(2, '0')}. ${node}`);
    });
  }
  
  if (missingInEditor.length > 0) {
    console.log('\n⚠️ 仍然缺失的节点列表:');
    missingInEditor.forEach((node, index) => {
      console.log(`   ${(index + 1).toString().padStart(2, '0')}. ${node}`);
    });
  }
  
  return missingInEditor.length === 0;
}

/**
 * 验证initializeMissingEngineNodes方法是否存在
 */
function verifyInitMethod() {
  console.log('\n🔍 验证初始化方法...');
  
  const editorFile = 'editor/src/services/NodeRegistryService.ts';
  const content = fs.readFileSync(editorFile, 'utf8');
  
  // 检查方法是否存在
  const methodExists = content.includes('initializeMissingEngineNodes');
  const methodCalled = content.includes('this.initializeMissingEngineNodes()');
  
  console.log(`   - initializeMissingEngineNodes方法存在: ${methodExists ? '✅' : '❌'}`);
  console.log(`   - 方法在构造函数中被调用: ${methodCalled ? '✅' : '❌'}`);
  
  return methodExists && methodCalled;
}

/**
 * 生成集成状态报告
 */
function generateIntegrationReport() {
  console.log('\n📝 生成集成状态报告...');
  
  const editorFile = 'editor/src/services/NodeRegistryService.ts';
  const content = fs.readFileSync(editorFile, 'utf8');
  
  let integratedNodes = [];
  let stillMissing = [];
  
  missingNodes.forEach((nodeType, index) => {
    const registerPattern = new RegExp(`type:\\s*['"\`]${nodeType.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g');
    
    if (registerPattern.test(content)) {
      integratedNodes.push({
        序号: (index + 1).toString().padStart(3, '0'),
        节点名: nodeType,
        状态: '已集成'
      });
    } else {
      stillMissing.push({
        序号: (index + 1).toString().padStart(3, '0'),
        节点名: nodeType,
        状态: '仍缺失'
      });
    }
  });
  
  const reportContent = `# 缺失节点集成验证报告

**生成时间**: ${new Date().toLocaleString('zh-CN')}
**验证范围**: 40个仅在引擎中注册的节点

## 📊 集成统计

- **总节点数**: ${missingNodes.length} 个
- **已集成节点**: ${integratedNodes.length} 个
- **仍然缺失**: ${stillMissing.length} 个
- **集成成功率**: ${((integratedNodes.length / missingNodes.length) * 100).toFixed(1)}%

## ✅ 已成功集成的节点

| 序号 | 节点名 | 状态 |
|------|--------|------|
${integratedNodes.map(node => `| ${node.序号} | ${node.节点名} | ${node.状态} |`).join('\n')}

## ❌ 仍然缺失的节点

| 序号 | 节点名 | 状态 |
|------|--------|------|
${stillMissing.map(node => `| ${node.序号} | ${node.节点名} | ${node.状态} |`).join('\n')}

## 🔧 技术实施详情

### 集成方法
- 在 \`NodeRegistryService\` 中添加了 \`initializeMissingEngineNodes()\` 方法
- 在构造函数中调用该方法确保节点在服务初始化时注册
- 为每个节点配置了适当的分类、图标、颜色和标签

### 节点分类分布
- **高级数学节点**: 5个 (幂运算、平方根、三角函数)
- **高级逻辑节点**: 7个 (分支、比较、运算、开关)
- **实体组件节点**: 1个 (组件检查)
- **物理系统节点**: 6个 (碰撞、约束、材质、软体)
- **动画系统节点**: 10个 (传统动画、骨骼、IK、状态机)
- **网络安全节点**: 5个 (哈希、签名、会话、WebRTC)
- **AI功能节点**: 3个 (摘要、翻译、图像生成)
- **数组操作节点**: 3个 (索引、切片、排序)

---
*此报告由自动化验证脚本生成*
`;

  fs.writeFileSync('缺失节点集成验证报告.md', reportContent, 'utf8');
  console.log('✅ 报告已保存到: 缺失节点集成验证报告.md');
  
  return integratedNodes.length === missingNodes.length;
}

/**
 * 主验证函数
 */
function main() {
  console.log('🚀 开始验证缺失节点集成状态...\n');
  
  // 验证编辑器集成
  const editorIntegrated = verifyEditorIntegration();
  
  // 验证初始化方法
  const methodVerified = verifyInitMethod();
  
  // 生成报告
  const reportGenerated = generateIntegrationReport();
  
  console.log('\n🎯 验证结果总结:');
  console.log(`   - 编辑器节点集成: ${editorIntegrated ? '✅ 完成' : '❌ 未完成'}`);
  console.log(`   - 初始化方法验证: ${methodVerified ? '✅ 正常' : '❌ 异常'}`);
  console.log(`   - 报告生成: ${reportGenerated ? '✅ 成功' : '❌ 失败'}`);
  
  if (editorIntegrated && methodVerified) {
    console.log('\n🎉 所有40个缺失节点已成功集成到编辑器！');
    console.log('💡 现在这些节点可以在编辑器中通过拖拽方式使用了。');
  } else {
    console.log('\n⚠️ 集成过程中发现问题，请检查上述详细信息。');
  }
}

// 运行验证
main();
