# 视觉脚本节点API参考 - 2025年7月11日

## 📋 API概述

本文档提供了所有228个视觉脚本节点的完整API参考，包括详细的参数说明、返回值、错误处理和使用示例。

## 🏗️ 节点基础架构

### 节点基类 (Node)

所有节点都继承自基础Node类，提供以下核心功能：

```typescript
abstract class Node {
  // 基础属性
  readonly id: string;
  readonly type: string;
  readonly category: NodeCategory;
  
  // 输入输出管理
  addInput(config: SocketConfig): void;
  addOutput(config: SocketConfig): void;
  getInputValue(name: string): any;
  setOutputValue(name: string, value: any): void;
  
  // 执行控制
  abstract executeInternal(context: ExecutionContext): Promise<void>;
  execute(context: ExecutionContext): Promise<void>;
  
  // 资源管理
  dispose(): void;
}
```

### 执行上下文 (ExecutionContext)

```typescript
interface ExecutionContext {
  inputSocket: string | null;
  world: World | null;
  performanceMonitor: PerformanceMonitor;
  userData: Map<string, any>;
}
```

## 📐 数学节点API

### SinNode (math/trigonometry/sin)

```typescript
class SinNode extends Node {
  constructor(options: NodeOptions);
  
  // 输入接口
  inputs: {
    angle: number; // 角度（弧度）
  };
  
  // 输出接口
  outputs: {
    result: number; // 正弦值 [-1, 1]
  };
  
  // 错误处理
  errors: {
    INVALID_ANGLE: "无效的角度值";
    NAN_INPUT: "输入包含NaN值";
  };
}
```

**使用示例**:
```typescript
const sinNode = new SinNode({ id: 'sin1', position: { x: 0, y: 0 } });
sinNode.setInputValue('angle', Math.PI / 2);
await sinNode.execute(context);
const result = sinNode.getOutputValue('result'); // 1.0
```

### CosNode (math/trigonometry/cos)

```typescript
class CosNode extends Node {
  constructor(options: NodeOptions);
  
  inputs: {
    angle: number; // 角度（弧度）
  };
  
  outputs: {
    result: number; // 余弦值 [-1, 1]
  };
}
```

### VectorMagnitudeNode (math/vector/magnitude)

```typescript
interface Vector3 {
  x: number;
  y: number;
  z: number;
}

class VectorMagnitudeNode extends Node {
  constructor(options: NodeOptions);
  
  inputs: {
    vector: Vector3; // 输入向量
  };
  
  outputs: {
    magnitude: number; // 向量长度 [0, +∞)
  };
  
  // 验证方法
  private isValidVector3(vector: any): boolean;
}
```

**使用示例**:
```typescript
const vectorNode = new VectorMagnitudeNode({ id: 'vec1', position: { x: 0, y: 0 } });
vectorNode.setInputValue('vector', { x: 3, y: 4, z: 0 });
await vectorNode.execute(context);
const magnitude = vectorNode.getOutputValue('magnitude'); // 5.0
```

### VectorNormalizeNode (math/vector/normalize)

```typescript
class VectorNormalizeNode extends Node {
  constructor(options: NodeOptions);
  
  inputs: {
    vector: Vector3; // 输入向量
  };
  
  outputs: {
    normalized: Vector3;      // 归一化向量
    originalMagnitude: number; // 原始长度
  };
  
  // 特殊处理
  handleZeroVector(): Vector3; // 返回零向量
}
```

## 🔗 逻辑节点API

### LogicalAndNode (logic/boolean/and)

```typescript
class LogicalAndNode extends Node {
  constructor(options: NodeOptions);
  
  inputs: {
    a: boolean; // 第一个布尔值
    b: boolean; // 第二个布尔值
  };
  
  outputs: {
    result: boolean;     // 逻辑与结果
    truthTable: string;  // 真值表描述
  };
  
  // 真值转换
  private toBooleanValue(value: any): boolean;
}
```

**真值转换规则**:
```typescript
// JavaScript真值转换
Boolean(true)    // true
Boolean(false)   // false
Boolean(1)       // true
Boolean(0)       // false
Boolean("")      // false
Boolean("hello") // true
Boolean(null)    // false
Boolean(undefined) // false
```

## ⚡ 物理节点API

### SetGravityNode (physics/gravity/set)

```typescript
class SetGravityNode extends Node {
  constructor(options: NodeOptions);
  
  inputs: {
    exec: FlowSocket;    // 执行输入
    gravity: Vector3;    // 重力向量
  };
  
  outputs: {
    exec: FlowSocket;    // 执行输出
  };
  
  // 默认值
  static readonly DEFAULT_GRAVITY: Vector3 = { x: 0, y: -9.81, z: 0 };
}
```

### CollisionDetectNode (physics/collision/detect)

```typescript
interface Entity {
  id: string;
  transform: Transform;
  collider?: Collider;
}

class CollisionDetectNode extends Node {
  constructor(options: NodeOptions);
  
  inputs: {
    entityA: Entity; // 实体A
    entityB: Entity; // 实体B
  };
  
  outputs: {
    isColliding: boolean; // 碰撞状态
  };
  
  // 碰撞检测算法
  private checkAABBCollision(a: Entity, b: Entity): boolean;
  private checkSphereCollision(a: Entity, b: Entity): boolean;
}
```

### CreateRigidBodyNode (physics/rigidbody/create)

```typescript
interface RigidBodyConfig {
  mass: number;
  friction: number;
  restitution: number;
  isKinematic: boolean;
}

class CreateRigidBodyNode extends Node {
  constructor(options: NodeOptions);
  
  inputs: {
    exec: FlowSocket;
    entity: Entity;
    mass: number;        // 质量 (kg)
  };
  
  outputs: {
    exec: FlowSocket;
    rigidBody: RigidBody; // 创建的刚体
  };
  
  // 默认配置
  static readonly DEFAULT_CONFIG: RigidBodyConfig;
}
```

## ⏰ 时间节点API

### DelayNode (time/delay)

```typescript
class DelayNode extends Node {
  private timeoutId: NodeJS.Timeout | null;
  
  constructor(options: NodeOptions);
  
  inputs: {
    exec: FlowSocket;
    duration: number;    // 延迟时间（秒）
  };
  
  outputs: {
    exec: FlowSocket;    // 延迟后执行
  };
  
  // 资源管理
  dispose(): void;
  
  // 时间验证
  private validateDuration(duration: number): number;
}
```

**时间精度**: 最小延迟时间为10毫秒

### TimerNode (time/timer)

```typescript
class TimerNode extends Node {
  private intervalId: NodeJS.Timeout | null;
  private tickCount: number;
  private startTime: number;
  
  constructor(options: NodeOptions);
  
  inputs: {
    start: FlowSocket;   // 开始计时
    stop: FlowSocket;    // 停止计时
    reset: FlowSocket;   // 重置计时器
    interval: number;    // 间隔时间（秒）
    maxTicks: number;    // 最大触发次数（0=无限）
  };
  
  outputs: {
    tick: FlowSocket;    // 计时器触发
    finished: FlowSocket; // 计时器完成
    tickCount: number;   // 当前触发次数
    elapsedTime: number; // 已运行时间
    isRunning: boolean;  // 是否正在运行
  };
  
  // 控制方法
  private startTimer(): void;
  private stopTimer(): void;
  private resetTimer(): void;
}
```

## 🎬 动画节点API

### PlayAnimationNode (animation/playAnimation)

```typescript
interface AnimationComponent {
  play(name: string): void;
  stop(): void;
  pause(): void;
  setSpeed(speed: number): void;
  isPlaying(): boolean;
  getCurrentTime(): number;
}

class PlayAnimationNode extends Node {
  constructor(options: NodeOptions);
  
  inputs: {
    exec: FlowSocket;
    entity: Entity;
    animationName: string; // 动画名称
  };
  
  outputs: {
    exec: FlowSocket;
  };
  
  // 验证方法
  private validateAnimation(entity: Entity, name: string): boolean;
}
```

## 🎮 输入节点API

### KeyboardInputNode (input/keyboard)

```typescript
class KeyboardInputNode extends Node {
  constructor(options: NodeOptions);
  
  inputs: {
    keyCode: string; // 按键代码
  };
  
  outputs: {
    isPressed: boolean;   // 是否按下
    onKeyDown: FlowSocket; // 按键按下事件
    onKeyUp: FlowSocket;   // 按键释放事件
  };
  
  // 支持的按键代码
  static readonly SUPPORTED_KEYS: string[];
}
```

**支持的按键代码**:
```typescript
const SUPPORTED_KEYS = [
  'Space', 'Enter', 'Escape', 'Tab',
  'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight',
  'KeyA', 'KeyB', 'KeyC', ..., 'KeyZ',
  'Digit0', 'Digit1', ..., 'Digit9',
  'F1', 'F2', ..., 'F12'
];
```

## 🔊 音频节点API

### PlayAudioNode (audio/playAudio)

```typescript
interface AudioClip {
  url: string;
  duration: number;
  format: string;
}

class PlayAudioNode extends Node {
  constructor(options: NodeOptions);
  
  inputs: {
    exec: FlowSocket;
    audioClip: AudioClip;
    volume: number;      // 音量 [0, 1]
    loop: boolean;       // 是否循环
  };
  
  outputs: {
    exec: FlowSocket;
    onFinished: FlowSocket; // 播放完成事件
  };
  
  // 音频控制
  private validateVolume(volume: number): number;
  private loadAudioClip(clip: AudioClip): Promise<AudioBuffer>;
}
```

## 🔧 错误处理

### 错误类型

```typescript
enum NodeErrorType {
  INVALID_INPUT = "INVALID_INPUT",
  MISSING_COMPONENT = "MISSING_COMPONENT",
  EXECUTION_FAILED = "EXECUTION_FAILED",
  RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND",
  TIMEOUT = "TIMEOUT"
}

interface NodeError {
  type: NodeErrorType;
  message: string;
  nodeId: string;
  timestamp: number;
}
```

### 错误处理策略

```typescript
class ErrorHandler {
  static handleNodeError(error: NodeError): void {
    // 记录错误日志
    console.error(`节点错误 [${error.nodeId}]: ${error.message}`);
    
    // 性能监控
    if (performanceMonitor) {
      performanceMonitor.recordNodeExecution(
        error.nodeId, 
        'error', 
        error.message
      );
    }
    
    // 错误恢复
    this.attemptErrorRecovery(error);
  }
  
  private static attemptErrorRecovery(error: NodeError): void {
    // 实现错误恢复逻辑
  }
}
```

## 📊 性能监控

### 性能监控接口

```typescript
interface PerformanceMonitor {
  recordNodeExecution(
    nodeType: string, 
    status: 'success' | 'error' | 'timeout',
    details?: string
  ): void;
  
  getNodeStatistics(nodeType: string): NodeStatistics;
  generatePerformanceReport(): PerformanceReport;
}

interface NodeStatistics {
  totalExecutions: number;
  successCount: number;
  errorCount: number;
  averageExecutionTime: number;
  maxExecutionTime: number;
  minExecutionTime: number;
}
```

## 🔍 调试工具

### 调试接口

```typescript
interface DebugInfo {
  nodeId: string;
  nodeType: string;
  inputs: Map<string, any>;
  outputs: Map<string, any>;
  executionTime: number;
  memoryUsage: number;
}

class NodeDebugger {
  static enableDebugMode(nodeId: string): void;
  static disableDebugMode(nodeId: string): void;
  static getDebugInfo(nodeId: string): DebugInfo;
  static exportDebugLog(): string;
}
```

---
*API版本: 1.0*  
*最后更新: 2025年7月11日*  
*兼容性: 引擎版本 >= 1.0.0*
