/**
 * 第9批次地形与环境系统节点测试
 * 测试节点241-250的基本功能
 */

import * as THREE from 'three';
import {
  EnableRefractionNode,
  CreateVegetationNode,
  AddGrassNode,
  AddTreesNode,
  CreateWeatherSystemNode,
  EnableRainNode,
  EnableSnowNode,
  SetWindDirectionNode,
  SetWindStrengthNode,
  SetTimeOfDayNode
} from '../TerrainEnvironmentNodesBatch9';

describe('第9批次地形与环境系统节点测试', () => {
  // 模拟节点选项
  const mockOptions = {
    id: 'test-node',
    type: 'test',
    category: 'test',
    metadata: { positionX: 0, positionY: 0 },
    graph: null,
    context: null
  };

  describe('水面折射节点 (241)', () => {
    test('EnableRefractionNode 应该能够创建', () => {
      const node = new EnableRefractionNode(mockOptions);
      expect(node).toBeDefined();
      expect(node.id).toBe('test-node');
    });
  });

  describe('植被系统节点 (242-244)', () => {
    test('CreateVegetationNode 应该能够创建', () => {
      const node = new CreateVegetationNode(mockOptions);
      expect(node).toBeDefined();
      expect(node.id).toBe('test-node');
    });

    test('AddGrassNode 应该能够创建', () => {
      const node = new AddGrassNode(mockOptions);
      expect(node).toBeDefined();
      expect(node.id).toBe('test-node');
    });

    test('AddTreesNode 应该能够创建', () => {
      const node = new AddTreesNode(mockOptions);
      expect(node).toBeDefined();
      expect(node.id).toBe('test-node');
    });
  });

  describe('天气系统节点 (245-249)', () => {
    test('CreateWeatherSystemNode 应该能够创建', () => {
      const node = new CreateWeatherSystemNode(mockOptions);
      expect(node).toBeDefined();
      expect(node.id).toBe('test-node');
    });

    test('EnableRainNode 应该能够创建', () => {
      const node = new EnableRainNode(mockOptions);
      expect(node).toBeDefined();
      expect(node.id).toBe('test-node');
    });

    test('EnableSnowNode 应该能够创建', () => {
      const node = new EnableSnowNode(mockOptions);
      expect(node).toBeDefined();
      expect(node.id).toBe('test-node');
    });

    test('SetWindDirectionNode 应该能够创建', () => {
      const node = new SetWindDirectionNode(mockOptions);
      expect(node).toBeDefined();
      expect(node.id).toBe('test-node');
    });

    test('SetWindStrengthNode 应该能够创建', () => {
      const node = new SetWindStrengthNode(mockOptions);
      expect(node).toBeDefined();
      expect(node.id).toBe('test-node');
    });
  });

  describe('环境时间控制节点 (250)', () => {
    test('SetTimeOfDayNode 应该能够创建', () => {
      const node = new SetTimeOfDayNode(mockOptions);
      expect(node).toBeDefined();
      expect(node.id).toBe('test-node');
    });
  });

  describe('节点数量验证', () => {
    test('应该有10个节点类', () => {
      const nodeClasses = [
        EnableRefractionNode,
        CreateVegetationNode,
        AddGrassNode,
        AddTreesNode,
        CreateWeatherSystemNode,
        EnableRainNode,
        EnableSnowNode,
        SetWindDirectionNode,
        SetWindStrengthNode,
        SetTimeOfDayNode
      ];
      
      expect(nodeClasses.length).toBe(10);
    });
  });

  describe('功能测试', () => {
    test('CreateVegetationNode 应该能够执行', () => {
      const node = new CreateVegetationNode(mockOptions);
      
      // 模拟输入值
      node.setInputValue = jest.fn();
      node.getInputValue = jest.fn((name: string) => {
        switch (name) {
          case 'terrain': return { type: 'terrain' };
          case 'vegetationType': return 'mixed';
          case 'density': return 0.5;
          default: return undefined;
        }
      });
      node.setOutputValue = jest.fn();

      const result = node.execute();
      
      expect(result.completed).toBe(true);
      expect(result.vegetationSystem).toBeDefined();
      expect(node.setOutputValue).toHaveBeenCalledWith('vegetationSystem', expect.any(Object));
    });

    test('CreateWeatherSystemNode 应该能够执行', () => {
      const node = new CreateWeatherSystemNode(mockOptions);
      
      // 模拟输入值
      node.setInputValue = jest.fn();
      node.getInputValue = jest.fn((name: string) => {
        switch (name) {
          case 'scene': return new THREE.Scene();
          case 'weatherType': return 'clear';
          default: return undefined;
        }
      });
      node.setOutputValue = jest.fn();

      const result = node.execute();
      
      expect(result.completed).toBe(true);
      expect(result.weatherSystem).toBeDefined();
      expect(node.setOutputValue).toHaveBeenCalledWith('weatherSystem', expect.any(Object));
    });

    test('SetTimeOfDayNode 应该能够执行', () => {
      const node = new SetTimeOfDayNode(mockOptions);
      
      // 模拟输入值
      node.setInputValue = jest.fn();
      node.getInputValue = jest.fn((name: string) => {
        switch (name) {
          case 'scene': return new THREE.Scene();
          case 'timeOfDay': return 12;
          case 'sunLight': return new THREE.DirectionalLight();
          default: return undefined;
        }
      });
      node.setOutputValue = jest.fn();

      const result = node.execute();
      
      expect(result.completed).toBe(true);
      expect(result.lightIntensity).toBeDefined();
      expect(result.skyColor).toBeDefined();
      expect(node.setOutputValue).toHaveBeenCalledWith('lightIntensity', expect.any(Number));
      expect(node.setOutputValue).toHaveBeenCalledWith('skyColor', expect.any(THREE.Color));
    });
  });
});
