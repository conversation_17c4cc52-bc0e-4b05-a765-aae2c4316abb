/**
 * 增强版节点单元测试
 * 使用Jest框架测试关键节点的功能正确性和稳定性
 */

import { 
  EnhancedSinNode, 
  EnhancedCosNode, 
  EnhancedVectorMagnitudeNode, 
  EnhancedVectorNormalizeNode,
  EnhancedLogicalAndNode,
  EnhancedDelayNode
} from '../Enhanced228NodesImplementation';
import { ExecutionContext } from '../../execution/ExecutionContext';

// 模拟执行上下文
class MockExecutionContext implements Partial<ExecutionContext> {
  public inputSocket: string | null = null;
  public performanceMonitor = {
    recordNodeExecution: jest.fn()
  };
  
  constructor(inputSocket?: string) {
    this.inputSocket = inputSocket || null;
  }
}

// 模拟节点选项
const mockNodeOptions = {
  id: 'test-node',
  position: { x: 0, y: 0 }
};

describe('增强版数学节点测试', () => {
  describe('EnhancedSinNode', () => {
    let sinNode: EnhancedSinNode;
    let context: MockExecutionContext;

    beforeEach(() => {
      sinNode = new EnhancedSinNode(mockNodeOptions);
      context = new MockExecutionContext();
    });

    test('应该正确计算正弦值', async () => {
      // 测试 sin(0) = 0
      sinNode.setInputValue('angle', 0);
      await sinNode.execute(context as ExecutionContext);
      expect(sinNode.getOutputValue('result')).toBeCloseTo(0, 5);

      // 测试 sin(π/2) = 1
      sinNode.setInputValue('angle', Math.PI / 2);
      await sinNode.execute(context as ExecutionContext);
      expect(sinNode.getOutputValue('result')).toBeCloseTo(1, 5);

      // 测试 sin(π) ≈ 0
      sinNode.setInputValue('angle', Math.PI);
      await sinNode.execute(context as ExecutionContext);
      expect(sinNode.getOutputValue('result')).toBeCloseTo(0, 5);
    });

    test('应该处理无效输入', async () => {
      // 测试 NaN 输入
      sinNode.setInputValue('angle', NaN);
      await sinNode.execute(context as ExecutionContext);
      expect(sinNode.getOutputValue('result')).toBe(0);

      // 测试字符串输入
      sinNode.setInputValue('angle', 'invalid');
      await sinNode.execute(context as ExecutionContext);
      expect(sinNode.getOutputValue('result')).toBe(0);
    });

    test('应该记录性能监控数据', async () => {
      sinNode.setInputValue('angle', 0);
      await sinNode.execute(context as ExecutionContext);
      
      expect(context.performanceMonitor.recordNodeExecution).toHaveBeenCalledWith(
        'math/trigonometry/sin',
        'success'
      );
    });
  });

  describe('EnhancedCosNode', () => {
    let cosNode: EnhancedCosNode;
    let context: MockExecutionContext;

    beforeEach(() => {
      cosNode = new EnhancedCosNode(mockNodeOptions);
      context = new MockExecutionContext();
    });

    test('应该正确计算余弦值', async () => {
      // 测试 cos(0) = 1
      cosNode.setInputValue('angle', 0);
      await cosNode.execute(context as ExecutionContext);
      expect(cosNode.getOutputValue('result')).toBeCloseTo(1, 5);

      // 测试 cos(π/2) ≈ 0
      cosNode.setInputValue('angle', Math.PI / 2);
      await cosNode.execute(context as ExecutionContext);
      expect(cosNode.getOutputValue('result')).toBeCloseTo(0, 5);

      // 测试 cos(π) = -1
      cosNode.setInputValue('angle', Math.PI);
      await cosNode.execute(context as ExecutionContext);
      expect(cosNode.getOutputValue('result')).toBeCloseTo(-1, 5);
    });
  });

  describe('EnhancedVectorMagnitudeNode', () => {
    let vectorMagnitudeNode: EnhancedVectorMagnitudeNode;
    let context: MockExecutionContext;

    beforeEach(() => {
      vectorMagnitudeNode = new EnhancedVectorMagnitudeNode(mockNodeOptions);
      context = new MockExecutionContext();
    });

    test('应该正确计算向量长度', async () => {
      // 测试 3-4-5 直角三角形
      vectorMagnitudeNode.setInputValue('vector', { x: 3, y: 4, z: 0 });
      await vectorMagnitudeNode.execute(context as ExecutionContext);
      expect(vectorMagnitudeNode.getOutputValue('magnitude')).toBeCloseTo(5, 5);

      // 测试单位向量
      vectorMagnitudeNode.setInputValue('vector', { x: 1, y: 0, z: 0 });
      await vectorMagnitudeNode.execute(context as ExecutionContext);
      expect(vectorMagnitudeNode.getOutputValue('magnitude')).toBeCloseTo(1, 5);

      // 测试零向量
      vectorMagnitudeNode.setInputValue('vector', { x: 0, y: 0, z: 0 });
      await vectorMagnitudeNode.execute(context as ExecutionContext);
      expect(vectorMagnitudeNode.getOutputValue('magnitude')).toBe(0);
    });

    test('应该处理无效向量输入', async () => {
      // 测试无效向量格式
      vectorMagnitudeNode.setInputValue('vector', 'invalid');
      await vectorMagnitudeNode.execute(context as ExecutionContext);
      expect(vectorMagnitudeNode.getOutputValue('magnitude')).toBe(0);

      // 测试缺少属性的向量
      vectorMagnitudeNode.setInputValue('vector', { x: 1, y: 2 });
      await vectorMagnitudeNode.execute(context as ExecutionContext);
      expect(vectorMagnitudeNode.getOutputValue('magnitude')).toBe(0);
    });
  });

  describe('EnhancedVectorNormalizeNode', () => {
    let vectorNormalizeNode: EnhancedVectorNormalizeNode;
    let context: MockExecutionContext;

    beforeEach(() => {
      vectorNormalizeNode = new EnhancedVectorNormalizeNode(mockNodeOptions);
      context = new MockExecutionContext();
    });

    test('应该正确归一化向量', async () => {
      // 测试标准向量归一化
      vectorNormalizeNode.setInputValue('vector', { x: 3, y: 4, z: 0 });
      await vectorNormalizeNode.execute(context as ExecutionContext);
      
      const normalized = vectorNormalizeNode.getOutputValue('normalized');
      expect(normalized.x).toBeCloseTo(0.6, 5);
      expect(normalized.y).toBeCloseTo(0.8, 5);
      expect(normalized.z).toBeCloseTo(0, 5);
      
      // 验证归一化向量的长度为1
      const magnitude = Math.sqrt(normalized.x * normalized.x + normalized.y * normalized.y + normalized.z * normalized.z);
      expect(magnitude).toBeCloseTo(1, 5);
      
      // 验证原始长度输出
      expect(vectorNormalizeNode.getOutputValue('originalMagnitude')).toBeCloseTo(5, 5);
    });

    test('应该处理零向量', async () => {
      vectorNormalizeNode.setInputValue('vector', { x: 0, y: 0, z: 0 });
      await vectorNormalizeNode.execute(context as ExecutionContext);
      
      const normalized = vectorNormalizeNode.getOutputValue('normalized');
      expect(normalized).toEqual({ x: 0, y: 0, z: 0 });
      expect(vectorNormalizeNode.getOutputValue('originalMagnitude')).toBe(0);
    });

    test('应该处理已归一化的向量', async () => {
      vectorNormalizeNode.setInputValue('vector', { x: 1, y: 0, z: 0 });
      await vectorNormalizeNode.execute(context as ExecutionContext);
      
      const normalized = vectorNormalizeNode.getOutputValue('normalized');
      expect(normalized.x).toBeCloseTo(1, 5);
      expect(normalized.y).toBeCloseTo(0, 5);
      expect(normalized.z).toBeCloseTo(0, 5);
      expect(vectorNormalizeNode.getOutputValue('originalMagnitude')).toBeCloseTo(1, 5);
    });
  });
});

describe('增强版逻辑节点测试', () => {
  describe('EnhancedLogicalAndNode', () => {
    let logicalAndNode: EnhancedLogicalAndNode;
    let context: MockExecutionContext;

    beforeEach(() => {
      logicalAndNode = new EnhancedLogicalAndNode(mockNodeOptions);
      context = new MockExecutionContext();
    });

    test('应该正确执行逻辑与运算', async () => {
      // 测试真值表的所有组合
      const testCases = [
        { a: true, b: true, expected: true },
        { a: true, b: false, expected: false },
        { a: false, b: true, expected: false },
        { a: false, b: false, expected: false }
      ];

      for (const testCase of testCases) {
        logicalAndNode.setInputValue('a', testCase.a);
        logicalAndNode.setInputValue('b', testCase.b);
        await logicalAndNode.execute(context as ExecutionContext);
        
        expect(logicalAndNode.getOutputValue('result')).toBe(testCase.expected);
        
        const truthTable = logicalAndNode.getOutputValue('truthTable');
        expect(truthTable).toContain(testCase.a ? 'T' : 'F');
        expect(truthTable).toContain(testCase.b ? 'T' : 'F');
        expect(truthTable).toContain(testCase.expected ? 'T' : 'F');
      }
    });

    test('应该处理真值转换', async () => {
      // 测试数字真值转换
      logicalAndNode.setInputValue('a', 1);
      logicalAndNode.setInputValue('b', 0);
      await logicalAndNode.execute(context as ExecutionContext);
      expect(logicalAndNode.getOutputValue('result')).toBe(false);

      // 测试字符串真值转换
      logicalAndNode.setInputValue('a', 'hello');
      logicalAndNode.setInputValue('b', '');
      await logicalAndNode.execute(context as ExecutionContext);
      expect(logicalAndNode.getOutputValue('result')).toBe(false);
    });
  });
});

describe('增强版时间节点测试', () => {
  describe('EnhancedDelayNode', () => {
    let delayNode: EnhancedDelayNode;
    let context: MockExecutionContext;

    beforeEach(() => {
      delayNode = new EnhancedDelayNode(mockNodeOptions);
      context = new MockExecutionContext('exec');
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
      delayNode.dispose();
    });

    test('应该在指定时间后执行', async () => {
      const mockExecuteOutput = jest.spyOn(delayNode, 'executeOutput');
      
      delayNode.setInputValue('duration', 1.0);
      await delayNode.execute(context as ExecutionContext);
      
      // 验证延迟还未执行
      expect(mockExecuteOutput).not.toHaveBeenCalled();
      
      // 快进时间
      jest.advanceTimersByTime(1000);
      
      // 验证延迟已执行
      expect(mockExecuteOutput).toHaveBeenCalledWith('exec', context);
    });

    test('应该处理无效的延迟时间', async () => {
      delayNode.setInputValue('duration', -1);
      await delayNode.execute(context as ExecutionContext);
      
      // 负数应该被转换为0，立即执行
      jest.advanceTimersByTime(0);
      expect(delayNode.executeOutput).toHaveBeenCalled();
    });

    test('应该正确清理资源', () => {
      delayNode.setInputValue('duration', 1.0);
      delayNode.execute(context as ExecutionContext);
      
      // 调用dispose应该清理定时器
      delayNode.dispose();
      
      // 快进时间，不应该执行
      jest.advanceTimersByTime(1000);
      expect(delayNode.executeOutput).not.toHaveBeenCalled();
    });
  });
});
