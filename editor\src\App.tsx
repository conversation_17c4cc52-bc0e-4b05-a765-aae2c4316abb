/**
 * 应用程序主组件
 */
import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, theme, Spin } from 'antd';
import { useTranslation } from 'react-i18next';

import { useAppDispatch, useAppSelector } from './store';
import { checkAuth } from './store/auth/authSlice';
import { ThemeType } from './store/ui/uiSlice';
import { AppLayout } from './components/layout/AppLayout';
import { LoginPage } from './pages/LoginPage';
import { RegisterPage } from './pages/RegisterPage';
import { ProjectsPage } from './pages/ProjectsPage';
import { EditorPage } from './pages/EditorPage';
import { NotFoundPage } from './pages/NotFoundPage';
import TerrainEditorPage from './pages/TerrainEditorPage';
import FeedbackSystemDemo from './pages/FeedbackSystemDemo';
import Batch4NodeTest from './components/testing/Batch4NodeTest';
import AchievementNotification from './components/achievements/AchievementNotification';
import TutorialHighlightContainer from './components/tutorials/TutorialHighlight';
import GitPanel from './components/git/GitPanel';
import GitConflictResolver from './components/git/GitConflictResolver';
import { microserviceIntegration } from './services/MicroserviceIntegration';
import { engineNodeIntegration } from './services/EngineNodeIntegration';
import EngineService from './services/EngineService';
import { config } from './config/environment';
import { DevToolsFloatButton } from './components/debug/DevToolsPanel';

const App: React.FC = () => {
  const dispatch = useAppDispatch();
  const { i18n } = useTranslation();
  const { isAuthenticated, isLoading } = useAppSelector((state) => state.auth);
  const { theme: themeType, language } = useAppSelector((state) => state.ui);

  // 检查认证状态
  useEffect(() => {
    dispatch(checkAuth());
  }, [dispatch]);

  // 设置语言
  useEffect(() => {
    if (language) {
      i18n.changeLanguage(language);
    }
  }, [language, i18n]);

  // 初始化微服务集成
  useEffect(() => {
    const initMicroservices = async () => {
      try {
        if (config.enableDebug) {
          console.log('🚀 初始化微服务集成...');
        }
        await microserviceIntegration.initialize();
        if (config.enableDebug) {
          console.log('✅ 微服务集成初始化完成');
        }
      } catch (error) {
        console.error('❌ 微服务集成初始化失败:', error);
      }
    };

    initMicroservices();

    // 清理函数
    return () => {
      microserviceIntegration.destroy();
    };
  }, []);

  // 初始化引擎节点集成
  useEffect(() => {
    const initEngineNodes = async () => {
      try {
        if (config.enableDebug) {
          console.log('🎯 初始化引擎节点集成...');
        }

        // 获取引擎服务实例
        const engineService = EngineService;

        // 初始化引擎节点集成
        engineNodeIntegration.initialize(engineService);

        if (config.enableDebug) {
          console.log('✅ 引擎节点集成初始化完成');
          console.log('📊 集成状态:', engineNodeIntegration.getStatus());
        }
      } catch (error) {
        console.error('❌ 引擎节点集成初始化失败:', error);
      }
    };

    initEngineNodes();

    // 清理函数
    return () => {
      engineNodeIntegration.dispose();
    };
  }, []);

  // 如果正在加载认证状态，显示加载中
  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  return (
    <ConfigProvider
      theme={{
        algorithm: themeType === ThemeType.DARK ? theme.darkAlgorithm : theme.defaultAlgorithm}}
    >
      <>
        <Routes>
          {/* 公共路由 */}
          <Route path="/login" element={isAuthenticated ? <Navigate to="/projects" /> : <LoginPage />} />
          <Route path="/register" element={isAuthenticated ? <Navigate to="/projects" /> : <RegisterPage />} />

          {/* 需要认证的路由 */}
          <Route path="/" element={isAuthenticated ? <AppLayout /> : <Navigate to="/login" />}>
            <Route index element={<Navigate to="/projects" />} />
            <Route path="projects" element={<ProjectsPage />} />
            <Route path="editor/:projectId/:sceneId" element={<EditorPage />} />
            <Route path="terrain-editor" element={<TerrainEditorPage />} />
            <Route path="feedback-demo" element={<FeedbackSystemDemo />} />
            <Route path="test/batch4-nodes" element={<Batch4NodeTest />} />
          </Route>

          {/* 编辑器全屏模式 */}
          <Route path="/editor/:projectId/:sceneId/fullscreen" element={<EditorPage />} />

          {/* 404页面 */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>

        {/* 全局组件 */}
        {isAuthenticated && (
          <>
            <AchievementNotification />
            <TutorialHighlightContainer />
            <GitPanel />
            <GitConflictResolver />
          </>
        )}

        {/* 开发工具 */}
        <DevToolsFloatButton />
      </>
    </ConfigProvider>
  );
};

export default App;
