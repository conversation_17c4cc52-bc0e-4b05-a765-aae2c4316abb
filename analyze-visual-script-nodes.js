/**
 * 视觉脚本系统节点分析工具
 * 分析引擎注册和编辑器集成的节点状态
 * 日期：2025-7-11
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始分析视觉脚本系统节点状态...\n');

// 分析结果存储
const analysisResult = {
  engineNodes: [],
  editorNodes: [],
  engineRegisteredCount: 0,
  editorIntegratedCount: 0,
  analysisDate: '2025-7-11'
};

/**
 * 分析引擎注册的节点
 */
function analyzeEngineNodes() {
  console.log('📊 分析引擎注册节点...');

  const presetDir = 'engine/src/visualscript/presets';

  // 动态获取所有.ts文件，排除测试文件和索引文件
  const allFiles = fs.readdirSync(presetDir);
  const presetFiles = allFiles.filter(file =>
    file.endsWith('.ts') &&
    !file.includes('test') &&
    !file.includes('Test') &&
    !file.includes('Index') &&
    file !== 'index.ts'
  );

  let nodeIndex = 1;
  const seenTypes = new Set(); // 用于去重
  let duplicateCount = 0;

  presetFiles.forEach(fileName => {
    const filePath = path.join(presetDir, fileName);

    if (fs.existsSync(filePath)) {
      console.log(`  分析文件: ${fileName}`);
      const content = fs.readFileSync(filePath, 'utf8');

      // 提取registerNodeType调用
      const registerPattern = /registry\.registerNodeType\(\{[\s\S]*?\}\);/g;
      const matches = content.match(registerPattern) || [];

      matches.forEach(match => {
        // 提取节点信息
        const typeMatch = match.match(/type:\s*['"`]([^'"`]+)['"`]/);
        const labelMatch = match.match(/label:\s*['"`]([^'"`]+)['"`]/);
        const descriptionMatch = match.match(/description:\s*['"`]([^'"`]+)['"`]/);
        const categoryMatch = match.match(/category:\s*NodeCategory\.(\w+)/);
        const iconMatch = match.match(/icon:\s*['"`]([^'"`]+)['"`]/);
        const colorMatch = match.match(/color:\s*['"`]([^'"`]+)['"`]/);

        if (typeMatch && labelMatch) {
          const nodeType = typeMatch[1];

          // 检查是否重复
          if (seenTypes.has(nodeType)) {
            duplicateCount++;
            console.log(`    ⚠️ 发现重复节点: ${nodeType} - ${labelMatch[1]} (在 ${fileName})`);
            return; // 跳过重复节点
          }

          seenTypes.add(nodeType);

          const nodeInfo = {
            序号: nodeIndex.toString().padStart(3, '0'),
            节点类型: nodeType,
            节点名称: labelMatch[1],
            节点中文名: labelMatch[1], // 标签通常就是中文名
            描述: descriptionMatch ? descriptionMatch[1] : '',
            分类: categoryMatch ? categoryMatch[1] : '',
            图标: iconMatch ? iconMatch[1] : '',
            颜色: colorMatch ? colorMatch[1] : '',
            源文件: fileName,
            已注册到引擎: '是',
            已集成到编辑器: '待检查'
          };

          analysisResult.engineNodes.push(nodeInfo);
          nodeIndex++;
        }
      });

      console.log(`    找到 ${matches.length} 个节点`);
    } else {
      console.log(`    文件不存在: ${fileName}`);
    }
  });

  analysisResult.engineRegisteredCount = analysisResult.engineNodes.length;
  console.log(`  总共发现 ${seenTypes.size + duplicateCount} 个registerNodeType调用`);
  console.log(`  去重后保留 ${analysisResult.engineRegisteredCount} 个唯一节点`);
  console.log(`  跳过 ${duplicateCount} 个重复节点`);
  console.log(`✅ 引擎注册节点分析完成\n`);
}

/**
 * 分析编辑器集成的节点
 */
function analyzeEditorNodes() {
  console.log('📊 分析编辑器集成节点...');

  const serviceFile = 'editor/src/services/NodeRegistryService.ts';

  if (!fs.existsSync(serviceFile)) {
    console.error('❌ NodeRegistryService.ts 文件不存在');
    return;
  }

  const content = fs.readFileSync(serviceFile, 'utf8');

  // 提取registerNode调用
  const registerPattern = /this\.registerNode\(\{[\s\S]*?\}\);/g;
  const matches = content.match(registerPattern) || [];

  let nodeIndex = 1;
  const seenTypes = new Set(); // 用于去重
  let duplicateCount = 0;

  matches.forEach(match => {
    // 提取节点信息
    const typeMatch = match.match(/type:\s*['"`]([^'"`]+)['"`]/);
    const labelMatch = match.match(/label:\s*['"`]([^'"`]+)['"`]/);
    const descriptionMatch = match.match(/description:\s*['"`]([^'"`]+)['"`]/);
    const categoryMatch = match.match(/category:\s*NodeCategory\.(\w+)/);
    const iconMatch = match.match(/icon:\s*['"`]([^'"`]+)['"`]/);
    const colorMatch = match.match(/color:\s*['"`]([^'"`]+)['"`]/);
    const tagsMatch = match.match(/tags:\s*\[([\s\S]*?)\]/);

    if (typeMatch && labelMatch) {
      const nodeType = typeMatch[1];

      // 检查是否重复
      if (seenTypes.has(nodeType)) {
        duplicateCount++;
        console.log(`  ⚠️ 发现重复节点: ${nodeType} - ${labelMatch[1]}`);
        return; // 跳过重复节点
      }

      seenTypes.add(nodeType);

      const nodeInfo = {
        序号: nodeIndex.toString().padStart(3, '0'),
        节点类型: nodeType,
        节点名称: labelMatch[1],
        节点中文名: labelMatch[1],
        描述: descriptionMatch ? descriptionMatch[1] : '',
        分类: categoryMatch ? categoryMatch[1] : '',
        图标: iconMatch ? iconMatch[1] : '',
        颜色: colorMatch ? colorMatch[1] : '',
        标签: tagsMatch ? tagsMatch[1].replace(/['"`]/g, '').split(',').map(t => t.trim()).join(', ') : '',
        已注册到引擎: '待检查',
        已集成到编辑器: '是'
      };

      analysisResult.editorNodes.push(nodeInfo);
      nodeIndex++;
    }
  });

  analysisResult.editorIntegratedCount = analysisResult.editorNodes.length;
  console.log(`  发现 ${matches.length} 个registerNode调用`);
  console.log(`  去重后保留 ${analysisResult.editorIntegratedCount} 个唯一节点`);
  console.log(`  跳过 ${duplicateCount} 个重复节点`);
  console.log(`✅ 编辑器集成节点分析完成\n`);
}

/**
 * 交叉验证节点状态
 */
function crossValidateNodes() {
  console.log('🔄 交叉验证节点状态...');
  
  // 为编辑器节点标记引擎注册状态
  analysisResult.editorNodes.forEach(editorNode => {
    const engineNode = analysisResult.engineNodes.find(en => en.节点类型 === editorNode.节点类型);
    editorNode.已注册到引擎 = engineNode ? '是' : '否';
  });
  
  // 为引擎节点标记编辑器集成状态
  analysisResult.engineNodes.forEach(engineNode => {
    const editorNode = analysisResult.editorNodes.find(en => en.节点类型 === engineNode.节点类型);
    engineNode.已集成到编辑器 = editorNode ? '是' : '否';
  });
  
  console.log('✅ 交叉验证完成\n');
}

/**
 * 生成分析报告
 */
function generateReport() {
  console.log('📝 生成分析报告...');
  
  const report = `# 视觉脚本系统节点状态分析报告
## 分析日期：${analysisResult.analysisDate}

## 📊 统计概览

- **引擎注册节点总数**：${analysisResult.engineRegisteredCount} 个
- **编辑器集成节点总数**：${analysisResult.editorIntegratedCount} 个
- **双重注册节点数**：${analysisResult.editorNodes.filter(n => n.已注册到引擎 === '是').length} 个
- **仅引擎注册节点数**：${analysisResult.engineNodes.filter(n => n.已集成到编辑器 === '否').length} 个
- **仅编辑器集成节点数**：${analysisResult.editorNodes.filter(n => n.已注册到引擎 === '否').length} 个

## 📋 完整节点清单

### 引擎注册节点列表

| 序号 | 节点类型 | 节点中文名 | 已注册到引擎 | 已集成到编辑器 | 源文件 |
|------|----------|------------|--------------|----------------|--------|
${analysisResult.engineNodes.map(node => 
  `| ${node.序号} | ${node.节点类型} | ${node.节点中文名} | ${node.已注册到引擎} | ${node.已集成到编辑器} | ${node.源文件} |`
).join('\n')}

### 编辑器集成节点列表

| 序号 | 节点类型 | 节点中文名 | 已注册到引擎 | 已集成到编辑器 | 分类 |
|------|----------|------------|--------------|----------------|------|
${analysisResult.editorNodes.map(node => 
  `| ${node.序号} | ${node.节点类型} | ${node.节点中文名} | ${node.已注册到引擎} | ${node.已集成到编辑器} | ${node.分类} |`
).join('\n')}

## 🔍 问题分析

### 仅在引擎注册但未集成到编辑器的节点
${analysisResult.engineNodes.filter(n => n.已集成到编辑器 === '否').map((node, index) => 
  `${(index + 1).toString().padStart(3, '0')}. ${node.节点类型} - ${node.节点中文名}`
).join('\n')}

### 仅在编辑器集成但未注册到引擎的节点
${analysisResult.editorNodes.filter(n => n.已注册到引擎 === '否').map((node, index) => 
  `${(index + 1).toString().padStart(3, '0')}. ${node.节点类型} - ${node.节点中文名}`
).join('\n')}

## 📈 建议

1. **完善双重注册**：确保所有节点既在引擎注册又在编辑器集成
2. **统一节点管理**：建立统一的节点注册机制
3. **定期同步检查**：定期验证引擎和编辑器的节点一致性

---
*报告生成时间：${new Date().toLocaleString('zh-CN')}*
`;

  // 保存报告
  const reportPath = `视觉脚本系统节点状态分析报告-${analysisResult.analysisDate}.md`;
  fs.writeFileSync(reportPath, report, 'utf8');
  
  console.log(`✅ 分析报告已生成：${reportPath}`);
  
  // 输出简要统计
  console.log('\n📊 分析结果统计：');
  console.log(`引擎注册节点：${analysisResult.engineRegisteredCount} 个`);
  console.log(`编辑器集成节点：${analysisResult.editorIntegratedCount} 个`);
  console.log(`双重注册节点：${analysisResult.editorNodes.filter(n => n.已注册到引擎 === '是').length} 个`);
  console.log(`仅引擎注册：${analysisResult.engineNodes.filter(n => n.已集成到编辑器 === '否').length} 个`);
  console.log(`仅编辑器集成：${analysisResult.editorNodes.filter(n => n.已注册到引擎 === '否').length} 个`);
}

// 执行分析
try {
  analyzeEngineNodes();
  analyzeEditorNodes();
  crossValidateNodes();
  generateReport();
  
  console.log('\n🎉 视觉脚本系统节点分析完成！');
} catch (error) {
  console.error('❌ 分析过程中发生错误：', error);
}
