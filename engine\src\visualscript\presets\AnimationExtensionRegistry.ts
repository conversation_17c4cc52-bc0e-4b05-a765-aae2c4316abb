/**
 * 动画系统扩展节点注册
 * 第7批次：动画系统扩展（节点181-210）
 */

import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory } from '../nodes/Node';

// 导入动画曲线节点
import {
  EvaluateCurveNode,
  CreateStateMachineNode,
  AddStateNode,
  AddTransitionNode,
  SetCurrentStateNode,
  Create3DAudioSourceNode,
  SetAudioPositionNode,
  SetAudioVelocityNode,
  SetListenerPositionNode,
  SetListenerOrientationNode,
  CreateReverbNode
} from './AnimationExtensionNodes';

// 导入剩余音频节点
import {
  CreateEchoNode,
  CreateFilterNode,
  CreateAnalyzerNode,
  GetFrequencyDataNode,
  GetWaveformDataNode,
  CreateAudioStreamNode,
  ConnectStreamNode,
  StartRecordingNode,
  StopRecordingNode
} from './AnimationExtensionNodes2';

// 导入场景管理节点
import {
  CreateSceneNode,
  LoadSceneNode,
  SaveSceneNode,
  SwitchSceneNode,
  AddToSceneNode,
  RemoveFromSceneNode,
  EnableFrustumCullingNode,
  EnableOcclusionCullingNode,
  EnableBatchingNode,
  EnableInstancingNode
} from './SceneManagementNodes';

/**
 * 注册第7批次动画系统扩展节点
 * @param registry 节点注册表
 */
export function registerAnimationExtensionNodes(registry: NodeRegistry): void {
  // ============================================================================
  // 动画曲线节点（181-185）
  // ============================================================================

  // 注册计算曲线值节点 (181)
  registry.registerNodeType({
    type: 'animation/curve/evaluateCurve',
    category: NodeCategory.ANIMATION,
    constructor: EvaluateCurveNode,
    label: '计算曲线值',
    description: '计算动画曲线在指定时间的值',
    icon: 'curve',
    color: '#4CAF50',
    tags: ['animation', 'curve', 'evaluate']
  });

  // 注册创建状态机节点 (182)
  registry.registerNodeType({
    type: 'animation/state/createStateMachine',
    category: NodeCategory.ANIMATION,
    constructor: CreateStateMachineNode,
    label: '创建状态机',
    description: '创建动画状态机',
    icon: 'state-machine',
    color: '#4CAF50',
    tags: ['animation', 'state', 'machine']
  });

  // 注册添加状态节点 (183)
  registry.registerNodeType({
    type: 'animation/state/addState',
    category: NodeCategory.ANIMATION,
    constructor: AddStateNode,
    label: '添加状态',
    description: '向状态机添加动画状态',
    icon: 'state-add',
    color: '#4CAF50',
    tags: ['animation', 'state', 'add']
  });

  // 注册添加过渡节点 (184)
  registry.registerNodeType({
    type: 'animation/state/addTransition',
    category: NodeCategory.ANIMATION,
    constructor: AddTransitionNode,
    label: '添加过渡',
    description: '在状态间添加过渡条件',
    icon: 'transition',
    color: '#4CAF50',
    tags: ['animation', 'state', 'transition']
  });

  // 注册设置当前状态节点 (185)
  registry.registerNodeType({
    type: 'animation/state/setCurrentState',
    category: NodeCategory.ANIMATION,
    constructor: SetCurrentStateNode,
    label: '设置当前状态',
    description: '切换到指定动画状态',
    icon: 'state-current',
    color: '#4CAF50',
    tags: ['animation', 'state', 'current']
  });

  // ============================================================================
  // 高级音频系统节点（186-200）
  // ============================================================================

  // 注册创建3D音频源节点 (186)
  registry.registerNodeType({
    type: 'audio/source/create3DAudioSource',
    category: NodeCategory.AUDIO,
    constructor: Create3DAudioSourceNode,
    label: '创建3D音频源',
    description: '创建空间音频源',
    icon: 'audio-3d',
    color: '#FF9800',
    tags: ['audio', '3d', 'source']
  });

  // 注册设置音频位置节点 (187)
  registry.registerNodeType({
    type: 'audio/source/setAudioPosition',
    category: NodeCategory.AUDIO,
    constructor: SetAudioPositionNode,
    label: '设置音频位置',
    description: '设置3D音频源位置',
    icon: 'audio-position',
    color: '#FF9800',
    tags: ['audio', 'position', '3d']
  });

  // 注册设置音频速度节点 (188)
  registry.registerNodeType({
    type: 'audio/source/setAudioVelocity',
    category: NodeCategory.AUDIO,
    constructor: SetAudioVelocityNode,
    label: '设置音频速度',
    description: '设置音频源移动速度',
    icon: 'audio-velocity',
    color: '#FF9800',
    tags: ['audio', 'velocity', 'doppler']
  });

  // 注册设置听者位置节点 (189)
  registry.registerNodeType({
    type: 'audio/listener/setListenerPosition',
    category: NodeCategory.AUDIO,
    constructor: SetListenerPositionNode,
    label: '设置听者位置',
    description: '设置音频听者位置',
    icon: 'listener-position',
    color: '#FF9800',
    tags: ['audio', 'listener', 'position']
  });

  // 注册设置听者朝向节点 (190)
  registry.registerNodeType({
    type: 'audio/listener/setListenerOrientation',
    category: NodeCategory.AUDIO,
    constructor: SetListenerOrientationNode,
    label: '设置听者朝向',
    description: '设置音频听者朝向',
    icon: 'listener-orientation',
    color: '#FF9800',
    tags: ['audio', 'listener', 'orientation']
  });

  // 注册创建混响效果节点 (191)
  registry.registerNodeType({
    type: 'audio/effect/createReverb',
    category: NodeCategory.AUDIO,
    constructor: CreateReverbNode,
    label: '创建混响效果',
    description: '创建音频混响处理器',
    icon: 'reverb',
    color: '#FF9800',
    tags: ['audio', 'effect', 'reverb']
  });

  // 注册创建回声效果节点 (192)
  registry.registerNodeType({
    type: 'audio/effect/createEcho',
    category: NodeCategory.AUDIO,
    constructor: CreateEchoNode,
    label: '创建回声效果',
    description: '创建音频回声处理器',
    icon: 'echo',
    color: '#FF9800',
    tags: ['audio', 'effect', 'echo']
  });

  // 注册创建滤波器节点 (193)
  registry.registerNodeType({
    type: 'audio/effect/createFilter',
    category: NodeCategory.AUDIO,
    constructor: CreateFilterNode,
    label: '创建滤波器',
    description: '创建音频滤波处理器',
    icon: 'filter',
    color: '#FF9800',
    tags: ['audio', 'effect', 'filter']
  });

  // 注册创建音频分析器节点 (194)
  registry.registerNodeType({
    type: 'audio/analysis/createAnalyzer',
    category: NodeCategory.AUDIO,
    constructor: CreateAnalyzerNode,
    label: '创建音频分析器',
    description: '创建音频频谱分析器',
    icon: 'analyzer',
    color: '#FF9800',
    tags: ['audio', 'analysis', 'analyzer']
  });

  // 注册获取频率数据节点 (195)
  registry.registerNodeType({
    type: 'audio/analysis/getFrequencyData',
    category: NodeCategory.AUDIO,
    constructor: GetFrequencyDataNode,
    label: '获取频率数据',
    description: '获取音频频谱数据',
    icon: 'frequency',
    color: '#FF9800',
    tags: ['audio', 'analysis', 'frequency']
  });

  // 注册获取波形数据节点 (196)
  registry.registerNodeType({
    type: 'audio/analysis/getWaveformData',
    category: NodeCategory.AUDIO,
    constructor: GetWaveformDataNode,
    label: '获取波形数据',
    description: '获取音频波形数据',
    icon: 'waveform',
    color: '#FF9800',
    tags: ['audio', 'analysis', 'waveform']
  });

  // 注册创建音频流节点 (197)
  registry.registerNodeType({
    type: 'audio/streaming/createAudioStream',
    category: NodeCategory.AUDIO,
    constructor: CreateAudioStreamNode,
    label: '创建音频流',
    description: '创建实时音频流',
    icon: 'stream',
    color: '#FF9800',
    tags: ['audio', 'streaming', 'stream']
  });

  // 注册连接音频流节点 (198)
  registry.registerNodeType({
    type: 'audio/streaming/connectStream',
    category: NodeCategory.AUDIO,
    constructor: ConnectStreamNode,
    label: '连接音频流',
    description: '连接到音频流源',
    icon: 'connect',
    color: '#FF9800',
    tags: ['audio', 'streaming', 'connect']
  });

  // 注册开始录音节点 (199)
  registry.registerNodeType({
    type: 'audio/recording/startRecording',
    category: NodeCategory.AUDIO,
    constructor: StartRecordingNode,
    label: '开始录音',
    description: '开始音频录制',
    icon: 'record-start',
    color: '#FF9800',
    tags: ['audio', 'recording', 'start']
  });

  // 注册停止录音节点 (200)
  registry.registerNodeType({
    type: 'audio/recording/stopRecording',
    category: NodeCategory.AUDIO,
    constructor: StopRecordingNode,
    label: '停止录音',
    description: '停止音频录制',
    icon: 'record-stop',
    color: '#FF9800',
    tags: ['audio', 'recording', 'stop']
  });

  // ============================================================================
  // 场景管理系统节点（201-210）
  // ============================================================================

  // 注册创建场景节点 (201)
  registry.registerNodeType({
    type: 'scene/management/createScene',
    category: NodeCategory.ENTITY,
    constructor: CreateSceneNode,
    label: '创建场景',
    description: '创建新的3D场景',
    icon: 'scene',
    color: '#2196F3',
    tags: ['scene', 'management', 'create']
  });

  // 注册加载场景节点 (202)
  registry.registerNodeType({
    type: 'scene/management/loadScene',
    category: NodeCategory.ENTITY,
    constructor: LoadSceneNode,
    label: '加载场景',
    description: '从文件加载场景',
    icon: 'scene-load',
    color: '#2196F3',
    tags: ['scene', 'management', 'load']
  });

  // 注册保存场景节点 (203)
  registry.registerNodeType({
    type: 'scene/management/saveScene',
    category: NodeCategory.ENTITY,
    constructor: SaveSceneNode,
    label: '保存场景',
    description: '保存场景到文件',
    icon: 'scene-save',
    color: '#2196F3',
    tags: ['scene', 'management', 'save']
  });

  // 注册切换场景节点 (204)
  registry.registerNodeType({
    type: 'scene/management/switchScene',
    category: NodeCategory.ENTITY,
    constructor: SwitchSceneNode,
    label: '切换场景',
    description: '切换到指定场景',
    icon: 'scene-switch',
    color: '#2196F3',
    tags: ['scene', 'management', 'switch']
  });

  // 注册添加到场景节点 (205)
  registry.registerNodeType({
    type: 'scene/management/addToScene',
    category: NodeCategory.ENTITY,
    constructor: AddToSceneNode,
    label: '添加到场景',
    description: '将对象添加到场景',
    icon: 'scene-add',
    color: '#2196F3',
    tags: ['scene', 'management', 'add']
  });

  // 注册从场景移除节点 (206)
  registry.registerNodeType({
    type: 'scene/management/removeFromScene',
    category: NodeCategory.ENTITY,
    constructor: RemoveFromSceneNode,
    label: '从场景移除',
    description: '从场景移除对象',
    icon: 'scene-remove',
    color: '#2196F3',
    tags: ['scene', 'management', 'remove']
  });

  // 注册启用视锥体剔除节点 (207)
  registry.registerNodeType({
    type: 'scene/culling/enableFrustumCulling',
    category: NodeCategory.ENTITY,
    constructor: EnableFrustumCullingNode,
    label: '启用视锥体剔除',
    description: '启用视锥体剔除优化',
    icon: 'frustum-culling',
    color: '#2196F3',
    tags: ['scene', 'culling', 'frustum']
  });

  // 注册启用遮挡剔除节点 (208)
  registry.registerNodeType({
    type: 'scene/culling/enableOcclusionCulling',
    category: NodeCategory.ENTITY,
    constructor: EnableOcclusionCullingNode,
    label: '启用遮挡剔除',
    description: '启用遮挡剔除优化',
    icon: 'occlusion-culling',
    color: '#2196F3',
    tags: ['scene', 'culling', 'occlusion']
  });

  // 注册启用批处理节点 (209)
  registry.registerNodeType({
    type: 'scene/optimization/enableBatching',
    category: NodeCategory.ENTITY,
    constructor: EnableBatchingNode,
    label: '启用批处理',
    description: '启用渲染批处理',
    icon: 'batching',
    color: '#2196F3',
    tags: ['scene', 'optimization', 'batching']
  });

  // 注册启用实例化节点 (210)
  registry.registerNodeType({
    type: 'scene/optimization/enableInstancing',
    category: NodeCategory.ENTITY,
    constructor: EnableInstancingNode,
    label: '启用实例化',
    description: '启用实例化渲染',
    icon: 'instancing',
    color: '#2196F3',
    tags: ['scene', 'optimization', 'instancing']
  });

  console.log('第7批次动画系统扩展节点注册完成：30个节点');
}
