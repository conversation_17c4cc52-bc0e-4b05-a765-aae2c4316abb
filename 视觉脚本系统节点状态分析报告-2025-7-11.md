# 视觉脚本系统节点状态分析报告
## 分析日期：2025-7-11

## 📊 统计概览

- **引擎注册节点总数**：201 个
- **编辑器集成节点总数**：360 个
- **双重注册节点数**：161 个
- **仅引擎注册节点数**：40 个
- **仅编辑器集成节点数**：199 个

## 📋 完整节点清单

### 引擎注册节点列表

| 序号 | 节点类型 | 节点中文名 | 已注册到引擎 | 已集成到编辑器 | 源文件 |
|------|----------|------------|--------------|----------------|--------|
| 001 | ai/emotion/analyze | 情感分析 | 是 | 是 | AIEmotionNodes.ts |
| 002 | ai/emotion/driveAnimation | 情感驱动动画 | 是 | 是 | AIEmotionNodes.ts |
| 003 | ai/model/load | 加载AI模型 | 是 | 是 | AIModelNodes.ts |
| 004 | ai/model/generateText | 生成文本 | 是 | 是 | AIModelNodes.ts |
| 005 | ai/model/generateImage | 生成图像 | 是 | 否 | AIModelNodes.ts |
| 006 | ai/nlp/classifyText | 文本分类 | 是 | 是 | AINLPNodes.ts |
| 007 | ai/nlp/recognizeEntities | 命名实体识别 | 是 | 是 | AINLPNodes.ts |
| 008 | ai/nlp/generateSummary | 生成文本摘要 | 是 | 否 | AINLPNodes.ts |
| 009 | ai/nlp/translateText | 语言翻译 | 是 | 否 | AINLPNodes.ts |
| 010 | ai/animation/generateBodyAnimation | 生成身体动画 | 是 | 是 | AINodes.ts |
| 011 | ai/animation/generateFacialAnimation | 生成面部动画 | 是 | 是 | AINodes.ts |
| 012 | animation/curve/evaluateCurve | 计算曲线值 | 是 | 是 | AnimationExtensionRegistry.ts |
| 013 | animation/state/createStateMachine | 创建状态机 | 是 | 是 | AnimationExtensionRegistry.ts |
| 014 | animation/state/addState | 添加状态 | 是 | 是 | AnimationExtensionRegistry.ts |
| 015 | animation/state/addTransition | 添加过渡 | 是 | 是 | AnimationExtensionRegistry.ts |
| 016 | animation/state/setCurrentState | 设置当前状态 | 是 | 是 | AnimationExtensionRegistry.ts |
| 017 | audio/source/create3DAudioSource | 创建3D音频源 | 是 | 是 | AnimationExtensionRegistry.ts |
| 018 | audio/source/setAudioPosition | 设置音频位置 | 是 | 是 | AnimationExtensionRegistry.ts |
| 019 | audio/source/setAudioVelocity | 设置音频速度 | 是 | 是 | AnimationExtensionRegistry.ts |
| 020 | audio/listener/setListenerPosition | 设置听者位置 | 是 | 是 | AnimationExtensionRegistry.ts |
| 021 | audio/listener/setListenerOrientation | 设置听者朝向 | 是 | 是 | AnimationExtensionRegistry.ts |
| 022 | audio/effect/createReverb | 创建混响效果 | 是 | 是 | AnimationExtensionRegistry.ts |
| 023 | audio/effect/createEcho | 创建回声效果 | 是 | 是 | AnimationExtensionRegistry.ts |
| 024 | audio/effect/createFilter | 创建滤波器 | 是 | 是 | AnimationExtensionRegistry.ts |
| 025 | audio/analysis/createAnalyzer | 创建音频分析器 | 是 | 是 | AnimationExtensionRegistry.ts |
| 026 | audio/analysis/getFrequencyData | 获取频率数据 | 是 | 是 | AnimationExtensionRegistry.ts |
| 027 | audio/analysis/getWaveformData | 获取波形数据 | 是 | 是 | AnimationExtensionRegistry.ts |
| 028 | audio/streaming/createAudioStream | 创建音频流 | 是 | 是 | AnimationExtensionRegistry.ts |
| 029 | audio/streaming/connectStream | 连接音频流 | 是 | 是 | AnimationExtensionRegistry.ts |
| 030 | audio/recording/startRecording | 开始录音 | 是 | 是 | AnimationExtensionRegistry.ts |
| 031 | audio/recording/stopRecording | 停止录音 | 是 | 是 | AnimationExtensionRegistry.ts |
| 032 | scene/management/createScene | 创建场景 | 是 | 是 | AnimationExtensionRegistry.ts |
| 033 | scene/management/loadScene | 加载场景 | 是 | 是 | AnimationExtensionRegistry.ts |
| 034 | scene/management/saveScene | 保存场景 | 是 | 是 | AnimationExtensionRegistry.ts |
| 035 | scene/management/switchScene | 切换场景 | 是 | 是 | AnimationExtensionRegistry.ts |
| 036 | scene/management/addToScene | 添加到场景 | 是 | 是 | AnimationExtensionRegistry.ts |
| 037 | scene/management/removeFromScene | 从场景移除 | 是 | 是 | AnimationExtensionRegistry.ts |
| 038 | scene/culling/enableFrustumCulling | 启用视锥体剔除 | 是 | 是 | AnimationExtensionRegistry.ts |
| 039 | scene/culling/enableOcclusionCulling | 启用遮挡剔除 | 是 | 是 | AnimationExtensionRegistry.ts |
| 040 | scene/optimization/enableBatching | 启用批处理 | 是 | 是 | AnimationExtensionRegistry.ts |
| 041 | scene/optimization/enableInstancing | 启用实例化 | 是 | 是 | AnimationExtensionRegistry.ts |
| 042 | animation/legacy/playAnimation | 播放动画 | 是 | 否 | AnimationNodes.ts |
| 043 | animation/legacy/stopAnimation | 停止动画 | 是 | 否 | AnimationNodes.ts |
| 044 | animation/legacy/setAnimationSpeed | 设置动画速度 | 是 | 否 | AnimationNodes.ts |
| 045 | animation/legacy/getAnimationState | 获取动画状态 | 是 | 否 | AnimationNodes.ts |
| 046 | animation/clip/createAnimationClip | 创建动画片段 | 是 | 是 | AnimationNodes.ts |
| 047 | animation/clip/addKeyframe | 添加关键帧 | 是 | 是 | AnimationNodes.ts |
| 048 | animation/clip/setInterpolation | 设置插值方式 | 是 | 是 | AnimationNodes.ts |
| 049 | animation/mixer/createAnimationMixer | 创建动画混合器 | 是 | 是 | AnimationNodes.ts |
| 050 | animation/mixer/playAnimationAction | 播放动画动作 | 是 | 否 | AnimationNodes.ts |
| 051 | animation/skeleton/createSkeletalAnimation | 创建骨骼动画 | 是 | 否 | AnimationNodes.ts |
| 052 | animation/skeleton/setBoneTransform | 设置骨骼变换 | 是 | 否 | AnimationNodes.ts |
| 053 | animation/ik/createIKConstraint | 创建IK约束 | 是 | 否 | AnimationNodes.ts |
| 054 | animation/ik/solveIK | 解算IK | 是 | 是 | AnimationNodes.ts |
| 055 | animation/morph/createMorphTarget | 创建变形目标 | 是 | 是 | AnimationNodes.ts |
| 056 | animation/morph/setMorphWeight | 设置变形权重 | 是 | 是 | AnimationNodes.ts |
| 057 | animation/curve/createAnimationCurve | 创建动画曲线 | 是 | 是 | AnimationNodes.ts |
| 058 | animation/statemachine/createStateMachine | 创建状态机 | 是 | 否 | AnimationNodes.ts |
| 059 | animation/statemachine/transitionState | 状态转换 | 是 | 否 | AnimationNodes.ts |
| 060 | array/push | 数组添加 | 是 | 是 | ArrayNodes.ts |
| 061 | array/pop | 数组弹出 | 是 | 是 | ArrayNodes.ts |
| 062 | array/length | 数组长度 | 是 | 是 | ArrayNodes.ts |
| 063 | array/get | 获取元素 | 是 | 是 | ArrayNodes.ts |
| 064 | array/set | 设置元素 | 是 | 是 | ArrayNodes.ts |
| 065 | array/indexOf | 查找索引 | 是 | 否 | ArrayNodes.ts |
| 066 | array/slice | 数组切片 | 是 | 否 | ArrayNodes.ts |
| 067 | array/sort | 数组排序 | 是 | 否 | ArrayNodes.ts |
| 068 | core/events/onStart | 开始 | 是 | 是 | CoreNodes.ts |
| 069 | core/events/onUpdate | 更新 | 是 | 是 | CoreNodes.ts |
| 070 | core/events/onEnd | 结束 | 是 | 是 | CoreNodes.ts |
| 071 | core/events/onPause | 暂停 | 是 | 是 | CoreNodes.ts |
| 072 | core/events/onResume | 恢复 | 是 | 是 | CoreNodes.ts |
| 073 | core/flow/branch | 分支 | 是 | 是 | CoreNodes.ts |
| 074 | core/flow/sequence | 序列 | 是 | 是 | CoreNodes.ts |
| 075 | core/debug/print | 打印日志 | 是 | 是 | CoreNodes.ts |
| 076 | core/flow/delay | 延时 | 是 | 是 | CoreNodes.ts |
| 077 | debug/breakpoint | 断点 | 是 | 是 | DebugNodes.ts |
| 078 | debug/log | 日志 | 是 | 是 | DebugNodes.ts |
| 079 | debug/performanceTimer | 性能计时 | 是 | 是 | DebugNodes.ts |
| 080 | debug/variableWatch | 变量监视 | 是 | 是 | DebugNodes.ts |
| 081 | debug/assert | 断言 | 是 | 是 | DebugNodes.ts |
| 082 | entity/create | 创建实体 | 是 | 是 | EntityNodes.ts |
| 083 | entity/destroy | 销毁实体 | 是 | 是 | EntityNodes.ts |
| 084 | entity/find | 查找实体 | 是 | 是 | EntityNodes.ts |
| 085 | entity/get | 获取实体 | 是 | 是 | EntityNodes.ts |
| 086 | entity/component/get | 获取组件 | 是 | 是 | EntityNodes.ts |
| 087 | entity/component/add | 添加组件 | 是 | 是 | EntityNodes.ts |
| 088 | entity/component/remove | 移除组件 | 是 | 是 | EntityNodes.ts |
| 089 | entity/component/has | 检查组件 | 是 | 否 | EntityNodes.ts |
| 090 | logic/flow/branch | 分支 | 是 | 否 | LogicNodes.ts |
| 091 | logic/comparison/equal | 相等 | 是 | 是 | LogicNodes.ts |
| 092 | logic/comparison/notEqual | 不相等 | 是 | 是 | LogicNodes.ts |
| 093 | logic/comparison/greater | 大于 | 是 | 是 | LogicNodes.ts |
| 094 | logic/comparison/greaterEqual | 大于等于 | 是 | 否 | LogicNodes.ts |
| 095 | logic/comparison/less | 小于 | 是 | 是 | LogicNodes.ts |
| 096 | logic/comparison/lessEqual | 小于等于 | 是 | 否 | LogicNodes.ts |
| 097 | logic/operation/and | 与 | 是 | 否 | LogicNodes.ts |
| 098 | logic/operation/or | 或 | 是 | 否 | LogicNodes.ts |
| 099 | logic/operation/not | 非 | 是 | 否 | LogicNodes.ts |
| 100 | logic/flow/toggle | 开关 | 是 | 否 | LogicNodes.ts |
| 101 | math/basic/add | 加法 | 是 | 是 | MathNodes.ts |
| 102 | math/basic/subtract | 减法 | 是 | 是 | MathNodes.ts |
| 103 | math/basic/multiply | 乘法 | 是 | 是 | MathNodes.ts |
| 104 | math/basic/divide | 除法 | 是 | 是 | MathNodes.ts |
| 105 | math/basic/modulo | 取模 | 是 | 是 | MathNodes.ts |
| 106 | math/advanced/power | 幂运算 | 是 | 否 | MathNodes.ts |
| 107 | math/advanced/sqrt | 平方根 | 是 | 否 | MathNodes.ts |
| 108 | math/trigonometric/sin | 正弦 | 是 | 否 | MathNodes.ts |
| 109 | math/trigonometric/cos | 余弦 | 是 | 否 | MathNodes.ts |
| 110 | math/trigonometric/tan | 正切 | 是 | 否 | MathNodes.ts |
| 111 | network/connectToServer | 连接到服务器 | 是 | 是 | NetworkNodes.ts |
| 112 | network/sendMessage | 发送网络消息 | 是 | 是 | NetworkNodes.ts |
| 113 | network/events/onMessage | 接收网络消息 | 是 | 是 | NetworkNodes.ts |
| 114 | network/protocol/udpSend | UDP发送 | 是 | 是 | NetworkProtocolNodes.ts |
| 115 | network/protocol/httpRequest | HTTP请求 | 是 | 是 | NetworkProtocolNodes.ts |
| 116 | network/security/encryptData | 数据加密 | 是 | 是 | NetworkSecurityNodes.ts |
| 117 | network/security/decryptData | 数据解密 | 是 | 是 | NetworkSecurityNodes.ts |
| 118 | network/security/computeHash | 计算哈希 | 是 | 否 | NetworkSecurityNodes.ts |
| 119 | network/security/generateSignature | 生成签名 | 是 | 否 | NetworkSecurityNodes.ts |
| 120 | network/security/verifySignature | 验证签名 | 是 | 否 | NetworkSecurityNodes.ts |
| 121 | network/security/createSession | 创建会话 | 是 | 否 | NetworkSecurityNodes.ts |
| 122 | network/security/validateSession | 验证会话 | 是 | 是 | NetworkSecurityNodes.ts |
| 123 | network/security/authenticateUser | 用户认证 | 是 | 是 | NetworkSecurityNodes.ts |
| 124 | object/getProperty | 获取属性 | 是 | 是 | ObjectNodes.ts |
| 125 | object/setProperty | 设置属性 | 是 | 是 | ObjectNodes.ts |
| 126 | object/hasProperty | 检查属性 | 是 | 是 | ObjectNodes.ts |
| 127 | object/keys | 获取键列表 | 是 | 是 | ObjectNodes.ts |
| 128 | object/values | 获取值列表 | 是 | 是 | ObjectNodes.ts |
| 129 | object/merge | 对象合并 | 是 | 是 | ObjectNodes.ts |
| 130 | object/clone | 对象克隆 | 是 | 是 | ObjectNodes.ts |
| 131 | physics/collision/onCollisionExit | 碰撞结束事件 | 是 | 是 | PhysicsNodes.ts |
| 132 | physics/collision/onTriggerEnter | 触发器进入事件 | 是 | 是 | PhysicsNodes.ts |
| 133 | physics/collision/onTriggerExit | 触发器退出事件 | 是 | 是 | PhysicsNodes.ts |
| 134 | physics/world/setGravity | 设置重力 | 是 | 是 | PhysicsNodes.ts |
| 135 | physics/world/setTimeStep | 设置时间步长 | 是 | 是 | PhysicsNodes.ts |
| 136 | physics/character/createCharacterController | 创建角色控制器 | 是 | 是 | PhysicsNodes.ts |
| 137 | physics/character/moveCharacter | 移动角色 | 是 | 是 | PhysicsNodes.ts |
| 138 | physics/character/jumpCharacter | 角色跳跃 | 是 | 是 | PhysicsNodes.ts |
| 139 | physics/vehicle/createVehicle | 创建载具 | 是 | 是 | PhysicsNodes.ts |
| 140 | physics/vehicle/setEngineForce | 设置引擎力 | 是 | 是 | PhysicsNodes.ts |
| 141 | physics/vehicle/setBrakeForce | 设置制动力 | 是 | 是 | PhysicsNodes.ts |
| 142 | physics/vehicle/setSteeringValue | 设置转向值 | 是 | 是 | PhysicsNodes.ts |
| 143 | physics/fluid/createFluidSimulation | 创建流体模拟 | 是 | 是 | PhysicsNodes.ts |
| 144 | physics/cloth/createClothSimulation | 创建布料模拟 | 是 | 是 | PhysicsNodes.ts |
| 145 | physics/destruction/createDestructible | 创建可破坏物体 | 是 | 是 | PhysicsNodes.ts |
| 146 | physics/raycast | 射线检测 | 是 | 是 | PhysicsNodes.ts |
| 147 | physics/applyForce | 应用力 | 是 | 是 | PhysicsNodes.ts |
| 148 | physics/collisionDetection | 碰撞检测 | 是 | 否 | PhysicsNodes.ts |
| 149 | physics/createConstraint | 创建约束 | 是 | 否 | PhysicsNodes.ts |
| 150 | physics/createMaterial | 创建物理材质 | 是 | 否 | PhysicsNodes.ts |
| 151 | physics/velocity/set | 设置速度 | 是 | 是 | PhysicsNodes.ts |
| 152 | rendering/camera/createPerspectiveCamera | 创建透视相机 | 是 | 是 | RenderingNodes.ts |
| 153 | rendering/camera/createOrthographicCamera | 创建正交相机 | 是 | 是 | RenderingNodes.ts |
| 154 | rendering/camera/setCameraPosition | 设置相机位置 | 是 | 是 | RenderingNodes.ts |
| 155 | rendering/camera/setCameraTarget | 设置相机目标 | 是 | 是 | RenderingNodes.ts |
| 156 | rendering/camera/setCameraFOV | 设置相机视野 | 是 | 是 | RenderingNodes.ts |
| 157 | rendering/light/createDirectionalLight | 创建方向光 | 是 | 是 | RenderingNodes.ts |
| 158 | rendering/light/createPointLight | 创建点光源 | 是 | 是 | RenderingNodes.ts |
| 159 | rendering/light/createSpotLight | 创建聚光灯 | 是 | 是 | RenderingNodes.ts |
| 160 | rendering/light/createAmbientLight | 创建环境光 | 是 | 是 | RenderingNodes.ts |
| 161 | rendering/light/setLightColor | 设置光源颜色 | 是 | 是 | RenderingNodes.ts |
| 162 | rendering/light/setLightIntensity | 设置光源强度 | 是 | 是 | RenderingNodes.ts |
| 163 | rendering/shadow/enableShadows | 启用阴影 | 是 | 是 | RenderingNodes.ts |
| 164 | rendering/shadow/setShadowMapSize | 设置阴影贴图大小 | 是 | 是 | RenderingNodes.ts |
| 165 | rendering/material/createBasicMaterial | 创建基础材质 | 是 | 是 | RenderingNodes.ts |
| 166 | rendering/material/createStandardMaterial | 创建标准材质 | 是 | 是 | RenderingNodes.ts |
| 167 | rendering/material/createPhysicalMaterial | 创建物理材质 | 是 | 是 | RenderingNodes.ts |
| 168 | rendering/material/setMaterialColor | 设置材质颜色 | 是 | 是 | RenderingNodes.ts |
| 169 | rendering/material/setMaterialTexture | 设置材质纹理 | 是 | 是 | RenderingNodes.ts |
| 170 | rendering/material/setMaterialOpacity | 设置材质透明度 | 是 | 是 | RenderingNodes.ts |
| 171 | rendering/postprocess/enableFXAA | 启用抗锯齿 | 是 | 是 | RenderingNodes.ts |
| 172 | rendering/postprocess/enableSSAO | 启用环境光遮蔽 | 是 | 是 | RenderingNodes.ts |
| 173 | rendering/postprocess/enableBloom | 启用辉光效果 | 是 | 是 | RenderingNodes.ts |
| 174 | rendering/lod/setLODLevel | 设置LOD级别 | 是 | 是 | RenderingNodes.ts |
| 175 | physics/rigidbody/createRigidBody | 创建刚体 | 是 | 是 | RenderingNodes.ts |
| 176 | physics/rigidbody/setMass | 设置质量 | 是 | 是 | RenderingNodes.ts |
| 177 | physics/rigidbody/setFriction | 设置摩擦力 | 是 | 是 | RenderingNodes.ts |
| 178 | physics/rigidbody/setRestitution | 设置弹性 | 是 | 是 | RenderingNodes.ts |
| 179 | physics/softbody/createCloth | 创建布料 | 是 | 是 | SoftBodyNodes.ts |
| 180 | physics/softbody/createRope | 创建绳索 | 是 | 是 | SoftBodyNodes.ts |
| 181 | physics/softbody/createBalloon | 创建气球 | 是 | 否 | SoftBodyNodes.ts |
| 182 | physics/softbody/createJelly | 创建果冻 | 是 | 否 | SoftBodyNodes.ts |
| 183 | physics/softbody/cut | 切割软体 | 是 | 否 | SoftBodyNodes.ts |
| 184 | string/concat | 字符串连接 | 是 | 是 | StringNodes.ts |
| 185 | string/substring | 子字符串 | 是 | 是 | StringNodes.ts |
| 186 | string/replace | 字符串替换 | 是 | 是 | StringNodes.ts |
| 187 | string/split | 字符串分割 | 是 | 是 | StringNodes.ts |
| 188 | string/length | 字符串长度 | 是 | 是 | StringNodes.ts |
| 189 | string/toUpperCase | 转大写 | 是 | 是 | StringNodes.ts |
| 190 | string/toLowerCase | 转小写 | 是 | 是 | StringNodes.ts |
| 191 | string/trim | 去除空格 | 是 | 是 | StringNodes.ts |
| 192 | variable/get | 获取变量 | 是 | 是 | VariableNodes.ts |
| 193 | variable/set | 设置变量 | 是 | 是 | VariableNodes.ts |
| 194 | variable/increment | 变量递增 | 是 | 是 | VariableNodes.ts |
| 195 | variable/decrement | 变量递减 | 是 | 是 | VariableNodes.ts |
| 196 | variable/exists | 变量存在 | 是 | 是 | VariableNodes.ts |
| 197 | variable/delete | 删除变量 | 是 | 是 | VariableNodes.ts |
| 198 | variable/type | 变量类型 | 是 | 是 | VariableNodes.ts |
| 199 | network/webrtc/createConnection | 创建WebRTC连接 | 是 | 是 | WebRTCNodes.ts |
| 200 | network/webrtc/sendDataChannelMessage | 发送数据通道消息 | 是 | 是 | WebRTCNodes.ts |
| 201 | network/webrtc/onDataChannelMessage | 数据通道消息事件 | 是 | 否 | WebRTCNodes.ts |

### 编辑器集成节点列表

| 序号 | 节点类型 | 节点中文名 | 已注册到引擎 | 已集成到编辑器 | 分类 |
|------|----------|------------|--------------|----------------|------|
| 001 | core/events/onStart | 开始事件 | 是 | 是 | EVENTS |
| 002 | core/events/onUpdate | 更新事件 | 是 | 是 | EVENTS |
| 003 | core/debug/print | 打印 | 是 | 是 | DEBUG |
| 004 | math/basic/add | 加法 | 是 | 是 | MATH |
| 005 | core/flow/delay | 延迟 | 是 | 是 | FLOW |
| 006 | core/events/onEnd | 结束事件 | 是 | 是 | EVENTS |
| 007 | core/events/onPause | 暂停事件 | 是 | 是 | EVENTS |
| 008 | core/events/onResume | 恢复事件 | 是 | 是 | EVENTS |
| 009 | math/basic/subtract | 减法 | 是 | 是 | MATH |
| 010 | math/basic/multiply | 乘法 | 是 | 是 | MATH |
| 011 | math/basic/divide | 除法 | 是 | 是 | MATH |
| 012 | math/trigonometry/sin | 正弦 | 否 | 是 | MATH |
| 013 | math/trigonometry/cos | 余弦 | 否 | 是 | MATH |
| 014 | math/vector/magnitude | 向量长度 | 否 | 是 | MATH |
| 015 | math/vector/normalize | 向量归一化 | 否 | 是 | MATH |
| 016 | math/basic/modulo | 取模 | 是 | 是 | MATH |
| 017 | logic/comparison/equal | 等于 | 是 | 是 | LOGIC |
| 018 | logic/comparison/notEqual | 不等于 | 是 | 是 | LOGIC |
| 019 | logic/comparison/greater | 大于 | 是 | 是 | LOGIC |
| 020 | logic/comparison/less | 小于 | 是 | 是 | LOGIC |
| 021 | logic/boolean/and | 逻辑与 | 否 | 是 | LOGIC |
| 022 | core/flow/branch | 分支 | 是 | 是 | FLOW |
| 023 | core/flow/sequence | 序列 | 是 | 是 | FLOW |
| 024 | entity/create | 创建实体 | 是 | 是 | ENTITY |
| 025 | entity/destroy | 销毁实体 | 是 | 是 | ENTITY |
| 026 | entity/find | 查找实体 | 是 | 是 | ENTITY |
| 027 | entity/component/add | 添加组件 | 是 | 是 | ENTITY |
| 028 | entity/component/remove | 移除组件 | 是 | 是 | ENTITY |
| 029 | physics/gravity/set | 设置重力 | 否 | 是 | PHYSICS |
| 030 | physics/collision/detect | 碰撞检测 | 否 | 是 | PHYSICS |
| 031 | physics/rigidbody/create | 创建刚体 | 否 | 是 | PHYSICS |
| 032 | physics/force/apply | 施加力 | 否 | 是 | PHYSICS |
| 033 | physics/velocity/set | 设置速度 | 是 | 是 | PHYSICS |
| 034 | entity/get | 获取实体 | 是 | 是 | ENTITY |
| 035 | entity/component/get | 获取组件 | 是 | 是 | ENTITY |
| 036 | entity/transform/getPosition | 获取位置 | 否 | 是 | ENTITY |
| 037 | entity/transform/setPosition | 设置位置 | 否 | 是 | ENTITY |
| 038 | entity/transform/getRotation | 获取旋转 | 否 | 是 | ENTITY |
| 039 | entity/transform/setRotation | 设置旋转 | 否 | 是 | ENTITY |
| 040 | physics/raycast | 射线检测 | 是 | 是 | PHYSICS |
| 041 | physics/applyForce | 应用力 | 是 | 是 | PHYSICS |
| 042 | physics/applyImpulse | 应用冲量 | 否 | 是 | PHYSICS |
| 043 | physics/setVelocity | 设置速度 | 否 | 是 | PHYSICS |
| 044 | physics/getVelocity | 获取速度 | 否 | 是 | PHYSICS |
| 045 | physics/collision/onEnter | 碰撞进入 | 否 | 是 | PHYSICS |
| 046 | physics/collision/onExit | 碰撞退出 | 否 | 是 | PHYSICS |
| 047 | physics/softbody/createCloth | 创建布料 | 是 | 是 | PHYSICS |
| 048 | physics/softbody/createRope | 创建绳索 | 是 | 是 | PHYSICS |
| 049 | physics/softbody/createSoftBody | 创建软体 | 否 | 是 | PHYSICS |
| 050 | physics/softbody/setStiffness | 设置刚度 | 否 | 是 | PHYSICS |
| 051 | physics/softbody/setDamping | 设置阻尼 | 否 | 是 | PHYSICS |
| 052 | network/connectToServer | 连接到服务器 | 是 | 是 | NETWORK |
| 053 | network/sendMessage | 发送网络消息 | 是 | 是 | NETWORK |
| 054 | network/events/onMessage | 接收网络消息 | 是 | 是 | NETWORK |
| 055 | network/disconnect | 断开连接 | 否 | 是 | NETWORK |
| 056 | ai/animation/generateBodyAnimation | 生成身体动画 | 是 | 是 | AI |
| 057 | ai/animation/generateFacialAnimation | 生成面部动画 | 是 | 是 | AI |
| 058 | ai/model/load | 加载AI模型 | 是 | 是 | AI |
| 059 | ai/model/generateText | 生成文本 | 是 | 是 | AI |
| 060 | time/delay | 延迟 | 否 | 是 | TIME |
| 061 | time/timer | 计时器 | 否 | 是 | TIME |
| 062 | animation/playAnimation | 播放动画 | 否 | 是 | ANIMATION |
| 063 | animation/stopAnimation | 停止动画 | 否 | 是 | ANIMATION |
| 064 | animation/setAnimationSpeed | 设置动画速度 | 否 | 是 | ANIMATION |
| 065 | animation/getAnimationState | 获取动画状态 | 否 | 是 | ANIMATION |
| 066 | input/keyboard | 键盘输入 | 否 | 是 | INPUT |
| 067 | input/mouse | 鼠标输入 | 否 | 是 | INPUT |
| 068 | input/gamepad | 游戏手柄输入 | 否 | 是 | INPUT |
| 069 | audio/playAudio | 播放音频 | 否 | 是 | AUDIO |
| 070 | audio/stopAudio | 停止音频 | 否 | 是 | AUDIO |
| 071 | audio/setVolume | 设置音量 | 否 | 是 | AUDIO |
| 072 | audio/analyzer | 音频分析 | 否 | 是 | AUDIO |
| 073 | audio/audio3D | 3D音频 | 否 | 是 | AUDIO |
| 074 | debug/breakpoint | 断点 | 是 | 是 | DEBUG |
| 075 | debug/log | 日志 | 是 | 是 | DEBUG |
| 076 | debug/performanceTimer | 性能计时 | 是 | 是 | DEBUG |
| 077 | debug/variableWatch | 变量监视 | 是 | 是 | DEBUG |
| 078 | debug/assert | 断言 | 是 | 是 | DEBUG |
| 079 | network/security/encryptData | 数据加密 | 是 | 是 | NETWORK |
| 080 | network/security/decryptData | 数据解密 | 是 | 是 | NETWORK |
| 081 | network/security/hashData | 数据哈希 | 否 | 是 | NETWORK |
| 082 | network/security/authenticateUser | 用户认证 | 是 | 是 | NETWORK |
| 083 | network/security/validateSession | 验证会话 | 是 | 是 | NETWORK |
| 084 | network/webrtc/createConnection | 创建WebRTC连接 | 是 | 是 | NETWORK |
| 085 | network/webrtc/sendDataChannelMessage | 发送数据通道消息 | 是 | 是 | NETWORK |
| 086 | network/webrtc/createDataChannel | 创建数据通道 | 否 | 是 | NETWORK |
| 087 | network/webrtc/closeConnection | 关闭WebRTC连接 | 否 | 是 | NETWORK |
| 088 | ai/emotion/analyze | 情感分析 | 是 | 是 | AI |
| 089 | ai/emotion/driveAnimation | 情感驱动动画 | 是 | 是 | AI |
| 090 | ai/nlp/classifyText | 文本分类 | 是 | 是 | AI |
| 091 | ai/nlp/recognizeEntities | 命名实体识别 | 是 | 是 | AI |
| 092 | ai/nlp/analyzeSentiment | 情感分析 | 否 | 是 | AI |
| 093 | ai/nlp/extractKeywords | 关键词提取 | 否 | 是 | AI |
| 094 | network/protocol/udpSend | UDP发送 | 是 | 是 | NETWORK |
| 095 | network/protocol/httpRequest | HTTP请求 | 是 | 是 | NETWORK |
| 096 | network/protocol/tcpConnect | TCP连接 | 否 | 是 | NETWORK |
| 097 | string/concat | 字符串连接 | 是 | 是 | STRING |
| 098 | string/substring | 子字符串 | 是 | 是 | STRING |
| 099 | string/replace | 字符串替换 | 是 | 是 | STRING |
| 100 | string/split | 字符串分割 | 是 | 是 | STRING |
| 101 | string/length | 字符串长度 | 是 | 是 | STRING |
| 102 | string/toUpperCase | 转大写 | 是 | 是 | STRING |
| 103 | string/toLowerCase | 转小写 | 是 | 是 | STRING |
| 104 | string/trim | 去除空格 | 是 | 是 | STRING |
| 105 | array/push | 数组添加 | 是 | 是 | ARRAY |
| 106 | array/pop | 数组弹出 | 是 | 是 | ARRAY |
| 107 | array/length | 数组长度 | 是 | 是 | ARRAY |
| 108 | array/get | 获取元素 | 是 | 是 | ARRAY |
| 109 | array/set | 设置元素 | 是 | 是 | ARRAY |
| 110 | object/getProperty | 获取属性 | 是 | 是 | OBJECT |
| 111 | object/setProperty | 设置属性 | 是 | 是 | OBJECT |
| 112 | object/hasProperty | 检查属性 | 是 | 是 | OBJECT |
| 113 | object/keys | 获取键列表 | 是 | 是 | OBJECT |
| 114 | object/values | 获取值列表 | 是 | 是 | OBJECT |
| 115 | object/merge | 对象合并 | 是 | 是 | OBJECT |
| 116 | object/clone | 对象克隆 | 是 | 是 | OBJECT |
| 117 | variable/get | 获取变量 | 是 | 是 | VARIABLE |
| 118 | variable/set | 设置变量 | 是 | 是 | VARIABLE |
| 119 | variable/increment | 变量递增 | 是 | 是 | VARIABLE |
| 120 | variable/decrement | 变量递减 | 是 | 是 | VARIABLE |
| 121 | variable/exists | 变量存在 | 是 | 是 | VARIABLE |
| 122 | variable/delete | 删除变量 | 是 | 是 | VARIABLE |
| 123 | variable/type | 变量类型 | 是 | 是 | VARIABLE |
| 124 | rendering/camera/createPerspectiveCamera | 创建透视相机 | 是 | 是 | RENDERING |
| 125 | rendering/camera/createOrthographicCamera | 创建正交相机 | 是 | 是 | RENDERING |
| 126 | rendering/camera/setCameraPosition | 设置相机位置 | 是 | 是 | RENDERING |
| 127 | rendering/camera/setCameraTarget | 设置相机目标 | 是 | 是 | RENDERING |
| 128 | rendering/camera/setCameraFOV | 设置相机视野 | 是 | 是 | RENDERING |
| 129 | rendering/light/createDirectionalLight | 创建方向光 | 是 | 是 | RENDERING |
| 130 | rendering/light/createPointLight | 创建点光源 | 是 | 是 | RENDERING |
| 131 | rendering/light/createSpotLight | 创建聚光灯 | 是 | 是 | RENDERING |
| 132 | rendering/light/createAmbientLight | 创建环境光 | 是 | 是 | RENDERING |
| 133 | rendering/light/setLightColor | 设置光源颜色 | 是 | 是 | RENDERING |
| 134 | rendering/light/setLightIntensity | 设置光源强度 | 是 | 是 | RENDERING |
| 135 | rendering/shadow/enableShadows | 启用阴影 | 是 | 是 | RENDERING |
| 136 | rendering/shadow/setShadowMapSize | 设置阴影贴图大小 | 是 | 是 | RENDERING |
| 137 | rendering/material/createBasicMaterial | 创建基础材质 | 是 | 是 | RENDERING |
| 138 | rendering/material/createStandardMaterial | 创建标准材质 | 是 | 是 | RENDERING |
| 139 | rendering/material/createPhysicalMaterial | 创建物理材质 | 是 | 是 | RENDERING |
| 140 | rendering/material/setMaterialColor | 设置材质颜色 | 是 | 是 | RENDERING |
| 141 | rendering/material/setMaterialTexture | 设置材质纹理 | 是 | 是 | RENDERING |
| 142 | rendering/material/setMaterialOpacity | 设置材质透明度 | 是 | 是 | RENDERING |
| 143 | rendering/postprocess/enableFXAA | 启用抗锯齿 | 是 | 是 | RENDERING |
| 144 | rendering/postprocess/enableSSAO | 启用环境光遮蔽 | 是 | 是 | RENDERING |
| 145 | rendering/postprocess/enableBloom | 启用辉光效果 | 是 | 是 | RENDERING |
| 146 | rendering/lod/setLODLevel | 设置LOD级别 | 是 | 是 | RENDERING |
| 147 | physics/rigidbody/createRigidBody | 创建刚体 | 是 | 是 | PHYSICS |
| 148 | physics/rigidbody/setMass | 设置质量 | 是 | 是 | PHYSICS |
| 149 | physics/rigidbody/setFriction | 设置摩擦力 | 是 | 是 | PHYSICS |
| 150 | physics/rigidbody/setRestitution | 设置弹性 | 是 | 是 | PHYSICS |
| 151 | physics/advanced/createSoftBody | 创建软体 | 否 | 是 | PHYSICS |
| 152 | physics/advanced/createFluid | 创建流体 | 否 | 是 | PHYSICS |
| 153 | physics/advanced/createCloth | 创建布料 | 否 | 是 | PHYSICS |
| 154 | physics/advanced/createParticleSystem | 创建粒子系统 | 否 | 是 | PHYSICS |
| 155 | physics/advanced/setGravity | 设置重力 | 否 | 是 | PHYSICS |
| 156 | physics/advanced/createJoint | 创建关节 | 否 | 是 | PHYSICS |
| 157 | physics/advanced/setDamping | 设置阻尼 | 否 | 是 | PHYSICS |
| 158 | physics/advanced/createConstraint | 创建约束 | 否 | 是 | PHYSICS |
| 159 | physics/advanced/simulateWind | 模拟风力 | 否 | 是 | PHYSICS |
| 160 | physics/advanced/createExplosion | 创建爆炸 | 否 | 是 | PHYSICS |
| 161 | physics/collision/onCollisionExit | 碰撞结束事件 | 是 | 是 | EVENTS |
| 162 | physics/collision/onTriggerEnter | 触发器进入事件 | 是 | 是 | EVENTS |
| 163 | physics/collision/onTriggerExit | 触发器退出事件 | 是 | 是 | EVENTS |
| 164 | physics/world/setGravity | 设置重力 | 是 | 是 | PHYSICS |
| 165 | physics/world/setTimeStep | 设置时间步长 | 是 | 是 | PHYSICS |
| 166 | physics/character/createCharacterController | 创建角色控制器 | 是 | 是 | PHYSICS |
| 167 | physics/character/moveCharacter | 移动角色 | 是 | 是 | PHYSICS |
| 168 | physics/character/jumpCharacter | 角色跳跃 | 是 | 是 | PHYSICS |
| 169 | physics/vehicle/createVehicle | 创建载具 | 是 | 是 | PHYSICS |
| 170 | physics/vehicle/setEngineForce | 设置引擎力 | 是 | 是 | PHYSICS |
| 171 | physics/vehicle/setBrakeForce | 设置制动力 | 是 | 是 | PHYSICS |
| 172 | physics/vehicle/setSteeringValue | 设置转向值 | 是 | 是 | PHYSICS |
| 173 | physics/fluid/createFluidSimulation | 创建流体模拟 | 是 | 是 | PHYSICS |
| 174 | physics/cloth/createClothSimulation | 创建布料模拟 | 是 | 是 | PHYSICS |
| 175 | physics/destruction/createDestructible | 创建可破坏物体 | 是 | 是 | PHYSICS |
| 176 | animation/clip/createAnimationClip | 创建动画片段 | 是 | 是 | ANIMATION |
| 177 | animation/clip/addKeyframe | 添加关键帧 | 是 | 是 | ANIMATION |
| 178 | animation/clip/setInterpolation | 设置插值方式 | 是 | 是 | ANIMATION |
| 179 | animation/mixer/createAnimationMixer | 创建动画混合器 | 是 | 是 | ANIMATION |
| 180 | animation/mixer/playClip | 播放动画片段 | 否 | 是 | ANIMATION |
| 181 | animation/mixer/stopClip | 停止动画片段 | 否 | 是 | ANIMATION |
| 182 | animation/mixer/crossFade | 交叉淡化 | 否 | 是 | ANIMATION |
| 183 | animation/mixer/setWeight | 设置动画权重 | 否 | 是 | ANIMATION |
| 184 | animation/bone/getBoneTransform | 获取骨骼变换 | 否 | 是 | ANIMATION |
| 185 | animation/bone/setBoneTransform | 设置骨骼变换 | 否 | 是 | ANIMATION |
| 186 | animation/ik/createIKChain | 创建IK链 | 否 | 是 | ANIMATION |
| 187 | animation/ik/solveIK | 解算IK | 是 | 是 | ANIMATION |
| 188 | animation/morph/createMorphTarget | 创建变形目标 | 是 | 是 | ANIMATION |
| 189 | animation/morph/setMorphWeight | 设置变形权重 | 是 | 是 | ANIMATION |
| 190 | animation/curve/createAnimationCurve | 创建动画曲线 | 是 | 是 | ANIMATION |
| 191 | animation/curve/evaluateCurve | 计算曲线值 | 是 | 是 | ANIMATION |
| 192 | animation/state/createStateMachine | 创建状态机 | 是 | 是 | ANIMATION |
| 193 | animation/state/addState | 添加状态 | 是 | 是 | ANIMATION |
| 194 | animation/state/addTransition | 添加过渡 | 是 | 是 | ANIMATION |
| 195 | animation/state/setCurrentState | 设置当前状态 | 是 | 是 | ANIMATION |
| 196 | audio/source/create3DAudioSource | 创建3D音频源 | 是 | 是 | AUDIO |
| 197 | audio/source/setAudioPosition | 设置音频位置 | 是 | 是 | AUDIO |
| 198 | audio/source/setAudioVelocity | 设置音频速度 | 是 | 是 | AUDIO |
| 199 | audio/listener/setListenerPosition | 设置听者位置 | 是 | 是 | AUDIO |
| 200 | audio/listener/setListenerOrientation | 设置听者朝向 | 是 | 是 | AUDIO |
| 201 | audio/effect/createReverb | 创建混响效果 | 是 | 是 | AUDIO |
| 202 | audio/effect/createEcho | 创建回声效果 | 是 | 是 | AUDIO |
| 203 | audio/effect/createFilter | 创建滤波器 | 是 | 是 | AUDIO |
| 204 | audio/analysis/createAnalyzer | 创建音频分析器 | 是 | 是 | AUDIO |
| 205 | audio/analysis/getFrequencyData | 获取频率数据 | 是 | 是 | AUDIO |
| 206 | audio/analysis/getWaveformData | 获取波形数据 | 是 | 是 | AUDIO |
| 207 | audio/streaming/createAudioStream | 创建音频流 | 是 | 是 | AUDIO |
| 208 | audio/streaming/connectStream | 连接音频流 | 是 | 是 | AUDIO |
| 209 | audio/recording/startRecording | 开始录音 | 是 | 是 | AUDIO |
| 210 | audio/recording/stopRecording | 停止录音 | 是 | 是 | AUDIO |
| 211 | scene/management/createScene | 创建场景 | 是 | 是 | ENTITY |
| 212 | scene/management/loadScene | 加载场景 | 是 | 是 | ENTITY |
| 213 | scene/management/saveScene | 保存场景 | 是 | 是 | ENTITY |
| 214 | scene/management/switchScene | 切换场景 | 是 | 是 | ENTITY |
| 215 | scene/management/addToScene | 添加到场景 | 是 | 是 | ENTITY |
| 216 | scene/management/removeFromScene | 从场景移除 | 是 | 是 | ENTITY |
| 217 | scene/culling/enableFrustumCulling | 启用视锥体剔除 | 是 | 是 | ENTITY |
| 218 | scene/culling/enableOcclusionCulling | 启用遮挡剔除 | 是 | 是 | ENTITY |
| 219 | scene/optimization/enableBatching | 启用批处理 | 是 | 是 | ENTITY |
| 220 | scene/optimization/enableInstancing | 启用实例化 | 是 | 是 | ENTITY |
| 221 | scene/skybox/setSkybox | 设置天空盒 | 否 | 是 | ENTITY |
| 222 | scene/fog/enableFog | 启用雾效 | 否 | 是 | ENTITY |
| 223 | scene/fog/setFogColor | 设置雾颜色 | 否 | 是 | ENTITY |
| 224 | scene/fog/setFogDensity | 设置雾密度 | 否 | 是 | ENTITY |
| 225 | scene/environment/setEnvironmentMap | 设置环境贴图 | 否 | 是 | ENTITY |
| 226 | particles/system/createParticleSystem | 创建粒子系统 | 否 | 是 | ENTITY |
| 227 | particles/emitter/createEmitter | 创建发射器 | 否 | 是 | ENTITY |
| 228 | particles/emitter/setEmissionRate | 设置发射速率 | 否 | 是 | ENTITY |
| 229 | particles/emitter/setEmissionShape | 设置发射形状 | 否 | 是 | ENTITY |
| 230 | particles/particle/setLifetime | 设置粒子寿命 | 否 | 是 | ENTITY |
| 231 | particles/particle/setVelocity | 设置粒子速度 | 否 | 是 | ENTITY |
| 232 | particles/particle/setSize | 设置粒子大小 | 否 | 是 | ENTITY |
| 233 | particles/particle/setColor | 设置粒子颜色 | 否 | 是 | ENTITY |
| 234 | particles/forces/addGravity | 添加重力 | 否 | 是 | ENTITY |
| 235 | particles/forces/addWind | 添加风力 | 否 | 是 | ENTITY |
| 236 | particles/forces/addTurbulence | 添加湍流 | 否 | 是 | ENTITY |
| 237 | particles/collision/enableCollision | 启用粒子碰撞 | 否 | 是 | ENTITY |
| 238 | particles/material/setParticleMaterial | 设置粒子材质 | 否 | 是 | ENTITY |
| 239 | particles/animation/animateSize | 动画粒子大小 | 否 | 是 | ANIMATION |
| 240 | particles/animation/animateColor | 动画粒子颜色 | 否 | 是 | ANIMATION |
| 241 | terrain/generation/createTerrain | 创建地形 | 否 | 是 | ENTITY |
| 242 | terrain/generation/generateHeightmap | 生成高度图 | 否 | 是 | ENTITY |
| 243 | terrain/generation/applyNoise | 应用噪声 | 否 | 是 | ENTITY |
| 244 | terrain/texture/setTerrainTexture | 设置地形纹理 | 否 | 是 | ENTITY |
| 245 | terrain/texture/blendTextures | 混合纹理 | 否 | 是 | ENTITY |
| 246 | terrain/lod/enableTerrainLOD | 启用地形LOD | 否 | 是 | ENTITY |
| 247 | terrain/collision/enableTerrainCollision | 启用地形碰撞 | 否 | 是 | ENTITY |
| 248 | water/system/createWaterSurface | 创建水面 | 否 | 是 | ENTITY |
| 249 | water/waves/addWaves | 添加波浪 | 否 | 是 | ENTITY |
| 250 | water/reflection/enableReflection | 启用水面反射 | 否 | 是 | ENTITY |
| 251 | water/refraction/enableRefraction | 启用水面折射 | 否 | 是 | ENTITY |
| 252 | vegetation/system/createVegetation | 创建植被 | 否 | 是 | ENTITY |
| 253 | vegetation/grass/addGrass | 添加草地 | 否 | 是 | ENTITY |
| 254 | vegetation/trees/addTrees | 添加树木 | 否 | 是 | ENTITY |
| 255 | weather/system/createWeatherSystem | 创建天气系统 | 否 | 是 | ENTITY |
| 256 | weather/rain/enableRain | 启用雨效 | 否 | 是 | ENTITY |
| 257 | weather/snow/enableSnow | 启用雪效 | 否 | 是 | ENTITY |
| 258 | weather/wind/setWindDirection | 设置风向 | 否 | 是 | ENTITY |
| 259 | weather/wind/setWindStrength | 设置风力 | 否 | 是 | ENTITY |
| 260 | environment/time/setTimeOfDay | 设置时间 | 否 | 是 | ENTITY |
| 261 | editor/project/createProject | 创建项目 | 否 | 是 | CUSTOM |
| 262 | editor/project/openProject | 打开项目 | 否 | 是 | CUSTOM |
| 263 | editor/project/saveProject | 保存项目 | 否 | 是 | CUSTOM |
| 264 | editor/project/closeProject | 关闭项目 | 否 | 是 | CUSTOM |
| 265 | editor/project/exportProject | 导出项目 | 否 | 是 | CUSTOM |
| 266 | editor/project/importProject | 导入项目 | 否 | 是 | CUSTOM |
| 267 | editor/project/setProjectSettings | 设置项目配置 | 否 | 是 | CUSTOM |
| 268 | editor/project/getProjectInfo | 获取项目信息 | 否 | 是 | CUSTOM |
| 269 | editor/asset/importAsset | 导入资产 | 否 | 是 | CUSTOM |
| 270 | editor/asset/deleteAsset | 删除资产 | 否 | 是 | CUSTOM |
| 271 | editor/asset/renameAsset | 重命名资产 | 否 | 是 | CUSTOM |
| 272 | editor/asset/moveAsset | 移动资产 | 否 | 是 | CUSTOM |
| 273 | editor/asset/createFolder | 创建文件夹 | 否 | 是 | CUSTOM |
| 274 | editor/asset/getAssetInfo | 获取资产信息 | 否 | 是 | CUSTOM |
| 275 | editor/asset/generateThumbnail | 生成缩略图 | 否 | 是 | CUSTOM |
| 276 | editor/scene/createEntity | 创建实体 | 否 | 是 | ENTITY |
| 277 | editor/scene/deleteEntity | 删除实体 | 否 | 是 | ENTITY |
| 278 | editor/scene/selectEntity | 选择实体 | 否 | 是 | ENTITY |
| 279 | editor/scene/duplicateEntity | 复制实体 | 否 | 是 | ENTITY |
| 280 | editor/scene/groupEntities | 组合实体 | 否 | 是 | ENTITY |
| 281 | editor/scene/ungroupEntities | 取消组合 | 否 | 是 | ENTITY |
| 282 | editor/scene/setEntityParent | 设置父对象 | 否 | 是 | ENTITY |
| 283 | editor/scene/moveEntity | 移动实体 | 否 | 是 | ENTITY |
| 284 | editor/scene/rotateEntity | 旋转实体 | 否 | 是 | ENTITY |
| 285 | editor/scene/scaleEntity | 缩放实体 | 否 | 是 | ENTITY |
| 286 | editor/scene/hideEntity | 隐藏实体 | 否 | 是 | ENTITY |
| 287 | editor/scene/showEntity | 显示实体 | 否 | 是 | ENTITY |
| 288 | editor/scene/lockEntity | 锁定实体 | 否 | 是 | ENTITY |
| 289 | editor/scene/unlockEntity | 解锁实体 | 否 | 是 | ENTITY |
| 290 | editor/scene/focusOnEntity | 聚焦实体 | 否 | 是 | ENTITY |
| 291 | editor/ui/createUIElement | 创建UI元素 | 否 | 是 | CUSTOM |
| 292 | editor/ui/deleteUIElement | 删除UI元素 | 否 | 是 | CUSTOM |
| 293 | editor/ui/setUIPosition | 设置UI位置 | 否 | 是 | CUSTOM |
| 294 | editor/ui/setUISize | 设置UI大小 | 否 | 是 | CUSTOM |
| 295 | editor/ui/setUIText | 设置UI文本 | 否 | 是 | CUSTOM |
| 296 | editor/ui/setUIColor | 设置UI颜色 | 否 | 是 | CUSTOM |
| 297 | editor/ui/setUIFont | 设置UI字体 | 否 | 是 | CUSTOM |
| 298 | editor/ui/setUIImage | 设置UI图像 | 否 | 是 | CUSTOM |
| 299 | editor/ui/addUIEvent | 添加UI事件 | 否 | 是 | CUSTOM |
| 300 | editor/ui/removeUIEvent | 移除UI事件 | 否 | 是 | CUSTOM |
| 301 | editor/ui/setUIVisible | 设置UI可见性 | 否 | 是 | CUSTOM |
| 302 | editor/ui/setUIEnabled | 设置UI启用状态 | 否 | 是 | CUSTOM |
| 303 | editor/ui/setUILayer | 设置UI层级 | 否 | 是 | CUSTOM |
| 304 | editor/ui/alignUIElements | 对齐UI元素 | 否 | 是 | CUSTOM |
| 305 | editor/ui/distributeUIElements | 分布UI元素 | 否 | 是 | CUSTOM |
| 306 | editor/tools/enableGizmo | 启用操作手柄 | 否 | 是 | CUSTOM |
| 307 | editor/tools/setGizmoMode | 设置手柄模式 | 否 | 是 | CUSTOM |
| 308 | editor/tools/enableGrid | 启用网格 | 否 | 是 | CUSTOM |
| 309 | editor/tools/setGridSize | 设置网格大小 | 否 | 是 | CUSTOM |
| 310 | editor/tools/enableSnap | 启用吸附 | 否 | 是 | CUSTOM |
| 311 | server/user/registerUser | 用户注册 | 否 | 是 | NETWORK |
| 312 | server/user/loginUser | 用户登录 | 否 | 是 | NETWORK |
| 313 | server/user/logoutUser | 用户登出 | 否 | 是 | NETWORK |
| 314 | server/user/updateUserProfile | 更新用户资料 | 否 | 是 | NETWORK |
| 315 | server/user/changePassword | 修改密码 | 否 | 是 | NETWORK |
| 316 | server/user/resetPassword | 重置密码 | 否 | 是 | NETWORK |
| 317 | server/user/getUserInfo | 获取用户信息 | 否 | 是 | NETWORK |
| 318 | server/user/deleteUser | 删除用户 | 否 | 是 | NETWORK |
| 319 | server/user/setUserRole | 设置用户角色 | 否 | 是 | NETWORK |
| 320 | server/user/validateToken | 验证令牌 | 否 | 是 | NETWORK |
| 321 | server/project/createProject | 创建服务器项目 | 否 | 是 | NETWORK |
| 322 | server/project/deleteProject | 删除服务器项目 | 否 | 是 | NETWORK |
| 323 | server/project/updateProject | 更新项目信息 | 否 | 是 | NETWORK |
| 324 | server/project/getProjectList | 获取项目列表 | 否 | 是 | NETWORK |
| 325 | server/project/getProjectDetails | 获取项目详情 | 否 | 是 | NETWORK |
| 326 | server/project/shareProject | 分享项目 | 否 | 是 | NETWORK |
| 327 | server/project/unshareProject | 取消分享 | 否 | 是 | NETWORK |
| 328 | server/project/setProjectPermission | 设置项目权限 | 否 | 是 | NETWORK |
| 329 | server/project/forkProject | 复制项目 | 否 | 是 | NETWORK |
| 330 | server/project/archiveProject | 归档项目 | 否 | 是 | NETWORK |
| 331 | server/project/restoreProject | 恢复项目 | 否 | 是 | NETWORK |
| 332 | server/project/exportProjectData | 导出项目数据 | 否 | 是 | NETWORK |
| 333 | server/project/importProjectData | 导入项目数据 | 否 | 是 | NETWORK |
| 334 | server/project/getProjectStats | 获取项目统计 | 否 | 是 | NETWORK |
| 335 | server/project/backupProject | 备份项目 | 否 | 是 | NETWORK |
| 336 | server/asset/uploadAsset | 上传资产 | 否 | 是 | NETWORK |
| 337 | server/asset/downloadAsset | 下载资产 | 否 | 是 | NETWORK |
| 338 | server/asset/deleteAsset | 删除服务器资产 | 否 | 是 | NETWORK |
| 339 | server/asset/getAssetList | 获取资产列表 | 否 | 是 | NETWORK |
| 340 | server/asset/getAssetInfo | 获取资产信息 | 否 | 是 | NETWORK |
| 341 | server/asset/updateAssetInfo | 更新资产信息 | 否 | 是 | NETWORK |
| 342 | server/asset/moveAssetToFolder | 移动资产到文件夹 | 否 | 是 | NETWORK |
| 343 | server/asset/createAssetFolder | 创建资产文件夹 | 否 | 是 | NETWORK |
| 344 | server/asset/deleteAssetFolder | 删除资产文件夹 | 否 | 是 | NETWORK |
| 345 | server/asset/shareAsset | 分享资产 | 否 | 是 | NETWORK |
| 346 | server/asset/getAssetVersions | 获取资产版本 | 否 | 是 | NETWORK |
| 347 | server/asset/createAssetVersion | 创建资产版本 | 否 | 是 | NETWORK |
| 348 | server/asset/restoreAssetVersion | 恢复资产版本 | 否 | 是 | NETWORK |
| 349 | server/asset/generateAssetThumbnail | 生成资产缩略图 | 否 | 是 | NETWORK |
| 350 | server/asset/optimizeAsset | 优化资产 | 否 | 是 | NETWORK |
| 351 | server/collaboration/joinRoom | 加入协作房间 | 否 | 是 | NETWORK |
| 352 | server/collaboration/leaveRoom | 离开协作房间 | 否 | 是 | NETWORK |
| 353 | server/collaboration/sendOperation | 发送协作操作 | 否 | 是 | NETWORK |
| 354 | server/collaboration/receiveOperation | 接收协作操作 | 否 | 是 | NETWORK |
| 355 | server/collaboration/resolveConflict | 解决编辑冲突 | 否 | 是 | NETWORK |
| 356 | server/collaboration/getOnlineUsers | 获取在线用户 | 否 | 是 | NETWORK |
| 357 | server/collaboration/broadcastMessage | 广播消息 | 否 | 是 | NETWORK |
| 358 | server/collaboration/lockResource | 锁定资源 | 否 | 是 | NETWORK |
| 359 | server/collaboration/unlockResource | 解锁资源 | 否 | 是 | NETWORK |
| 360 | server/collaboration/syncState | 同步状态 | 否 | 是 | NETWORK |

## 🔍 问题分析

### 仅在引擎注册但未集成到编辑器的节点
001. ai/model/generateImage - 生成图像
002. ai/nlp/generateSummary - 生成文本摘要
003. ai/nlp/translateText - 语言翻译
004. animation/legacy/playAnimation - 播放动画
005. animation/legacy/stopAnimation - 停止动画
006. animation/legacy/setAnimationSpeed - 设置动画速度
007. animation/legacy/getAnimationState - 获取动画状态
008. animation/mixer/playAnimationAction - 播放动画动作
009. animation/skeleton/createSkeletalAnimation - 创建骨骼动画
010. animation/skeleton/setBoneTransform - 设置骨骼变换
011. animation/ik/createIKConstraint - 创建IK约束
012. animation/statemachine/createStateMachine - 创建状态机
013. animation/statemachine/transitionState - 状态转换
014. array/indexOf - 查找索引
015. array/slice - 数组切片
016. array/sort - 数组排序
017. entity/component/has - 检查组件
018. logic/flow/branch - 分支
019. logic/comparison/greaterEqual - 大于等于
020. logic/comparison/lessEqual - 小于等于
021. logic/operation/and - 与
022. logic/operation/or - 或
023. logic/operation/not - 非
024. logic/flow/toggle - 开关
025. math/advanced/power - 幂运算
026. math/advanced/sqrt - 平方根
027. math/trigonometric/sin - 正弦
028. math/trigonometric/cos - 余弦
029. math/trigonometric/tan - 正切
030. network/security/computeHash - 计算哈希
031. network/security/generateSignature - 生成签名
032. network/security/verifySignature - 验证签名
033. network/security/createSession - 创建会话
034. physics/collisionDetection - 碰撞检测
035. physics/createConstraint - 创建约束
036. physics/createMaterial - 创建物理材质
037. physics/softbody/createBalloon - 创建气球
038. physics/softbody/createJelly - 创建果冻
039. physics/softbody/cut - 切割软体
040. network/webrtc/onDataChannelMessage - 数据通道消息事件

### 仅在编辑器集成但未注册到引擎的节点
001. math/trigonometry/sin - 正弦
002. math/trigonometry/cos - 余弦
003. math/vector/magnitude - 向量长度
004. math/vector/normalize - 向量归一化
005. logic/boolean/and - 逻辑与
006. physics/gravity/set - 设置重力
007. physics/collision/detect - 碰撞检测
008. physics/rigidbody/create - 创建刚体
009. physics/force/apply - 施加力
010. entity/transform/getPosition - 获取位置
011. entity/transform/setPosition - 设置位置
012. entity/transform/getRotation - 获取旋转
013. entity/transform/setRotation - 设置旋转
014. physics/applyImpulse - 应用冲量
015. physics/setVelocity - 设置速度
016. physics/getVelocity - 获取速度
017. physics/collision/onEnter - 碰撞进入
018. physics/collision/onExit - 碰撞退出
019. physics/softbody/createSoftBody - 创建软体
020. physics/softbody/setStiffness - 设置刚度
021. physics/softbody/setDamping - 设置阻尼
022. network/disconnect - 断开连接
023. time/delay - 延迟
024. time/timer - 计时器
025. animation/playAnimation - 播放动画
026. animation/stopAnimation - 停止动画
027. animation/setAnimationSpeed - 设置动画速度
028. animation/getAnimationState - 获取动画状态
029. input/keyboard - 键盘输入
030. input/mouse - 鼠标输入
031. input/gamepad - 游戏手柄输入
032. audio/playAudio - 播放音频
033. audio/stopAudio - 停止音频
034. audio/setVolume - 设置音量
035. audio/analyzer - 音频分析
036. audio/audio3D - 3D音频
037. network/security/hashData - 数据哈希
038. network/webrtc/createDataChannel - 创建数据通道
039. network/webrtc/closeConnection - 关闭WebRTC连接
040. ai/nlp/analyzeSentiment - 情感分析
041. ai/nlp/extractKeywords - 关键词提取
042. network/protocol/tcpConnect - TCP连接
043. physics/advanced/createSoftBody - 创建软体
044. physics/advanced/createFluid - 创建流体
045. physics/advanced/createCloth - 创建布料
046. physics/advanced/createParticleSystem - 创建粒子系统
047. physics/advanced/setGravity - 设置重力
048. physics/advanced/createJoint - 创建关节
049. physics/advanced/setDamping - 设置阻尼
050. physics/advanced/createConstraint - 创建约束
051. physics/advanced/simulateWind - 模拟风力
052. physics/advanced/createExplosion - 创建爆炸
053. animation/mixer/playClip - 播放动画片段
054. animation/mixer/stopClip - 停止动画片段
055. animation/mixer/crossFade - 交叉淡化
056. animation/mixer/setWeight - 设置动画权重
057. animation/bone/getBoneTransform - 获取骨骼变换
058. animation/bone/setBoneTransform - 设置骨骼变换
059. animation/ik/createIKChain - 创建IK链
060. scene/skybox/setSkybox - 设置天空盒
061. scene/fog/enableFog - 启用雾效
062. scene/fog/setFogColor - 设置雾颜色
063. scene/fog/setFogDensity - 设置雾密度
064. scene/environment/setEnvironmentMap - 设置环境贴图
065. particles/system/createParticleSystem - 创建粒子系统
066. particles/emitter/createEmitter - 创建发射器
067. particles/emitter/setEmissionRate - 设置发射速率
068. particles/emitter/setEmissionShape - 设置发射形状
069. particles/particle/setLifetime - 设置粒子寿命
070. particles/particle/setVelocity - 设置粒子速度
071. particles/particle/setSize - 设置粒子大小
072. particles/particle/setColor - 设置粒子颜色
073. particles/forces/addGravity - 添加重力
074. particles/forces/addWind - 添加风力
075. particles/forces/addTurbulence - 添加湍流
076. particles/collision/enableCollision - 启用粒子碰撞
077. particles/material/setParticleMaterial - 设置粒子材质
078. particles/animation/animateSize - 动画粒子大小
079. particles/animation/animateColor - 动画粒子颜色
080. terrain/generation/createTerrain - 创建地形
081. terrain/generation/generateHeightmap - 生成高度图
082. terrain/generation/applyNoise - 应用噪声
083. terrain/texture/setTerrainTexture - 设置地形纹理
084. terrain/texture/blendTextures - 混合纹理
085. terrain/lod/enableTerrainLOD - 启用地形LOD
086. terrain/collision/enableTerrainCollision - 启用地形碰撞
087. water/system/createWaterSurface - 创建水面
088. water/waves/addWaves - 添加波浪
089. water/reflection/enableReflection - 启用水面反射
090. water/refraction/enableRefraction - 启用水面折射
091. vegetation/system/createVegetation - 创建植被
092. vegetation/grass/addGrass - 添加草地
093. vegetation/trees/addTrees - 添加树木
094. weather/system/createWeatherSystem - 创建天气系统
095. weather/rain/enableRain - 启用雨效
096. weather/snow/enableSnow - 启用雪效
097. weather/wind/setWindDirection - 设置风向
098. weather/wind/setWindStrength - 设置风力
099. environment/time/setTimeOfDay - 设置时间
100. editor/project/createProject - 创建项目
101. editor/project/openProject - 打开项目
102. editor/project/saveProject - 保存项目
103. editor/project/closeProject - 关闭项目
104. editor/project/exportProject - 导出项目
105. editor/project/importProject - 导入项目
106. editor/project/setProjectSettings - 设置项目配置
107. editor/project/getProjectInfo - 获取项目信息
108. editor/asset/importAsset - 导入资产
109. editor/asset/deleteAsset - 删除资产
110. editor/asset/renameAsset - 重命名资产
111. editor/asset/moveAsset - 移动资产
112. editor/asset/createFolder - 创建文件夹
113. editor/asset/getAssetInfo - 获取资产信息
114. editor/asset/generateThumbnail - 生成缩略图
115. editor/scene/createEntity - 创建实体
116. editor/scene/deleteEntity - 删除实体
117. editor/scene/selectEntity - 选择实体
118. editor/scene/duplicateEntity - 复制实体
119. editor/scene/groupEntities - 组合实体
120. editor/scene/ungroupEntities - 取消组合
121. editor/scene/setEntityParent - 设置父对象
122. editor/scene/moveEntity - 移动实体
123. editor/scene/rotateEntity - 旋转实体
124. editor/scene/scaleEntity - 缩放实体
125. editor/scene/hideEntity - 隐藏实体
126. editor/scene/showEntity - 显示实体
127. editor/scene/lockEntity - 锁定实体
128. editor/scene/unlockEntity - 解锁实体
129. editor/scene/focusOnEntity - 聚焦实体
130. editor/ui/createUIElement - 创建UI元素
131. editor/ui/deleteUIElement - 删除UI元素
132. editor/ui/setUIPosition - 设置UI位置
133. editor/ui/setUISize - 设置UI大小
134. editor/ui/setUIText - 设置UI文本
135. editor/ui/setUIColor - 设置UI颜色
136. editor/ui/setUIFont - 设置UI字体
137. editor/ui/setUIImage - 设置UI图像
138. editor/ui/addUIEvent - 添加UI事件
139. editor/ui/removeUIEvent - 移除UI事件
140. editor/ui/setUIVisible - 设置UI可见性
141. editor/ui/setUIEnabled - 设置UI启用状态
142. editor/ui/setUILayer - 设置UI层级
143. editor/ui/alignUIElements - 对齐UI元素
144. editor/ui/distributeUIElements - 分布UI元素
145. editor/tools/enableGizmo - 启用操作手柄
146. editor/tools/setGizmoMode - 设置手柄模式
147. editor/tools/enableGrid - 启用网格
148. editor/tools/setGridSize - 设置网格大小
149. editor/tools/enableSnap - 启用吸附
150. server/user/registerUser - 用户注册
151. server/user/loginUser - 用户登录
152. server/user/logoutUser - 用户登出
153. server/user/updateUserProfile - 更新用户资料
154. server/user/changePassword - 修改密码
155. server/user/resetPassword - 重置密码
156. server/user/getUserInfo - 获取用户信息
157. server/user/deleteUser - 删除用户
158. server/user/setUserRole - 设置用户角色
159. server/user/validateToken - 验证令牌
160. server/project/createProject - 创建服务器项目
161. server/project/deleteProject - 删除服务器项目
162. server/project/updateProject - 更新项目信息
163. server/project/getProjectList - 获取项目列表
164. server/project/getProjectDetails - 获取项目详情
165. server/project/shareProject - 分享项目
166. server/project/unshareProject - 取消分享
167. server/project/setProjectPermission - 设置项目权限
168. server/project/forkProject - 复制项目
169. server/project/archiveProject - 归档项目
170. server/project/restoreProject - 恢复项目
171. server/project/exportProjectData - 导出项目数据
172. server/project/importProjectData - 导入项目数据
173. server/project/getProjectStats - 获取项目统计
174. server/project/backupProject - 备份项目
175. server/asset/uploadAsset - 上传资产
176. server/asset/downloadAsset - 下载资产
177. server/asset/deleteAsset - 删除服务器资产
178. server/asset/getAssetList - 获取资产列表
179. server/asset/getAssetInfo - 获取资产信息
180. server/asset/updateAssetInfo - 更新资产信息
181. server/asset/moveAssetToFolder - 移动资产到文件夹
182. server/asset/createAssetFolder - 创建资产文件夹
183. server/asset/deleteAssetFolder - 删除资产文件夹
184. server/asset/shareAsset - 分享资产
185. server/asset/getAssetVersions - 获取资产版本
186. server/asset/createAssetVersion - 创建资产版本
187. server/asset/restoreAssetVersion - 恢复资产版本
188. server/asset/generateAssetThumbnail - 生成资产缩略图
189. server/asset/optimizeAsset - 优化资产
190. server/collaboration/joinRoom - 加入协作房间
191. server/collaboration/leaveRoom - 离开协作房间
192. server/collaboration/sendOperation - 发送协作操作
193. server/collaboration/receiveOperation - 接收协作操作
194. server/collaboration/resolveConflict - 解决编辑冲突
195. server/collaboration/getOnlineUsers - 获取在线用户
196. server/collaboration/broadcastMessage - 广播消息
197. server/collaboration/lockResource - 锁定资源
198. server/collaboration/unlockResource - 解锁资源
199. server/collaboration/syncState - 同步状态

## 📈 建议

1. **完善双重注册**：确保所有节点既在引擎注册又在编辑器集成
2. **统一节点管理**：建立统一的节点注册机制
3. **定期同步检查**：定期验证引擎和编辑器的节点一致性

---
*报告生成时间：2025/7/11 16:49:56*
