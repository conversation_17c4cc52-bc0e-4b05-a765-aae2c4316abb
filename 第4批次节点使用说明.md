# 第4批次节点使用说明

## 概述

第4批次节点包含3个新增的渲染相机节点，已成功集成到DL引擎的可视化脚本系统中。这些节点为用户提供了通过拖拽方式创建和控制3D相机的能力。

## 新增节点列表

### 118. 创建透视相机 (rendering/camera/createPerspectiveCamera)

**功能**: 创建透视投影相机，适用于3D场景渲染

**输入参数**:
- `execute` (流程): 执行输入
- `fov` (数值): 视野角度，默认75度
- `aspect` (数值): 宽高比，默认16:9
- `near` (数值): 近裁剪面，默认0.1
- `far` (数值): 远裁剪面，默认1000
- `entityName` (字符串): 实体名称，默认"透视相机"

**输出参数**:
- `onComplete` (流程): 完成输出
- `camera` (相机组件): 创建的相机组件
- `entity` (实体): 相机实体

**使用场景**: 3D游戏、虚拟现实、建筑可视化等需要透视效果的场景

### 119. 创建正交相机 (rendering/camera/createOrthographicCamera)

**功能**: 创建正交投影相机，适用于2D界面和技术图纸

**输入参数**:
- `execute` (流程): 执行输入
- `left` (数值): 左平面，默认-10
- `right` (数值): 右平面，默认10
- `top` (数值): 上平面，默认10
- `bottom` (数值): 下平面，默认-10
- `near` (数值): 近裁剪面，默认0.1
- `far` (数值): 远裁剪面，默认1000
- `entityName` (字符串): 实体名称，默认"正交相机"

**输出参数**:
- `onComplete` (流程): 完成输出
- `camera` (相机组件): 创建的相机组件
- `entity` (实体): 相机实体

**使用场景**: UI界面、2D游戏、CAD图纸、等距视图等

### 120. 设置相机位置 (rendering/camera/setCameraPosition)

**功能**: 设置相机在3D空间中的位置

**输入参数**:
- `execute` (流程): 执行输入
- `entity` (实体): 相机实体
- `position` (向量3): 相机位置，默认{x:0, y:0, z:5}

**输出参数**:
- `onComplete` (流程): 完成输出
- `entity` (实体): 相机实体

**使用场景**: 相机定位、视角切换、动画控制等

## 使用方法

### 1. 在编辑器中使用

1. 打开可视化脚本编辑器
2. 点击"添加节点"按钮
3. 在节点搜索中输入"相机"或"rendering"
4. 选择需要的相机节点
5. 拖拽到画布上
6. 连接节点并设置参数
7. 执行脚本

### 2. 基础使用示例

```
开始事件 → 创建透视相机 → 设置相机位置 → 完成
```

**步骤**:
1. 添加"开始事件"节点
2. 添加"创建透视相机"节点
3. 添加"设置相机位置"节点
4. 连接流程: 开始事件.onComplete → 创建透视相机.execute
5. 连接流程: 创建透视相机.onComplete → 设置相机位置.execute
6. 连接数据: 创建透视相机.entity → 设置相机位置.entity
7. 设置参数: 相机位置为{x:0, y:5, z:10}

### 3. 多相机系统示例

```
开始事件 → 创建主相机 → 设置主相机位置
         ↓
         创建UI相机 → 设置UI相机位置
```

**用途**: 游戏中同时需要3D场景相机和2D UI相机

## 测试验证

### 访问测试页面

在浏览器中访问: `http://localhost:3000/test/batch4-nodes`

### 测试内容

1. **节点注册测试**: 验证节点是否正确注册到系统
2. **引擎集成测试**: 验证节点是否正确集成到引擎
3. **脚本执行测试**: 验证节点是否能正确执行

### 预期结果

- 所有测试项显示"通过"状态
- 能够在节点搜索中找到新增的相机节点
- 能够拖拽节点到画布并正常连接
- 执行脚本时能够成功创建相机实体

## 技术实现

### 节点注册

- 节点定义在 `engine/src/visualscript/presets/RenderingNodes.ts`
- 通过 `NodeRegistryService` 注册到编辑器
- 支持类型检查和参数验证

### 引擎集成

- 通过 `EngineNodeIntegration` 服务集成到引擎
- 支持实际的相机创建和控制
- 与Three.js渲染系统完全兼容

### 编辑器支持

- 在节点搜索中可以找到和筛选
- 支持拖拽创建和参数设置
- 提供完整的可视化编辑体验

## 常见问题

### Q: 为什么创建的相机看不到效果？

A: 需要确保:
1. 相机已正确创建并添加到场景
2. 相机位置设置合理
3. 场景中有可渲染的对象
4. 相机被设置为活动相机

### Q: 透视相机和正交相机有什么区别？

A: 
- **透视相机**: 有透视效果，远处物体看起来更小，适合3D场景
- **正交相机**: 无透视效果，物体大小不随距离变化，适合2D界面

### Q: 如何切换不同的相机？

A: 可以通过引擎的 `setActiveCamera` 方法切换活动相机，或者创建多个相机节点分别控制不同的渲染目标。

## 下一步计划

第5批次将添加更多渲染系统节点，包括:
- 光照系统节点 (121-128)
- 阴影系统节点 (129-130)  
- 材质系统节点 (131-136)
- 后处理节点 (137-139)
- LOD系统节点 (140)

## 反馈和支持

如果在使用过程中遇到问题，请:
1. 查看浏览器控制台的错误信息
2. 使用测试页面验证节点状态
3. 提交问题报告并附上详细的重现步骤

---

**文档版本**: v1.0  
**更新时间**: 2025年7月10日  
**适用版本**: DL引擎 v2.0+
