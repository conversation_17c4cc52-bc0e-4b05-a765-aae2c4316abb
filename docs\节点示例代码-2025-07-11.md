# 视觉脚本节点示例代码 - 2025年7月11日

## 🎯 示例概述

本文档提供了视觉脚本系统中各类节点的实际使用示例，包括常见场景、最佳实践和完整的代码演示。

## 📐 数学节点示例

### 示例1: 波形动画

使用正弦和余弦节点创建平滑的波形动画效果。

```typescript
// 场景：创建一个在圆形路径上移动的物体
class CircularMotionExample {
  private time: number = 0;
  private radius: number = 5;
  private speed: number = 2;
  
  async updatePosition(entity: Entity, deltaTime: number) {
    this.time += deltaTime * this.speed;
    
    // 使用正弦和余弦计算圆形路径
    const sinNode = new SinNode({ id: 'sin', position: { x: 0, y: 0 } });
    const cosNode = new CosNode({ id: 'cos', position: { x: 0, y: 0 } });
    
    // 计算X坐标（余弦）
    cosNode.setInputValue('angle', this.time);
    await cosNode.execute(context);
    const x = cosNode.getOutputValue('result') * this.radius;
    
    // 计算Y坐标（正弦）
    sinNode.setInputValue('angle', this.time);
    await sinNode.execute(context);
    const y = sinNode.getOutputValue('result') * this.radius;
    
    // 更新实体位置
    entity.transform.position = { x, y, z: 0 };
  }
}
```

### 示例2: 向量运算

使用向量节点进行3D空间计算。

```typescript
// 场景：计算两点间的距离和方向
class VectorCalculationExample {
  async calculateDistanceAndDirection(pointA: Vector3, pointB: Vector3) {
    // 计算方向向量
    const direction = {
      x: pointB.x - pointA.x,
      y: pointB.y - pointA.y,
      z: pointB.z - pointA.z
    };
    
    // 计算距离
    const magnitudeNode = new VectorMagnitudeNode({ 
      id: 'magnitude', 
      position: { x: 0, y: 0 } 
    });
    magnitudeNode.setInputValue('vector', direction);
    await magnitudeNode.execute(context);
    const distance = magnitudeNode.getOutputValue('magnitude');
    
    // 计算单位方向向量
    const normalizeNode = new VectorNormalizeNode({ 
      id: 'normalize', 
      position: { x: 0, y: 0 } 
    });
    normalizeNode.setInputValue('vector', direction);
    await normalizeNode.execute(context);
    const unitDirection = normalizeNode.getOutputValue('normalized');
    
    return { distance, unitDirection };
  }
}
```

## 🔗 逻辑节点示例

### 示例3: 条件判断系统

使用逻辑节点构建复杂的条件判断。

```typescript
// 场景：游戏中的权限验证系统
class PermissionSystem {
  async checkAccess(user: User, resource: Resource): Promise<boolean> {
    // 检查用户是否已登录
    const isLoggedIn = user.isAuthenticated;
    
    // 检查用户是否有权限
    const hasPermission = user.permissions.includes(resource.requiredPermission);
    
    // 使用逻辑与节点
    const andNode = new LogicalAndNode({ 
      id: 'access-check', 
      position: { x: 0, y: 0 } 
    });
    andNode.setInputValue('a', isLoggedIn);
    andNode.setInputValue('b', hasPermission);
    await andNode.execute(context);
    
    const hasAccess = andNode.getOutputValue('result');
    const truthTable = andNode.getOutputValue('truthTable');
    
    console.log(`访问检查: ${truthTable}`);
    return hasAccess;
  }
}
```

## ⚡ 物理节点示例

### 示例4: 重力模拟

创建不同重力环境的物理模拟。

```typescript
// 场景：太空游戏中的多重力环境
class GravityEnvironment {
  // 预定义的重力设置
  static readonly GRAVITY_PRESETS = {
    EARTH: { x: 0, y: -9.81, z: 0 },
    MOON: { x: 0, y: -1.635, z: 0 },
    MARS: { x: 0, y: -3.71, z: 0 },
    ZERO_G: { x: 0, y: 0, z: 0 }
  };
  
  async setEnvironment(environment: keyof typeof GravityEnvironment.GRAVITY_PRESETS) {
    const gravityNode = new SetGravityNode({ 
      id: 'gravity-setter', 
      position: { x: 0, y: 0 } 
    });
    
    const gravity = GravityEnvironment.GRAVITY_PRESETS[environment];
    gravityNode.setInputValue('gravity', gravity);
    
    // 执行重力设置
    await gravityNode.execute(context);
    
    console.log(`环境已切换到: ${environment}, 重力: ${JSON.stringify(gravity)}`);
  }
}
```

### 示例5: 碰撞检测系统

实现游戏中的碰撞检测和响应。

```typescript
// 场景：射击游戏中的子弹碰撞检测
class BulletCollisionSystem {
  private bullets: Entity[] = [];
  private enemies: Entity[] = [];
  
  async updateCollisions() {
    const collisionNode = new CollisionDetectNode({ 
      id: 'collision-detector', 
      position: { x: 0, y: 0 } 
    });
    
    for (const bullet of this.bullets) {
      for (const enemy of this.enemies) {
        // 检测碰撞
        collisionNode.setInputValue('entityA', bullet);
        collisionNode.setInputValue('entityB', enemy);
        await collisionNode.execute(context);
        
        const isColliding = collisionNode.getOutputValue('isColliding');
        
        if (isColliding) {
          await this.handleBulletHit(bullet, enemy);
        }
      }
    }
  }
  
  private async handleBulletHit(bullet: Entity, enemy: Entity) {
    // 移除子弹
    this.removeBullet(bullet);
    
    // 对敌人造成伤害
    enemy.health -= bullet.damage;
    
    // 如果敌人死亡，移除敌人
    if (enemy.health <= 0) {
      this.removeEnemy(enemy);
    }
    
    console.log(`子弹击中敌人！剩余血量: ${enemy.health}`);
  }
}
```

## ⏰ 时间节点示例

### 示例6: 技能冷却系统

使用延迟节点实现游戏技能的冷却机制。

```typescript
// 场景：RPG游戏中的技能冷却系统
class SkillCooldownSystem {
  private cooldowns: Map<string, boolean> = new Map();
  
  async useSkill(skillId: string, cooldownTime: number): Promise<boolean> {
    // 检查技能是否在冷却中
    if (this.cooldowns.get(skillId)) {
      console.log(`技能 ${skillId} 正在冷却中`);
      return false;
    }
    
    // 执行技能
    console.log(`使用技能: ${skillId}`);
    this.cooldowns.set(skillId, true);
    
    // 设置冷却延迟
    const delayNode = new DelayNode({ 
      id: `cooldown-${skillId}`, 
      position: { x: 0, y: 0 } 
    });
    delayNode.setInputValue('duration', cooldownTime);
    
    // 冷却结束后重置状态
    delayNode.onExecuteOutput = () => {
      this.cooldowns.set(skillId, false);
      console.log(`技能 ${skillId} 冷却完成`);
    };
    
    await delayNode.execute(context);
    return true;
  }
}
```

### 示例7: 游戏循环计时器

使用计时器节点创建游戏的主循环。

```typescript
// 场景：游戏主循环和状态更新
class GameLoop {
  private isRunning: boolean = false;
  private gameObjects: GameObject[] = [];
  
  async startGameLoop() {
    const timerNode = new TimerNode({ 
      id: 'game-timer', 
      position: { x: 0, y: 0 } 
    });
    
    // 设置60FPS（约16.67ms间隔）
    timerNode.setInputValue('interval', 1/60);
    this.isRunning = true;
    
    // 设置计时器回调
    timerNode.onTick = async () => {
      if (this.isRunning) {
        await this.updateGame();
      }
    };
    
    // 启动计时器
    const startContext = { ...context, inputSocket: 'start' };
    await timerNode.execute(startContext);
    
    console.log('游戏循环已启动');
  }
  
  async stopGameLoop() {
    this.isRunning = false;
    console.log('游戏循环已停止');
  }
  
  private async updateGame() {
    // 更新所有游戏对象
    for (const obj of this.gameObjects) {
      obj.update();
    }
    
    // 渲染场景
    this.render();
  }
}
```

## 🎬 动画节点示例

### 示例8: 角色动画状态机

使用动画节点创建角色的动画状态管理。

```typescript
// 场景：角色动画状态机
class CharacterAnimationController {
  private currentState: string = 'idle';
  private character: Entity;
  
  constructor(character: Entity) {
    this.character = character;
  }
  
  async setState(newState: string) {
    if (this.currentState === newState) return;
    
    console.log(`动画状态切换: ${this.currentState} -> ${newState}`);
    
    // 停止当前动画
    const stopNode = new StopAnimationNode({ 
      id: 'stop-anim', 
      position: { x: 0, y: 0 } 
    });
    stopNode.setInputValue('entity', this.character);
    await stopNode.execute(context);
    
    // 播放新动画
    const playNode = new PlayAnimationNode({ 
      id: 'play-anim', 
      position: { x: 0, y: 0 } 
    });
    playNode.setInputValue('entity', this.character);
    playNode.setInputValue('animationName', newState);
    await playNode.execute(context);
    
    this.currentState = newState;
  }
  
  // 便捷方法
  async idle() { await this.setState('idle'); }
  async walk() { await this.setState('walk'); }
  async run() { await this.setState('run'); }
  async jump() { await this.setState('jump'); }
  async attack() { await this.setState('attack'); }
}
```

## 🎮 输入节点示例

### 示例9: 玩家控制系统

使用输入节点实现玩家角色控制。

```typescript
// 场景：第一人称射击游戏的玩家控制
class PlayerController {
  private player: Entity;
  private moveSpeed: number = 5;
  private isMoving: boolean = false;
  
  constructor(player: Entity) {
    this.player = player;
    this.setupInputHandlers();
  }
  
  private async setupInputHandlers() {
    // WASD移动控制
    const keys = ['KeyW', 'KeyA', 'KeyS', 'KeyD'];
    
    for (const key of keys) {
      const keyboardNode = new KeyboardInputNode({ 
        id: `input-${key}`, 
        position: { x: 0, y: 0 } 
      });
      keyboardNode.setInputValue('keyCode', key);
      
      // 设置按键事件处理
      keyboardNode.onKeyDown = () => this.handleKeyDown(key);
      keyboardNode.onKeyUp = () => this.handleKeyUp(key);
      
      await keyboardNode.execute(context);
    }
    
    // 跳跃控制
    const spaceNode = new KeyboardInputNode({ 
      id: 'input-space', 
      position: { x: 0, y: 0 } 
    });
    spaceNode.setInputValue('keyCode', 'Space');
    spaceNode.onKeyDown = () => this.jump();
    await spaceNode.execute(context);
  }
  
  private handleKeyDown(key: string) {
    const direction = this.getDirectionFromKey(key);
    this.movePlayer(direction);
  }
  
  private handleKeyUp(key: string) {
    // 停止移动逻辑
    this.stopMovement();
  }
  
  private getDirectionFromKey(key: string): Vector3 {
    switch (key) {
      case 'KeyW': return { x: 0, y: 0, z: 1 };  // 前进
      case 'KeyS': return { x: 0, y: 0, z: -1 }; // 后退
      case 'KeyA': return { x: -1, y: 0, z: 0 }; // 左移
      case 'KeyD': return { x: 1, y: 0, z: 0 };  // 右移
      default: return { x: 0, y: 0, z: 0 };
    }
  }
  
  private movePlayer(direction: Vector3) {
    const velocity = {
      x: direction.x * this.moveSpeed,
      y: direction.y * this.moveSpeed,
      z: direction.z * this.moveSpeed
    };
    
    // 应用移动
    this.player.transform.position.x += velocity.x;
    this.player.transform.position.z += velocity.z;
  }
  
  private async jump() {
    // 应用向上的冲量
    const impulseNode = new ApplyImpulseNode({ 
      id: 'jump-impulse', 
      position: { x: 0, y: 0 } 
    });
    impulseNode.setInputValue('entity', this.player);
    impulseNode.setInputValue('impulse', { x: 0, y: 10, z: 0 });
    await impulseNode.execute(context);
    
    console.log('玩家跳跃');
  }
}
```

## 🔊 音频节点示例

### 示例10: 动态音效系统

使用音频节点创建响应式音效系统。

```typescript
// 场景：根据游戏状态播放不同音效
class AudioManager {
  private backgroundMusic: AudioClip;
  private soundEffects: Map<string, AudioClip> = new Map();
  
  async playBackgroundMusic(musicClip: AudioClip) {
    const audioNode = new PlayAudioNode({ 
      id: 'bg-music', 
      position: { x: 0, y: 0 } 
    });
    
    audioNode.setInputValue('audioClip', musicClip);
    audioNode.setInputValue('volume', 0.3); // 30% 音量
    audioNode.setInputValue('loop', true);  // 循环播放
    
    await audioNode.execute(context);
    console.log('背景音乐开始播放');
  }
  
  async playSoundEffect(effectName: string, volume: number = 1.0) {
    const soundClip = this.soundEffects.get(effectName);
    if (!soundClip) {
      console.warn(`音效不存在: ${effectName}`);
      return;
    }
    
    const audioNode = new PlayAudioNode({ 
      id: `sfx-${effectName}`, 
      position: { x: 0, y: 0 } 
    });
    
    audioNode.setInputValue('audioClip', soundClip);
    audioNode.setInputValue('volume', volume);
    audioNode.setInputValue('loop', false);
    
    await audioNode.execute(context);
    console.log(`播放音效: ${effectName}`);
  }
  
  // 预加载音效
  loadSoundEffect(name: string, url: string) {
    const audioClip: AudioClip = {
      url,
      duration: 0, // 将在加载时确定
      format: url.split('.').pop() || 'mp3'
    };
    
    this.soundEffects.set(name, audioClip);
  }
}
```

## 🔧 综合示例

### 示例11: 完整的游戏场景

结合多种节点类型创建一个完整的游戏场景。

```typescript
// 场景：简单的射击游戏关卡
class ShootingGameLevel {
  private player: Entity;
  private enemies: Entity[] = [];
  private bullets: Entity[] = [];
  private gameLoop: GameLoop;
  private audioManager: AudioManager;
  private playerController: PlayerController;
  
  async initializeLevel() {
    // 设置物理环境
    const gravityEnv = new GravityEnvironment();
    await gravityEnv.setEnvironment('EARTH');
    
    // 初始化玩家
    this.player = this.createPlayer();
    this.playerController = new PlayerController(this.player);
    
    // 初始化音频
    this.audioManager = new AudioManager();
    await this.audioManager.playBackgroundMusic(this.loadAudioClip('battle-music.mp3'));
    
    // 启动游戏循环
    this.gameLoop = new GameLoop();
    await this.gameLoop.startGameLoop();
    
    // 生成敌人
    await this.spawnEnemies();
    
    console.log('游戏关卡初始化完成');
  }
  
  private createPlayer(): Entity {
    // 创建玩家实体
    const player = new Entity('player');
    
    // 添加刚体组件
    const rigidBodyNode = new CreateRigidBodyNode({ 
      id: 'player-rigidbody', 
      position: { x: 0, y: 0 } 
    });
    rigidBodyNode.setInputValue('entity', player);
    rigidBodyNode.setInputValue('mass', 70); // 70kg
    rigidBodyNode.execute(context);
    
    return player;
  }
  
  private async spawnEnemies() {
    // 每5秒生成一个敌人
    const spawnTimer = new TimerNode({ 
      id: 'enemy-spawner', 
      position: { x: 0, y: 0 } 
    });
    spawnTimer.setInputValue('interval', 5.0);
    
    spawnTimer.onTick = () => {
      this.createEnemy();
    };
    
    const startContext = { ...context, inputSocket: 'start' };
    await spawnTimer.execute(startContext);
  }
  
  private createEnemy(): Entity {
    const enemy = new Entity(`enemy-${Date.now()}`);
    
    // 随机位置
    enemy.transform.position = {
      x: Math.random() * 20 - 10,
      y: 0,
      z: Math.random() * 20 - 10
    };
    
    this.enemies.push(enemy);
    console.log('生成新敌人');
    
    return enemy;
  }
}
```

## 📝 最佳实践总结

### 1. 性能优化
- 缓存频繁使用的节点实例
- 避免在循环中创建新节点
- 及时清理不需要的资源

### 2. 错误处理
- 始终验证输入参数
- 提供有意义的错误信息
- 实现优雅的降级处理

### 3. 代码组织
- 将相关节点组织成系统类
- 使用工厂模式创建节点
- 保持代码的可读性和可维护性

### 4. 调试技巧
- 使用有意义的节点ID
- 添加详细的日志输出
- 利用性能监控工具

---
*示例版本: 1.0*  
*最后更新: 2025年7月11日*  
*适用范围: 所有228个已注册节点*
