/**
 * 检查节点重复注册问题
 * 分析为什么会有366个编辑器节点
 */

const fs = require('fs');

console.log('🔍 检查节点重复注册问题...\n');

// 分析编辑器节点
function analyzeEditorNodeDuplicates() {
  console.log('📊 分析编辑器节点重复情况...');
  
  const serviceFile = 'editor/src/services/NodeRegistryService.ts';
  const content = fs.readFileSync(serviceFile, 'utf8');
  
  // 提取所有registerNode调用
  const registerPattern = /this\.registerNode\(\{[\s\S]*?\}\);/g;
  const matches = content.match(registerPattern) || [];
  
  console.log(`总共找到 ${matches.length} 个registerNode调用`);
  
  const nodeTypes = [];
  const duplicates = {};
  
  matches.forEach((match, index) => {
    const typeMatch = match.match(/type:\s*['"`]([^'"`]+)['"`]/);
    const labelMatch = match.match(/label:\s*['"`]([^'"`]+)['"`]/);
    
    if (typeMatch) {
      const nodeType = typeMatch[1];
      const label = labelMatch ? labelMatch[1] : '未知';
      
      if (nodeTypes.includes(nodeType)) {
        if (!duplicates[nodeType]) {
          duplicates[nodeType] = [];
        }
        duplicates[nodeType].push({
          index: index + 1,
          label: label
        });
      } else {
        nodeTypes.push(nodeType);
      }
    }
  });
  
  console.log(`\n去重后的节点类型数量: ${nodeTypes.length}`);
  console.log(`重复的节点类型数量: ${Object.keys(duplicates).length}`);
  
  if (Object.keys(duplicates).length > 0) {
    console.log('\n🔄 发现重复注册的节点:');
    Object.entries(duplicates).forEach(([nodeType, instances]) => {
      console.log(`\n节点类型: ${nodeType}`);
      instances.forEach(instance => {
        console.log(`  - 第${instance.index}个注册: ${instance.label}`);
      });
    });
  }
  
  // 按批次分析
  console.log('\n📋 按批次分析节点分布:');
  
  const batchPatterns = [
    { name: '默认节点', pattern: /initializeDefaultNodes.*?(?=initializeBatch1Nodes|$)/s },
    { name: '第1批次', pattern: /initializeBatch1Nodes.*?(?=initializeBatch2Nodes|$)/s },
    { name: '第2批次', pattern: /initializeBatch2Nodes.*?(?=initializeBatch3Nodes|$)/s },
    { name: '第3批次', pattern: /initializeBatch3Nodes.*?(?=initializeBatch4Nodes|$)/s },
    { name: '第4批次', pattern: /initializeBatch4Nodes.*?(?=initializeBatch5Nodes|$)/s },
    { name: '第5批次', pattern: /initializeBatch5Nodes.*?(?=initializeBatch5ExtendedNodes|$)/s },
    { name: '第5批次扩展', pattern: /initializeBatch5ExtendedNodes.*?(?=initializeBatch6Nodes|$)/s },
    { name: '第6批次', pattern: /initializeBatch6Nodes.*?(?=initializeBatch7Nodes|$)/s },
    { name: '第7批次', pattern: /initializeBatch7Nodes.*?(?=initializeBatch8Nodes|$)/s },
    { name: '第8批次', pattern: /initializeBatch8Nodes.*?(?=initializeBatch9Nodes|$)/s },
    { name: '第9批次', pattern: /initializeBatch9Nodes.*?(?=initializeBatch10Nodes|$)/s },
    { name: '第10批次', pattern: /initializeBatch10Nodes.*?(?=initializeBatch11Nodes|$)/s },
    { name: '第11批次', pattern: /initializeBatch11Nodes.*?(?=initializeBatch12Nodes|$)/s },
    { name: '第12批次', pattern: /initializeBatch12Nodes.*?(?=\}|$)/s }
  ];
  
  batchPatterns.forEach(batch => {
    const batchMatch = content.match(batch.pattern);
    if (batchMatch) {
      const batchContent = batchMatch[0];
      const batchRegisterCalls = batchContent.match(/this\.registerNode\(/g) || [];
      console.log(`${batch.name}: ${batchRegisterCalls.length} 个节点`);
    } else {
      console.log(`${batch.name}: 未找到`);
    }
  });
  
  return {
    totalCalls: matches.length,
    uniqueTypes: nodeTypes.length,
    duplicateTypes: Object.keys(duplicates).length,
    duplicates: duplicates
  };
}

// 分析引擎节点
function analyzeEngineNodeDuplicates() {
  console.log('\n📊 分析引擎节点重复情况...');
  
  const presetDir = 'engine/src/visualscript/presets';
  const presetFiles = fs.readdirSync(presetDir).filter(file => file.endsWith('.ts'));
  
  const allEngineNodes = [];
  const engineDuplicates = {};
  
  presetFiles.forEach(fileName => {
    const filePath = `${presetDir}/${fileName}`;
    const content = fs.readFileSync(filePath, 'utf8');
    
    const registerPattern = /registry\.registerNodeType\(\{[\s\S]*?\}\);/g;
    const matches = content.match(registerPattern) || [];
    
    matches.forEach(match => {
      const typeMatch = match.match(/type:\s*['"`]([^'"`]+)['"`]/);
      if (typeMatch) {
        const nodeType = typeMatch[1];
        
        if (allEngineNodes.includes(nodeType)) {
          if (!engineDuplicates[nodeType]) {
            engineDuplicates[nodeType] = [];
          }
          engineDuplicates[nodeType].push(fileName);
        } else {
          allEngineNodes.push(nodeType);
        }
      }
    });
  });
  
  console.log(`引擎节点总数: ${allEngineNodes.length}`);
  console.log(`重复的引擎节点: ${Object.keys(engineDuplicates).length}`);
  
  if (Object.keys(engineDuplicates).length > 0) {
    console.log('\n🔄 发现重复注册的引擎节点:');
    Object.entries(engineDuplicates).forEach(([nodeType, files]) => {
      console.log(`${nodeType}: ${files.join(', ')}`);
    });
  }
  
  return {
    totalEngineNodes: allEngineNodes.length,
    duplicateEngineTypes: Object.keys(engineDuplicates).length
  };
}

// 执行分析
try {
  const editorResult = analyzeEditorNodeDuplicates();
  const engineResult = analyzeEngineNodeDuplicates();
  
  console.log('\n📊 总结:');
  console.log(`编辑器注册调用: ${editorResult.totalCalls}`);
  console.log(`编辑器唯一节点: ${editorResult.uniqueTypes}`);
  console.log(`编辑器重复节点: ${editorResult.duplicateTypes}`);
  console.log(`引擎唯一节点: ${engineResult.totalEngineNodes}`);
  console.log(`引擎重复节点: ${engineResult.duplicateEngineTypes}`);
  
  // 计算真实的节点总数
  console.log('\n🎯 真实情况:');
  console.log(`编辑器实际可用节点: ${editorResult.uniqueTypes} 个`);
  console.log(`引擎实际可用节点: ${engineResult.totalEngineNodes} 个`);
  
} catch (error) {
  console.error('❌ 分析过程中发生错误：', error);
}
