/**
 * 节点注册状态分析脚本
 * 分析视觉脚本系统中所有节点的引擎注册和编辑器集成情况
 */

const fs = require('fs');
const path = require('path');

// 节点分析结果
const analysisResult = {
  engineNodes: [],      // 引擎中注册的节点
  editorNodes: [],      // 编辑器中注册的节点
  bothRegistered: [],   // 既在引擎又在编辑器中注册的节点
  engineOnly: [],       // 仅在引擎中注册的节点
  editorOnly: []        // 仅在编辑器中注册的节点
};

/**
 * 分析引擎中的节点注册
 */
function analyzeEngineNodes() {
  console.log('🔍 分析引擎中的节点注册...');
  
  const enginePresetFiles = [
    'engine/src/visualscript/presets/CoreNodes.ts',
    'engine/src/visualscript/presets/MathNodes.ts',
    'engine/src/visualscript/presets/LogicNodes.ts',
    'engine/src/visualscript/presets/EntityNodes.ts',
    'engine/src/visualscript/presets/PhysicsNodes.ts',
    'engine/src/visualscript/presets/SoftBodyNodes.ts',
    'engine/src/visualscript/presets/AnimationNodes.ts',
    'engine/src/visualscript/presets/InputNodes.ts',
    'engine/src/visualscript/presets/AudioNodes.ts',
    'engine/src/visualscript/presets/NetworkNodes.ts',
    'engine/src/visualscript/presets/AINodes.ts',
    'engine/src/visualscript/presets/DebugNodes.ts',
    'engine/src/visualscript/presets/TimeNodes.ts',
    'engine/src/visualscript/presets/RenderingNodes.ts',
    'engine/src/visualscript/presets/NetworkSecurityNodes.ts',
    'engine/src/visualscript/presets/WebRTCNodes.ts',
    'engine/src/visualscript/presets/AIEmotionNodes.ts',
    'engine/src/visualscript/presets/AINLPNodes.ts',
    'engine/src/visualscript/presets/AIModelNodes.ts',
    'engine/src/visualscript/presets/NetworkProtocolNodes.ts',
    'engine/src/visualscript/presets/StringNodes.ts',
    'engine/src/visualscript/presets/ArrayNodes.ts',
    'engine/src/visualscript/presets/ObjectNodes.ts',
    'engine/src/visualscript/presets/VariableNodes.ts'
  ];

  let nodeIndex = 1;
  const seenTypes = new Set();

  enginePresetFiles.forEach(fileName => {
    if (fs.existsSync(fileName)) {
      const content = fs.readFileSync(fileName, 'utf8');
      
      // 匹配 registerNodeType 调用
      const registerPattern = /registry\.registerNodeType\(\s*\{[\s\S]*?type:\s*['"`]([^'"`]+)['"`][\s\S]*?label:\s*['"`]([^'"`]+)['"`][\s\S]*?\}\s*\)/g;
      
      let match;
      while ((match = registerPattern.exec(content)) !== null) {
        const nodeType = match[1];
        const nodeLabel = match[2];
        
        // 检查是否重复
        if (seenTypes.has(nodeType)) {
          console.log(`    ⚠️ 发现重复节点: ${nodeType} - ${nodeLabel} (在 ${fileName})`);
          continue;
        }
        
        seenTypes.add(nodeType);
        
        // 提取更多信息
        const nodeMatch = content.match(new RegExp(
          `registry\\.registerNodeType\\(\\s*\\{[\\s\\S]*?type:\\s*['"\`]${nodeType.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`][\\s\\S]*?\\}\\s*\\)`,
          'g'
        ));
        
        if (nodeMatch && nodeMatch[0]) {
          const nodeInfo = nodeMatch[0];
          
          // 提取描述
          const descMatch = nodeInfo.match(/description:\s*['"`]([^'"`]+)['"`]/);
          const categoryMatch = nodeInfo.match(/category:\s*NodeCategory\.([A-Z_]+)/);
          const iconMatch = nodeInfo.match(/icon:\s*['"`]([^'"`]+)['"`]/);
          const colorMatch = nodeInfo.match(/color:\s*['"`]([^'"`]+)['"`]/);
          
          const nodeData = {
            序号: nodeIndex.toString().padStart(3, '0'),
            节点类型: nodeType,
            节点中文名: nodeLabel,
            描述: descMatch ? descMatch[1] : '',
            分类: categoryMatch ? categoryMatch[1] : '',
            图标: iconMatch ? iconMatch[1] : '',
            颜色: colorMatch ? colorMatch[1] : '',
            源文件: fileName,
            引擎注册: '✓',
            编辑器注册: '待检查'
          };
          
          analysisResult.engineNodes.push(nodeData);
          nodeIndex++;
        }
      }
      
      console.log(`    ${fileName}: 找到 ${seenTypes.size} 个节点`);
    } else {
      console.log(`    文件不存在: ${fileName}`);
    }
  });
  
  console.log(`✅ 引擎节点分析完成，共找到 ${analysisResult.engineNodes.length} 个节点`);
}

/**
 * 分析编辑器中的节点注册
 */
function analyzeEditorNodes() {
  console.log('🔍 分析编辑器中的节点注册...');
  
  const editorFile = 'editor/src/services/NodeRegistryService.ts';
  
  if (fs.existsSync(editorFile)) {
    const content = fs.readFileSync(editorFile, 'utf8');
    
    // 匹配 registerNode 调用
    const registerPattern = /this\.registerNode\(\s*\{[\s\S]*?type:\s*['"`]([^'"`]+)['"`][\s\S]*?label:\s*['"`]([^'"`]+)['"`][\s\S]*?\}\s*\)/g;
    
    let nodeIndex = 1;
    const seenTypes = new Set();
    
    let match;
    while ((match = registerPattern.exec(content)) !== null) {
      const nodeType = match[1];
      const nodeLabel = match[2];
      
      // 检查是否重复
      if (seenTypes.has(nodeType)) {
        console.log(`    ⚠️ 发现重复节点: ${nodeType} - ${nodeLabel}`);
        continue;
      }
      
      seenTypes.add(nodeType);
      
      // 提取更多信息
      const nodeMatch = content.match(new RegExp(
        `this\\.registerNode\\(\\s*\\{[\\s\\S]*?type:\\s*['"\`]${nodeType.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`][\\s\\S]*?\\}\\s*\\)`,
        'g'
      ));
      
      if (nodeMatch && nodeMatch[0]) {
        const nodeInfo = nodeMatch[0];
        
        // 提取描述
        const descMatch = nodeInfo.match(/description:\s*['"`]([^'"`]+)['"`]/);
        const categoryMatch = nodeInfo.match(/category:\s*NodeCategory\.([A-Z_]+)/);
        const iconMatch = nodeInfo.match(/icon:\s*['"`]([^'"`]+)['"`]/);
        const colorMatch = nodeInfo.match(/color:\s*['"`]([^'"`]+)['"`]/);
        
        const nodeData = {
          序号: nodeIndex.toString().padStart(3, '0'),
          节点类型: nodeType,
          节点中文名: nodeLabel,
          描述: descMatch ? descMatch[1] : '',
          分类: categoryMatch ? categoryMatch[1] : '',
          图标: iconMatch ? iconMatch[1] : '',
          颜色: colorMatch ? colorMatch[1] : '',
          源文件: editorFile,
          引擎注册: '待检查',
          编辑器注册: '✓'
        };
        
        analysisResult.editorNodes.push(nodeData);
        nodeIndex++;
      }
    }
    
    console.log(`✅ 编辑器节点分析完成，共找到 ${analysisResult.editorNodes.length} 个节点`);
  } else {
    console.log(`❌ 编辑器文件不存在: ${editorFile}`);
  }
}

/**
 * 交叉分析节点注册状态
 */
function crossAnalyzeNodes() {
  console.log('🔍 交叉分析节点注册状态...');
  
  // 创建映射表
  const engineNodeMap = new Map();
  const editorNodeMap = new Map();
  
  analysisResult.engineNodes.forEach(node => {
    engineNodeMap.set(node.节点类型, node);
  });
  
  analysisResult.editorNodes.forEach(node => {
    editorNodeMap.set(node.节点类型, node);
  });
  
  // 分析既在引擎又在编辑器中注册的节点
  engineNodeMap.forEach((engineNode, nodeType) => {
    if (editorNodeMap.has(nodeType)) {
      const editorNode = editorNodeMap.get(nodeType);
      analysisResult.bothRegistered.push({
        ...engineNode,
        编辑器注册: '✓',
        编辑器中文名: editorNode.节点中文名
      });
    } else {
      analysisResult.engineOnly.push(engineNode);
    }
  });
  
  // 分析仅在编辑器中注册的节点
  editorNodeMap.forEach((editorNode, nodeType) => {
    if (!engineNodeMap.has(nodeType)) {
      analysisResult.editorOnly.push(editorNode);
    }
  });
  
  console.log(`✅ 交叉分析完成:`);
  console.log(`   - 既在引擎又在编辑器中注册: ${analysisResult.bothRegistered.length} 个`);
  console.log(`   - 仅在引擎中注册: ${analysisResult.engineOnly.length} 个`);
  console.log(`   - 仅在编辑器中注册: ${analysisResult.editorOnly.length} 个`);
}

/**
 * 生成分析报告
 */
function generateReport() {
  console.log('📝 生成分析报告...');
  
  const reportContent = `# 节点注册分类统计情况2025-7-11

## 📊 统计概览

- **引擎中注册节点总数**: ${analysisResult.engineNodes.length} 个
- **编辑器中注册节点总数**: ${analysisResult.editorNodes.length} 个
- **既在引擎又在编辑器中注册**: ${analysisResult.bothRegistered.length} 个
- **仅在引擎中注册**: ${analysisResult.engineOnly.length} 个
- **仅在编辑器中注册**: ${analysisResult.editorOnly.length} 个

## 第一段：既在引擎中注册又在编辑器中集成

| 序号 | 节点名 | 节点中文名 | 引擎中注册 | 编辑器中注册 |
|------|--------|------------|------------|--------------|
${analysisResult.bothRegistered.map(node => 
  `| ${node.序号} | ${node.节点类型} | ${node.节点中文名} | ✓ | ✓ |`
).join('\n')}

## 第二段：仅在引擎中注册的，未在编辑器中集成的

| 序号 | 节点名 | 节点中文名 | 引擎中注册 | 编辑器中注册 |
|------|--------|------------|------------|--------------|
${analysisResult.engineOnly.map((node, index) => 
  `| ${(index + 1).toString().padStart(3, '0')} | ${node.节点类型} | ${node.节点中文名} | ✓ | ✗ |`
).join('\n')}

## 第三段：仅在编辑器中集成，未在引擎中注册

| 序号 | 节点名 | 节点中文名 | 引擎中注册 | 编辑器中注册 |
|------|--------|------------|------------|--------------|
${analysisResult.editorOnly.map((node, index) => 
  `| ${(index + 1).toString().padStart(3, '0')} | ${node.节点类型} | ${node.节点中文名} | ✗ | ✓ |`
).join('\n')}

## 📋 详细节点信息

### 引擎注册节点详情
${analysisResult.engineNodes.map(node => 
  `- **${node.序号}**: ${node.节点类型} (${node.节点中文名}) - ${node.描述}`
).join('\n')}

### 编辑器注册节点详情
${analysisResult.editorNodes.map(node => 
  `- **${node.序号}**: ${node.节点类型} (${node.节点中文名}) - ${node.描述}`
).join('\n')}

---
*报告生成时间: ${new Date().toLocaleString('zh-CN')}*
`;

  fs.writeFileSync('节点注册分类统计情况2025-7-11.md', reportContent, 'utf8');
  console.log('✅ 报告已保存到: 节点注册分类统计情况2025-7-11.md');
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始分析视觉脚本系统节点注册状态...\n');
  
  // 分析引擎节点
  analyzeEngineNodes();
  console.log('');
  
  // 分析编辑器节点
  analyzeEditorNodes();
  console.log('');
  
  // 交叉分析
  crossAnalyzeNodes();
  console.log('');
  
  // 生成报告
  generateReport();
  
  console.log('\n🎉 分析完成！');
}

// 运行分析
main();
