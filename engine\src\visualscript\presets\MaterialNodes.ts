/**
 * 材质系统节点
 * 提供材质创建和控制功能
 */
import * as THREE from 'three';
import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory, SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 创建基础材质节点
 */
export class CreateBasicMaterialNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 颜色输入
    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      dataType: 'Color',
      direction: SocketDirection.INPUT,
      description: '颜色',
      defaultValue: { r: 1, g: 1, b: 1 }
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 材质输出
    this.addOutput({
      name: 'material',
      type: SocketType.DATA,
      dataType: 'Material',
      direction: SocketDirection.OUTPUT,
      description: '材质'
    });
  }

  protected async executeFlow(): Promise<void> {
    const color = this.getInputValue('color') || { r: 1, g: 1, b: 1 };

    try {
      // 创建基础材质
      const material = new THREE.MeshBasicMaterial({
        color: new THREE.Color(color.r, color.g, color.b)
      });

      // 输出材质
      this.setOutputValue('material', {
        threeMaterial: material,
        type: 'basic'
      });

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('创建基础材质失败:', error);
      throw error;
    }
  }
}

/**
 * 创建标准材质节点
 */
export class CreateStandardMaterialNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 颜色输入
    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      dataType: 'Color',
      direction: SocketDirection.INPUT,
      description: '颜色',
      defaultValue: { r: 1, g: 1, b: 1 }
    });

    // 金属度输入
    this.addInput({
      name: 'metalness',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '金属度',
      defaultValue: 0
    });

    // 粗糙度输入
    this.addInput({
      name: 'roughness',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '粗糙度',
      defaultValue: 1
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 材质输出
    this.addOutput({
      name: 'material',
      type: SocketType.DATA,
      dataType: 'Material',
      direction: SocketDirection.OUTPUT,
      description: '材质'
    });
  }

  protected async executeFlow(): Promise<void> {
    const color = this.getInputValue('color') || { r: 1, g: 1, b: 1 };
    const metalness = this.getInputValue('metalness') || 0;
    const roughness = this.getInputValue('roughness') || 1;

    try {
      // 创建标准材质
      const material = new THREE.MeshStandardMaterial({
        color: new THREE.Color(color.r, color.g, color.b),
        metalness: Math.max(0, Math.min(1, metalness)),
        roughness: Math.max(0, Math.min(1, roughness))
      });

      // 输出材质
      this.setOutputValue('material', {
        threeMaterial: material,
        type: 'standard'
      });

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('创建标准材质失败:', error);
      throw error;
    }
  }
}

/**
 * 创建物理材质节点
 */
export class CreatePhysicalMaterialNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 颜色输入
    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      dataType: 'Color',
      direction: SocketDirection.INPUT,
      description: '颜色',
      defaultValue: { r: 1, g: 1, b: 1 }
    });

    // 金属度输入
    this.addInput({
      name: 'metalness',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '金属度',
      defaultValue: 0
    });

    // 粗糙度输入
    this.addInput({
      name: 'roughness',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '粗糙度',
      defaultValue: 1
    });

    // 透射率输入
    this.addInput({
      name: 'transmission',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '透射率',
      defaultValue: 0
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 材质输出
    this.addOutput({
      name: 'material',
      type: SocketType.DATA,
      dataType: 'Material',
      direction: SocketDirection.OUTPUT,
      description: '材质'
    });
  }

  protected async executeFlow(): Promise<void> {
    const color = this.getInputValue('color') || { r: 1, g: 1, b: 1 };
    const metalness = this.getInputValue('metalness') || 0;
    const roughness = this.getInputValue('roughness') || 1;
    const transmission = this.getInputValue('transmission') || 0;

    try {
      // 创建物理材质
      const material = new THREE.MeshPhysicalMaterial({
        color: new THREE.Color(color.r, color.g, color.b),
        metalness: Math.max(0, Math.min(1, metalness)),
        roughness: Math.max(0, Math.min(1, roughness)),
        transmission: Math.max(0, Math.min(1, transmission))
      });

      // 输出材质
      this.setOutputValue('material', {
        threeMaterial: material,
        type: 'physical'
      });

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('创建物理材质失败:', error);
      throw error;
    }
  }
}

/**
 * 设置材质颜色节点
 */
export class SetMaterialColorNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 材质输入
    this.addInput({
      name: 'material',
      type: SocketType.DATA,
      dataType: 'Material',
      direction: SocketDirection.INPUT,
      description: '材质',
      optional: false
    });

    // 颜色输入
    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      dataType: 'Color',
      direction: SocketDirection.INPUT,
      description: '颜色',
      optional: false,
      defaultValue: { r: 1, g: 1, b: 1 }
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 材质输出
    this.addOutput({
      name: 'material',
      type: SocketType.DATA,
      dataType: 'Material',
      direction: SocketDirection.OUTPUT,
      description: '材质'
    });
  }

  protected async executeFlow(): Promise<void> {
    const material = this.getInputValue('material');
    const color = this.getInputValue('color');

    if (!material) {
      throw new Error('材质参数不能为空');
    }

    if (!color) {
      throw new Error('颜色参数不能为空');
    }

    try {
      // 设置材质颜色
      if (material.threeMaterial && material.threeMaterial.color) {
        material.threeMaterial.color.setRGB(color.r, color.g, color.b);
        material.threeMaterial.needsUpdate = true;
      }

      // 输出材质
      this.setOutputValue('material', material);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('设置材质颜色失败:', error);
      throw error;
    }
  }
}

/**
 * 设置材质纹理节点
 */
export class SetMaterialTextureNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 材质输入
    this.addInput({
      name: 'material',
      type: SocketType.DATA,
      dataType: 'Material',
      direction: SocketDirection.INPUT,
      description: '材质',
      optional: false
    });

    // 纹理输入
    this.addInput({
      name: 'texture',
      type: SocketType.DATA,
      dataType: 'Texture',
      direction: SocketDirection.INPUT,
      description: '纹理',
      optional: false
    });

    // 纹理类型输入
    this.addInput({
      name: 'textureType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '纹理类型',
      defaultValue: 'map'
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 材质输出
    this.addOutput({
      name: 'material',
      type: SocketType.DATA,
      dataType: 'Material',
      direction: SocketDirection.OUTPUT,
      description: '材质'
    });
  }

  protected async executeFlow(): Promise<void> {
    const material = this.getInputValue('material');
    const texture = this.getInputValue('texture');
    const textureType = this.getInputValue('textureType') || 'map';

    if (!material) {
      throw new Error('材质参数不能为空');
    }

    if (!texture) {
      throw new Error('纹理参数不能为空');
    }

    try {
      // 设置材质纹理
      if (material.threeMaterial && texture.threeTexture) {
        switch (textureType) {
          case 'map':
            material.threeMaterial.map = texture.threeTexture;
            break;
          case 'normalMap':
            material.threeMaterial.normalMap = texture.threeTexture;
            break;
          case 'roughnessMap':
            material.threeMaterial.roughnessMap = texture.threeTexture;
            break;
          case 'metalnessMap':
            material.threeMaterial.metalnessMap = texture.threeTexture;
            break;
          default:
            material.threeMaterial.map = texture.threeTexture;
        }
        material.threeMaterial.needsUpdate = true;
      }

      // 输出材质
      this.setOutputValue('material', material);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('设置材质纹理失败:', error);
      throw error;
    }
  }
}

/**
 * 设置材质透明度节点
 */
export class SetMaterialOpacityNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 材质输入
    this.addInput({
      name: 'material',
      type: SocketType.DATA,
      dataType: 'Material',
      direction: SocketDirection.INPUT,
      description: '材质',
      optional: false
    });

    // 透明度输入
    this.addInput({
      name: 'opacity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '透明度',
      optional: false,
      defaultValue: 1
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 材质输出
    this.addOutput({
      name: 'material',
      type: SocketType.DATA,
      dataType: 'Material',
      direction: SocketDirection.OUTPUT,
      description: '材质'
    });
  }

  protected async executeFlow(): Promise<void> {
    const material = this.getInputValue('material');
    const opacity = this.getInputValue('opacity');

    if (!material) {
      throw new Error('材质参数不能为空');
    }

    if (typeof opacity !== 'number' || opacity < 0 || opacity > 1) {
      throw new Error('透明度必须在0-1之间');
    }

    try {
      // 设置材质透明度
      if (material.threeMaterial) {
        material.threeMaterial.opacity = opacity;
        material.threeMaterial.transparent = opacity < 1;
        material.threeMaterial.needsUpdate = true;
      }

      // 输出材质
      this.setOutputValue('material', material);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('设置材质透明度失败:', error);
      throw error;
    }
  }
}
