/**
 * 第12批次节点简单测试
 * 验证节点是否能正常导入和实例化
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 第12批次节点简单测试开始...\n');

// 测试1: 检查文件是否存在
console.log('=== 📁 文件存在性检查 ===');
const mainFile = 'engine/src/visualscript/presets/ServerNodesBatch12.ts';
const indexFile = 'engine/src/visualscript/presets/ServerNodesBatch12Index.ts';

if (fs.existsSync(mainFile)) {
  console.log('✅ 主实现文件存在');
  const stats = fs.statSync(mainFile);
  console.log(`📦 文件大小: ${stats.size} 字节`);
} else {
  console.log('❌ 主实现文件不存在');
}

if (fs.existsSync(indexFile)) {
  console.log('✅ 索引文件存在');
} else {
  console.log('❌ 索引文件不存在');
}

// 测试2: 检查TypeScript编译
console.log('\n=== 🔧 TypeScript编译检查 ===');
const { execSync } = require('child_process');

try {
  execSync(`npx tsc --noEmit --skipLibCheck ${mainFile}`, { stdio: 'pipe' });
  console.log('✅ TypeScript编译通过');
} catch (error) {
  console.log('❌ TypeScript编译失败');
  console.log(error.stdout?.toString() || error.message);
}

// 测试3: 检查节点类名
console.log('\n=== 📊 节点类名检查 ===');
const expectedNodes = [
  'UpdateAssetInfoNode', 'MoveAssetToFolderNode', 'CreateAssetFolderNode', 'DeleteAssetFolderNode',
  'ShareAssetNode', 'GetAssetVersionsNode', 'CreateAssetVersionNode', 'RestoreAssetVersionNode',
  'GenerateAssetThumbnailNode', 'OptimizeAssetNode', 'JoinCollaborationRoomNode', 'LeaveCollaborationRoomNode',
  'SendCollaborationOperationNode', 'ReceiveCollaborationOperationNode', 'ResolveCollaborationConflictNode',
  'GetOnlineUsersNode', 'BroadcastCollaborationMessageNode', 'LockCollaborationResourceNode',
  'UnlockCollaborationResourceNode', 'SyncCollaborationStateNode'
];

try {
  const content = fs.readFileSync(mainFile, 'utf8');
  let foundCount = 0;
  
  expectedNodes.forEach((nodeName, index) => {
    if (content.includes(nodeName)) {
      foundCount++;
      console.log(`✅ ${index + 331}. ${nodeName}`);
    } else {
      console.log(`❌ ${index + 331}. ${nodeName} - 未找到`);
    }
  });
  
  console.log(`\n📊 总计: ${foundCount}/${expectedNodes.length} 个节点类找到`);
  
  if (foundCount === expectedNodes.length) {
    console.log('🎉 所有节点类都已正确实现！');
  } else {
    console.log(`⚠️ 还有 ${expectedNodes.length - foundCount} 个节点类未找到`);
  }
  
} catch (error) {
  console.log('❌ 读取文件失败:', error.message);
}

// 测试4: 检查节点映射
console.log('\n=== 🗺️ 节点映射检查 ===');
try {
  const indexContent = fs.readFileSync(indexFile, 'utf8');
  
  if (indexContent.includes('BATCH12_NODE_MAPPING')) {
    console.log('✅ 节点映射配置存在');
  } else {
    console.log('❌ 节点映射配置缺失');
  }
  
  if (indexContent.includes('BATCH12_NODE_CONFIGS')) {
    console.log('✅ 节点配置信息存在');
  } else {
    console.log('❌ 节点配置信息缺失');
  }
  
} catch (error) {
  console.log('❌ 读取索引文件失败:', error.message);
}

// 测试5: 检查集成状态
console.log('\n=== 🔗 集成状态检查 ===');
const engineFile = 'editor/src/services/EngineNodeIntegration.ts';
const editorFile = 'editor/src/services/NodeRegistryService.ts';

if (fs.existsSync(engineFile)) {
  const engineContent = fs.readFileSync(engineFile, 'utf8');
  if (engineContent.includes('registerBatch12Nodes')) {
    console.log('✅ 引擎集成已配置');
  } else {
    console.log('❌ 引擎集成未配置');
  }
} else {
  console.log('❌ 引擎集成文件不存在');
}

if (fs.existsSync(editorFile)) {
  const editorContent = fs.readFileSync(editorFile, 'utf8');
  if (editorContent.includes('initializeBatch12Nodes')) {
    console.log('✅ 编辑器集成已配置');
  } else {
    console.log('❌ 编辑器集成未配置');
  }
} else {
  console.log('❌ 编辑器集成文件不存在');
}

console.log('\n🎯 第12批次节点简单测试完成！');
console.log('📝 如果所有检查都通过，第12批次节点已成功实现');
