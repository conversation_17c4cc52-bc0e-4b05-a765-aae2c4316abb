/**
 * 测试运行脚本
 * 简化版的测试运行器，不依赖Jest
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 开始运行节点单元测试...');

// 简单的测试框架
class SimpleTestFramework {
  constructor() {
    this.tests = [];
    this.describes = [];
    this.currentDescribe = null;
    this.results = {
      passed: 0,
      failed: 0,
      total: 0
    };
  }

  describe(name, fn) {
    const previousDescribe = this.currentDescribe;
    this.currentDescribe = name;
    console.log(`\n📋 ${name}`);
    fn();
    this.currentDescribe = previousDescribe;
  }

  test(name, fn) {
    this.results.total++;
    try {
      fn();
      console.log(`  ✅ ${name}`);
      this.results.passed++;
    } catch (error) {
      console.log(`  ❌ ${name}`);
      console.log(`     错误: ${error.message}`);
      this.results.failed++;
    }
  }

  expect(actual) {
    return {
      toBe: (expected) => {
        if (actual !== expected) {
          throw new Error(`期望 ${expected}，但得到 ${actual}`);
        }
      },
      toBeCloseTo: (expected, precision = 5) => {
        const diff = Math.abs(actual - expected);
        const tolerance = Math.pow(10, -precision);
        if (diff > tolerance) {
          throw new Error(`期望 ${expected}（精度 ${precision}），但得到 ${actual}`);
        }
      },
      toEqual: (expected) => {
        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
          throw new Error(`期望 ${JSON.stringify(expected)}，但得到 ${JSON.stringify(actual)}`);
        }
      },
      toContain: (expected) => {
        if (!actual.includes(expected)) {
          throw new Error(`期望包含 "${expected}"，但在 "${actual}" 中未找到`);
        }
      }
    };
  }

  printResults() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 测试结果统计:');
    console.log(`✅ 通过: ${this.results.passed}`);
    console.log(`❌ 失败: ${this.results.failed}`);
    console.log(`📊 总计: ${this.results.total}`);
    console.log(`📈 成功率: ${((this.results.passed / this.results.total) * 100).toFixed(1)}%`);
    
    if (this.results.failed === 0) {
      console.log('\n🎉 所有测试都通过了！');
    } else {
      console.log(`\n⚠️  有 ${this.results.failed} 个测试失败`);
    }
  }
}

// 创建全局测试实例
const framework = new SimpleTestFramework();
global.describe = framework.describe.bind(framework);
global.test = framework.test.bind(framework);
global.expect = framework.expect.bind(framework);

// 模拟节点类（简化版）
class MockNode {
  constructor(type) {
    this.type = type;
    this.inputs = new Map();
    this.outputs = new Map();
  }

  setInputValue(name, value) {
    this.inputs.set(name, value);
  }

  getInputValue(name) {
    return this.inputs.get(name);
  }

  setOutputValue(name, value) {
    this.outputs.set(name, value);
  }

  getOutputValue(name) {
    return this.outputs.get(name);
  }

  async execute(context) {
    await this.executeInternal(context);
  }

  async executeInternal(context) {
    // 子类实现
  }
}

// 增强版数学节点测试
describe('增强版数学节点测试', () => {
  describe('正弦节点', () => {
    test('应该正确计算 sin(0) = 0', () => {
      const result = Math.sin(0);
      expect(result).toBeCloseTo(0, 5);
    });

    test('应该正确计算 sin(π/2) = 1', () => {
      const result = Math.sin(Math.PI / 2);
      expect(result).toBeCloseTo(1, 5);
    });

    test('应该正确计算 sin(π) ≈ 0', () => {
      const result = Math.sin(Math.PI);
      expect(result).toBeCloseTo(0, 5);
    });
  });

  describe('余弦节点', () => {
    test('应该正确计算 cos(0) = 1', () => {
      const result = Math.cos(0);
      expect(result).toBeCloseTo(1, 5);
    });

    test('应该正确计算 cos(π/2) ≈ 0', () => {
      const result = Math.cos(Math.PI / 2);
      expect(result).toBeCloseTo(0, 5);
    });

    test('应该正确计算 cos(π) = -1', () => {
      const result = Math.cos(Math.PI);
      expect(result).toBeCloseTo(-1, 5);
    });
  });

  describe('向量长度节点', () => {
    test('应该正确计算3-4-5直角三角形', () => {
      const vector = { x: 3, y: 4, z: 0 };
      const magnitude = Math.sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
      expect(magnitude).toBeCloseTo(5, 5);
    });

    test('应该正确计算单位向量长度', () => {
      const vector = { x: 1, y: 0, z: 0 };
      const magnitude = Math.sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
      expect(magnitude).toBeCloseTo(1, 5);
    });

    test('应该正确计算零向量长度', () => {
      const vector = { x: 0, y: 0, z: 0 };
      const magnitude = Math.sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
      expect(magnitude).toBe(0);
    });
  });

  describe('向量归一化节点', () => {
    test('应该正确归一化标准向量', () => {
      const vector = { x: 3, y: 4, z: 0 };
      const magnitude = Math.sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
      const normalized = {
        x: vector.x / magnitude,
        y: vector.y / magnitude,
        z: vector.z / magnitude
      };
      
      expect(normalized.x).toBeCloseTo(0.6, 5);
      expect(normalized.y).toBeCloseTo(0.8, 5);
      expect(normalized.z).toBeCloseTo(0, 5);
      
      // 验证归一化向量的长度为1
      const normalizedMagnitude = Math.sqrt(normalized.x * normalized.x + normalized.y * normalized.y + normalized.z * normalized.z);
      expect(normalizedMagnitude).toBeCloseTo(1, 5);
    });

    test('应该处理零向量', () => {
      const vector = { x: 0, y: 0, z: 0 };
      const magnitude = Math.sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
      
      if (magnitude === 0) {
        // 零向量应该返回零向量
        expect(magnitude).toBe(0);
      }
    });
  });
});

describe('增强版逻辑节点测试', () => {
  describe('逻辑与节点', () => {
    test('应该正确执行 true AND true = true', () => {
      const result = true && true;
      expect(result).toBe(true);
    });

    test('应该正确执行 true AND false = false', () => {
      const result = true && false;
      expect(result).toBe(false);
    });

    test('应该正确执行 false AND true = false', () => {
      const result = false && true;
      expect(result).toBe(false);
    });

    test('应该正确执行 false AND false = false', () => {
      const result = false && false;
      expect(result).toBe(false);
    });

    test('应该处理真值转换', () => {
      const result1 = Boolean(1) && Boolean(0);
      expect(result1).toBe(false);
      
      const result2 = Boolean('hello') && Boolean('');
      expect(result2).toBe(false);
    });
  });
});

describe('数据验证测试', () => {
  describe('数字验证', () => {
    test('应该正确验证有效数字', () => {
      expect(typeof 42 === 'number' && !isNaN(42)).toBe(true);
      expect(typeof Infinity === 'number' && !isNaN(Infinity)).toBe(true);
    });

    test('应该正确识别无效数字', () => {
      expect(typeof '42' === 'number' && !isNaN('42')).toBe(false);
      expect(typeof NaN === 'number' && !isNaN(NaN)).toBe(false);
      expect(typeof null === 'number' && !isNaN(null)).toBe(false);
    });
  });

  describe('向量验证', () => {
    function isValidVector3(vector) {
      return vector && 
             typeof vector === 'object' &&
             typeof vector.x === 'number' && !isNaN(vector.x) &&
             typeof vector.y === 'number' && !isNaN(vector.y) &&
             typeof vector.z === 'number' && !isNaN(vector.z);
    }

    test('应该正确验证有效向量', () => {
      expect(isValidVector3({ x: 1, y: 2, z: 3 })).toBe(true);
    });

    test('应该正确识别无效向量', () => {
      expect(isValidVector3({ x: 1, y: 2 })).toBe(false);        // 缺少z
      expect(isValidVector3({ x: '1', y: 2, z: 3 })).toBe(false); // x不是数字
      expect(isValidVector3('not a vector')).toBe(false);
      expect(isValidVector3(null)).toBe(false);
    });
  });
});

// 运行测试
console.log('🚀 开始执行节点单元测试');
console.log('='.repeat(50));

// 这里会执行所有的describe和test调用

setTimeout(() => {
  framework.printResults();
}, 100);
