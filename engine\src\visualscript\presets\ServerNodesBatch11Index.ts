/**
 * 第11批次：服务器端功能节点统一导出
 * 节点301-330：完整的30个节点实现
 */

// 用户服务节点 (301-310)
export {
  RegisterUserNode,
  LoginUserNode,
  LogoutUserNode,
  UpdateUserProfileNode,
  ChangePasswordNode,
  ResetPasswordNode,
  GetUserInfoNode,
  DeleteUserNode,
  SetUserRoleNode,
  ValidateTokenNode
} from './ServerNodesBatch11';

// 项目服务节点 (311-325)
export {
  CreateServerProjectNode,
  DeleteServerProjectNode,
  UpdateProjectNode,
  GetProjectListNode,
  GetProjectDetailsNode,
  ShareProjectNode,
  UnshareProjectNode,
  SetProjectPermissionNode,
  ForkProjectNode,
  ArchiveProjectNode,
  RestoreProjectNode,
  ExportProjectDataNode,
  ImportProjectDataNode,
  GetProjectStatsNode,
  BackupProjectNode
} from './ServerNodesBatch11';

// 资产服务节点 (326-330)
export {
  UploadAssetNode,
  DownloadAssetNode,
  DeleteServerAssetNode,
  GetAssetListNode,
  GetAssetInfoNode
} from './ServerNodesBatch11';

/**
 * 第11批次节点映射表
 * 用于节点注册和类型检查
 */
export const BATCH11_NODE_MAPPING = {
  // 用户服务节点 (301-310)
  'server/user/registerUser': 'RegisterUserNode',
  'server/user/loginUser': 'LoginUserNode',
  'server/user/logoutUser': 'LogoutUserNode',
  'server/user/updateUserProfile': 'UpdateUserProfileNode',
  'server/user/changePassword': 'ChangePasswordNode',
  'server/user/resetPassword': 'ResetPasswordNode',
  'server/user/getUserInfo': 'GetUserInfoNode',
  'server/user/deleteUser': 'DeleteUserNode',
  'server/user/setUserRole': 'SetUserRoleNode',
  'server/user/validateToken': 'ValidateTokenNode',

  // 项目服务节点 (311-325)
  'server/project/createProject': 'CreateServerProjectNode',
  'server/project/deleteProject': 'DeleteServerProjectNode',
  'server/project/updateProject': 'UpdateProjectNode',
  'server/project/getProjectList': 'GetProjectListNode',
  'server/project/getProjectDetails': 'GetProjectDetailsNode',
  'server/project/shareProject': 'ShareProjectNode',
  'server/project/unshareProject': 'UnshareProjectNode',
  'server/project/setProjectPermission': 'SetProjectPermissionNode',
  'server/project/forkProject': 'ForkProjectNode',
  'server/project/archiveProject': 'ArchiveProjectNode',
  'server/project/restoreProject': 'RestoreProjectNode',
  'server/project/exportProjectData': 'ExportProjectDataNode',
  'server/project/importProjectData': 'ImportProjectDataNode',
  'server/project/getProjectStats': 'GetProjectStatsNode',
  'server/project/backupProject': 'BackupProjectNode',

  // 资产服务节点 (326-330)
  'server/asset/uploadAsset': 'UploadAssetNode',
  'server/asset/downloadAsset': 'DownloadAssetNode',
  'server/asset/deleteAsset': 'DeleteServerAssetNode',
  'server/asset/getAssetList': 'GetAssetListNode',
  'server/asset/getAssetInfo': 'GetAssetInfoNode'
};

/**
 * 第11批次节点信息配置
 * 用于编辑器注册
 */
export const BATCH11_NODE_CONFIGS = [
  // 用户服务节点 (301-310)
  {
    type: 'server/user/registerUser',
    label: '用户注册',
    description: '注册新用户账户',
    category: 'NETWORK',
    icon: 'user-add',
    color: '#1890ff',
    tags: ['服务器', '用户', '注册']
  },
  {
    type: 'server/user/loginUser',
    label: '用户登录',
    description: '用户账户登录',
    category: 'NETWORK',
    icon: 'login',
    color: '#1890ff',
    tags: ['服务器', '用户', '登录']
  },
  {
    type: 'server/user/logoutUser',
    label: '用户登出',
    description: '用户账户登出',
    category: 'NETWORK',
    icon: 'logout',
    color: '#1890ff',
    tags: ['服务器', '用户', '登出']
  },
  {
    type: 'server/user/updateUserProfile',
    label: '更新用户资料',
    description: '更新用户个人信息',
    category: 'NETWORK',
    icon: 'user-edit',
    color: '#1890ff',
    tags: ['服务器', '用户', '更新']
  },
  {
    type: 'server/user/changePassword',
    label: '修改密码',
    description: '修改用户登录密码',
    category: 'NETWORK',
    icon: 'key',
    color: '#1890ff',
    tags: ['服务器', '用户', '密码']
  },
  {
    type: 'server/user/resetPassword',
    label: '重置密码',
    description: '重置用户密码',
    category: 'NETWORK',
    icon: 'key-reset',
    color: '#1890ff',
    tags: ['服务器', '用户', '重置']
  },
  {
    type: 'server/user/getUserInfo',
    label: '获取用户信息',
    description: '获取用户详细信息',
    category: 'NETWORK',
    icon: 'user-info',
    color: '#1890ff',
    tags: ['服务器', '用户', '查询']
  },
  {
    type: 'server/user/deleteUser',
    label: '删除用户',
    description: '删除用户账户',
    category: 'NETWORK',
    icon: 'user-delete',
    color: '#ff4d4f',
    tags: ['服务器', '用户', '删除']
  },
  {
    type: 'server/user/setUserRole',
    label: '设置用户角色',
    description: '设置用户权限角色',
    category: 'NETWORK',
    icon: 'user-role',
    color: '#1890ff',
    tags: ['服务器', '用户', '权限']
  },
  {
    type: 'server/user/validateToken',
    label: '验证令牌',
    description: '验证用户访问令牌',
    category: 'NETWORK',
    icon: 'token',
    color: '#1890ff',
    tags: ['服务器', '用户', '验证']
  },

  // 项目服务节点 (311-325)
  {
    type: 'server/project/createProject',
    label: '创建服务器项目',
    description: '在服务器创建新项目',
    category: 'NETWORK',
    icon: 'project-add',
    color: '#52c41a',
    tags: ['服务器', '项目', '创建']
  },
  {
    type: 'server/project/deleteProject',
    label: '删除服务器项目',
    description: '从服务器删除项目',
    category: 'NETWORK',
    icon: 'project-delete',
    color: '#ff4d4f',
    tags: ['服务器', '项目', '删除']
  },
  {
    type: 'server/project/updateProject',
    label: '更新项目信息',
    description: '更新服务器项目信息',
    category: 'NETWORK',
    icon: 'project-edit',
    color: '#52c41a',
    tags: ['服务器', '项目', '更新']
  },
  {
    type: 'server/project/getProjectList',
    label: '获取项目列表',
    description: '获取用户项目列表',
    category: 'NETWORK',
    icon: 'project-list',
    color: '#52c41a',
    tags: ['服务器', '项目', '列表']
  },
  {
    type: 'server/project/getProjectDetails',
    label: '获取项目详情',
    description: '获取项目详细信息',
    category: 'NETWORK',
    icon: 'project-info',
    color: '#52c41a',
    tags: ['服务器', '项目', '详情']
  },
  {
    type: 'server/project/shareProject',
    label: '分享项目',
    description: '分享项目给其他用户',
    category: 'NETWORK',
    icon: 'share',
    color: '#52c41a',
    tags: ['服务器', '项目', '分享']
  },
  {
    type: 'server/project/unshareProject',
    label: '取消分享',
    description: '取消项目分享',
    category: 'NETWORK',
    icon: 'unshare',
    color: '#52c41a',
    tags: ['服务器', '项目', '取消分享']
  },
  {
    type: 'server/project/setProjectPermission',
    label: '设置项目权限',
    description: '设置用户项目权限',
    category: 'NETWORK',
    icon: 'permission',
    color: '#52c41a',
    tags: ['服务器', '项目', '权限']
  },
  {
    type: 'server/project/forkProject',
    label: '复制项目',
    description: '复制现有项目',
    category: 'NETWORK',
    icon: 'fork',
    color: '#52c41a',
    tags: ['服务器', '项目', '复制']
  },
  {
    type: 'server/project/archiveProject',
    label: '归档项目',
    description: '归档不活跃项目',
    category: 'NETWORK',
    icon: 'archive',
    color: '#faad14',
    tags: ['服务器', '项目', '归档']
  },
  {
    type: 'server/project/restoreProject',
    label: '恢复项目',
    description: '恢复归档项目',
    category: 'NETWORK',
    icon: 'restore',
    color: '#52c41a',
    tags: ['服务器', '项目', '恢复']
  },
  {
    type: 'server/project/exportProjectData',
    label: '导出项目数据',
    description: '导出项目完整数据',
    category: 'NETWORK',
    icon: 'export',
    color: '#52c41a',
    tags: ['服务器', '项目', '导出']
  },
  {
    type: 'server/project/importProjectData',
    label: '导入项目数据',
    description: '导入项目数据到服务器',
    category: 'NETWORK',
    icon: 'import',
    color: '#52c41a',
    tags: ['服务器', '项目', '导入']
  },
  {
    type: 'server/project/getProjectStats',
    label: '获取项目统计',
    description: '获取项目使用统计',
    category: 'NETWORK',
    icon: 'stats',
    color: '#52c41a',
    tags: ['服务器', '项目', '统计']
  },
  {
    type: 'server/project/backupProject',
    label: '备份项目',
    description: '创建项目备份',
    category: 'NETWORK',
    icon: 'backup',
    color: '#52c41a',
    tags: ['服务器', '项目', '备份']
  },

  // 资产服务节点 (326-330)
  {
    type: 'server/asset/uploadAsset',
    label: '上传资产',
    description: '上传资产文件到服务器',
    category: 'NETWORK',
    icon: 'upload',
    color: '#722ed1',
    tags: ['服务器', '资产', '上传']
  },
  {
    type: 'server/asset/downloadAsset',
    label: '下载资产',
    description: '从服务器下载资产',
    category: 'NETWORK',
    icon: 'download',
    color: '#722ed1',
    tags: ['服务器', '资产', '下载']
  },
  {
    type: 'server/asset/deleteAsset',
    label: '删除服务器资产',
    description: '从服务器删除资产',
    category: 'NETWORK',
    icon: 'asset-delete',
    color: '#ff4d4f',
    tags: ['服务器', '资产', '删除']
  },
  {
    type: 'server/asset/getAssetList',
    label: '获取资产列表',
    description: '获取项目资产列表',
    category: 'NETWORK',
    icon: 'asset-list',
    color: '#722ed1',
    tags: ['服务器', '资产', '列表']
  },
  {
    type: 'server/asset/getAssetInfo',
    label: '获取资产信息',
    description: '获取资产详细信息',
    category: 'NETWORK',
    icon: 'asset-info',
    color: '#722ed1',
    tags: ['服务器', '资产', '信息']
  }
];
