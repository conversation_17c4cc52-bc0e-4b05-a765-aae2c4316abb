/**
 * 第7批次节点功能测试
 * 测试动画系统扩展节点的基本功能
 */

import { nodeRegistryService } from '../services/NodeRegistryService';

describe('第7批次节点测试 - 动画系统扩展（节点181-210）', () => {
  beforeAll(() => {
    // 确保节点注册服务已初始化
    expect(nodeRegistryService).toBeDefined();
  });

  describe('节点注册测试', () => {
    test('应该注册所有动画曲线节点', () => {
      const animationCurveNodes = [
        'animation/curve/evaluateCurve',
        'animation/state/createStateMachine',
        'animation/state/addState',
        'animation/state/addTransition',
        'animation/state/setCurrentState'
      ];

      animationCurveNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.type).toBe(nodeType);
      });
    });

    test('应该注册所有音频系统节点', () => {
      const audioNodes = [
        'audio/source/create3DAudioSource',
        'audio/source/setAudioPosition',
        'audio/source/setAudioVelocity',
        'audio/listener/setListenerPosition',
        'audio/listener/setListenerOrientation',
        'audio/effect/createReverb',
        'audio/effect/createEcho',
        'audio/effect/createFilter',
        'audio/analysis/createAnalyzer',
        'audio/analysis/getFrequencyData',
        'audio/analysis/getWaveformData',
        'audio/streaming/createAudioStream',
        'audio/streaming/connectStream',
        'audio/recording/startRecording',
        'audio/recording/stopRecording'
      ];

      audioNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.type).toBe(nodeType);
      });
    });

    test('应该注册所有场景管理节点', () => {
      const sceneNodes = [
        'scene/management/createScene',
        'scene/management/loadScene',
        'scene/management/saveScene',
        'scene/management/switchScene',
        'scene/management/addToScene',
        'scene/management/removeFromScene',
        'scene/culling/enableFrustumCulling',
        'scene/culling/enableOcclusionCulling',
        'scene/optimization/enableBatching',
        'scene/optimization/enableInstancing'
      ];

      sceneNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.type).toBe(nodeType);
      });
    });

    test('应该注册总共30个第7批次节点', () => {
      const allNodes = nodeRegistryService.getAllNodes();
      const batch7Nodes = allNodes.filter(node => 
        node.type.startsWith('animation/curve/') ||
        node.type.startsWith('animation/state/') ||
        node.type.startsWith('audio/') ||
        node.type.startsWith('scene/')
      );

      expect(batch7Nodes.length).toBeGreaterThanOrEqual(30);
    });
  });

  describe('节点分类测试', () => {
    test('动画节点应该有正确的分类', () => {
      const animationNode = nodeRegistryService.getNode('animation/curve/evaluateCurve');
      expect(animationNode?.category).toBe('ANIMATION');
    });

    test('音频节点应该有正确的分类', () => {
      const audioNode = nodeRegistryService.getNode('audio/source/create3DAudioSource');
      expect(audioNode?.category).toBe('AUDIO');
    });

    test('场景节点应该有正确的分类', () => {
      const sceneNode = nodeRegistryService.getNode('scene/management/createScene');
      expect(sceneNode?.category).toBe('ENTITY');
    });
  });

  describe('节点标签测试', () => {
    test('动画节点应该有正确的标签', () => {
      const animationNode = nodeRegistryService.getNode('animation/curve/evaluateCurve');
      expect(animationNode?.tags).toContain('动画');
      expect(animationNode?.tags).toContain('曲线');
    });

    test('音频节点应该有正确的标签', () => {
      const audioNode = nodeRegistryService.getNode('audio/source/create3DAudioSource');
      expect(audioNode?.tags).toContain('音频');
      expect(audioNode?.tags).toContain('3D');
    });

    test('场景节点应该有正确的标签', () => {
      const sceneNode = nodeRegistryService.getNode('scene/management/createScene');
      expect(sceneNode?.tags).toContain('场景');
      expect(sceneNode?.tags).toContain('管理');
    });
  });

  describe('节点搜索测试', () => {
    test('应该能够通过标签搜索动画节点', () => {
      const animationNodes = nodeRegistryService.getNodesByTag('动画');
      expect(animationNodes.length).toBeGreaterThan(0);
      
      const curveNodes = animationNodes.filter(node => 
        node.type.includes('curve') || node.type.includes('state')
      );
      expect(curveNodes.length).toBeGreaterThan(0);
    });

    test('应该能够通过标签搜索音频节点', () => {
      const audioNodes = nodeRegistryService.getNodesByTag('音频');
      expect(audioNodes.length).toBeGreaterThanOrEqual(15);
    });

    test('应该能够通过标签搜索场景节点', () => {
      const sceneNodes = nodeRegistryService.getNodesByTag('场景');
      expect(sceneNodes.length).toBeGreaterThanOrEqual(10);
    });
  });

  describe('节点属性测试', () => {
    test('所有节点应该有必要的属性', () => {
      const testNodes = [
        'animation/curve/evaluateCurve',
        'audio/source/create3DAudioSource',
        'scene/management/createScene'
      ];

      testNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.type).toBe(nodeType);
        expect(node?.label).toBeDefined();
        expect(node?.description).toBeDefined();
        expect(node?.category).toBeDefined();
        expect(node?.icon).toBeDefined();
        expect(node?.color).toBeDefined();
        expect(node?.tags).toBeDefined();
        expect(Array.isArray(node?.tags)).toBe(true);
      });
    });

    test('节点标签应该是数组且不为空', () => {
      const node = nodeRegistryService.getNode('animation/curve/evaluateCurve');
      expect(Array.isArray(node?.tags)).toBe(true);
      expect(node?.tags.length).toBeGreaterThan(0);
    });

    test('节点颜色应该是有效的颜色值', () => {
      const node = nodeRegistryService.getNode('audio/source/create3DAudioSource');
      expect(node?.color).toMatch(/^#[0-9A-Fa-f]{6}$/);
    });
  });

  describe('节点功能分组测试', () => {
    test('动画系统节点应该正确分组', () => {
      const allNodes = nodeRegistryService.getAllNodes();
      
      const curveNodes = allNodes.filter(node => node.type.includes('curve'));
      const stateNodes = allNodes.filter(node => node.type.includes('state'));
      
      expect(curveNodes.length).toBeGreaterThan(0);
      expect(stateNodes.length).toBeGreaterThan(0);
    });

    test('音频系统节点应该正确分组', () => {
      const allNodes = nodeRegistryService.getAllNodes();
      
      const sourceNodes = allNodes.filter(node => node.type.includes('audio/source'));
      const effectNodes = allNodes.filter(node => node.type.includes('audio/effect'));
      const analysisNodes = allNodes.filter(node => node.type.includes('audio/analysis'));
      
      expect(sourceNodes.length).toBeGreaterThan(0);
      expect(effectNodes.length).toBeGreaterThan(0);
      expect(analysisNodes.length).toBeGreaterThan(0);
    });

    test('场景管理节点应该正确分组', () => {
      const allNodes = nodeRegistryService.getAllNodes();
      
      const managementNodes = allNodes.filter(node => node.type.includes('scene/management'));
      const cullingNodes = allNodes.filter(node => node.type.includes('scene/culling'));
      const optimizationNodes = allNodes.filter(node => node.type.includes('scene/optimization'));
      
      expect(managementNodes.length).toBeGreaterThan(0);
      expect(cullingNodes.length).toBeGreaterThan(0);
      expect(optimizationNodes.length).toBeGreaterThan(0);
    });
  });

  describe('性能测试', () => {
    test('节点注册性能应该在合理范围内', () => {
      const startTime = performance.now();
      
      // 获取所有第7批次节点
      const allNodes = nodeRegistryService.getAllNodes();
      const batch7Nodes = allNodes.filter(node => 
        node.type.startsWith('animation/curve/') ||
        node.type.startsWith('animation/state/') ||
        node.type.startsWith('audio/') ||
        node.type.startsWith('scene/')
      );
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(batch7Nodes.length).toBeGreaterThanOrEqual(30);
      expect(duration).toBeLessThan(100); // 应该在100ms内完成
    });

    test('节点搜索性能应该在合理范围内', () => {
      const startTime = performance.now();
      
      // 执行多次搜索
      nodeRegistryService.getNodesByTag('动画');
      nodeRegistryService.getNodesByTag('音频');
      nodeRegistryService.getNodesByTag('场景');
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(50); // 应该在50ms内完成
    });
  });
});
