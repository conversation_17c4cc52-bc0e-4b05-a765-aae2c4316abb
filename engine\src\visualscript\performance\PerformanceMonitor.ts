/**
 * 视觉脚本性能监控系统
 * 监控节点执行性能，提供性能分析和优化建议
 */

export interface NodeExecutionMetrics {
  nodeId: string;
  nodeType: string;
  executionTime: number;
  memoryUsage: number;
  status: 'success' | 'error' | 'timeout';
  timestamp: number;
  errorMessage?: string;
}

export interface NodeStatistics {
  nodeType: string;
  totalExecutions: number;
  successCount: number;
  errorCount: number;
  timeoutCount: number;
  averageExecutionTime: number;
  maxExecutionTime: number;
  minExecutionTime: number;
  totalExecutionTime: number;
  memoryUsage: {
    average: number;
    max: number;
    min: number;
  };
  lastExecuted: number;
}

export interface PerformanceReport {
  generatedAt: number;
  totalNodes: number;
  totalExecutions: number;
  overallSuccessRate: number;
  averageExecutionTime: number;
  topSlowNodes: Array<{ nodeType: string; averageTime: number }>;
  topErrorNodes: Array<{ nodeType: string; errorRate: number }>;
  memoryUsage: {
    total: number;
    average: number;
    peak: number;
  };
  recommendations: string[];
}

/**
 * 性能监控器主类
 */
export class PerformanceMonitor {
  private metrics: NodeExecutionMetrics[] = [];
  private statistics: Map<string, NodeStatistics> = new Map();
  private maxMetricsHistory: number = 10000; // 最多保存10000条记录
  private performanceThresholds = {
    slowExecutionTime: 100, // 100ms
    highErrorRate: 0.1,     // 10%
    highMemoryUsage: 50 * 1024 * 1024 // 50MB
  };

  /**
   * 记录节点执行指标
   */
  recordNodeExecution(
    nodeId: string,
    nodeType: string,
    executionTime: number,
    memoryUsage: number,
    status: 'success' | 'error' | 'timeout',
    errorMessage?: string
  ): void {
    const metric: NodeExecutionMetrics = {
      nodeId,
      nodeType,
      executionTime,
      memoryUsage,
      status,
      timestamp: Date.now(),
      errorMessage
    };

    // 添加到指标历史
    this.metrics.push(metric);

    // 限制历史记录数量
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics.shift();
    }

    // 更新统计信息
    this.updateStatistics(metric);
  }

  /**
   * 更新节点统计信息
   */
  private updateStatistics(metric: NodeExecutionMetrics): void {
    let stats = this.statistics.get(metric.nodeType);
    
    if (!stats) {
      stats = {
        nodeType: metric.nodeType,
        totalExecutions: 0,
        successCount: 0,
        errorCount: 0,
        timeoutCount: 0,
        averageExecutionTime: 0,
        maxExecutionTime: 0,
        minExecutionTime: Infinity,
        totalExecutionTime: 0,
        memoryUsage: {
          average: 0,
          max: 0,
          min: Infinity
        },
        lastExecuted: 0
      };
      this.statistics.set(metric.nodeType, stats);
    }

    // 更新计数
    stats.totalExecutions++;
    stats.lastExecuted = metric.timestamp;

    switch (metric.status) {
      case 'success':
        stats.successCount++;
        break;
      case 'error':
        stats.errorCount++;
        break;
      case 'timeout':
        stats.timeoutCount++;
        break;
    }

    // 更新执行时间统计
    stats.totalExecutionTime += metric.executionTime;
    stats.averageExecutionTime = stats.totalExecutionTime / stats.totalExecutions;
    stats.maxExecutionTime = Math.max(stats.maxExecutionTime, metric.executionTime);
    stats.minExecutionTime = Math.min(stats.minExecutionTime, metric.executionTime);

    // 更新内存使用统计
    const memoryStats = stats.memoryUsage;
    memoryStats.max = Math.max(memoryStats.max, metric.memoryUsage);
    memoryStats.min = Math.min(memoryStats.min, metric.memoryUsage);
    
    // 计算内存使用平均值（简化计算）
    const memoryMetrics = this.metrics
      .filter(m => m.nodeType === metric.nodeType)
      .map(m => m.memoryUsage);
    memoryStats.average = memoryMetrics.reduce((sum, mem) => sum + mem, 0) / memoryMetrics.length;
  }

  /**
   * 获取节点统计信息
   */
  getNodeStatistics(nodeType: string): NodeStatistics | undefined {
    return this.statistics.get(nodeType);
  }

  /**
   * 获取所有节点统计信息
   */
  getAllStatistics(): NodeStatistics[] {
    return Array.from(this.statistics.values());
  }

  /**
   * 生成性能报告
   */
  generatePerformanceReport(): PerformanceReport {
    const allStats = this.getAllStatistics();
    const totalExecutions = allStats.reduce((sum, stat) => sum + stat.totalExecutions, 0);
    const totalSuccesses = allStats.reduce((sum, stat) => sum + stat.successCount, 0);
    const totalExecutionTime = allStats.reduce((sum, stat) => sum + stat.totalExecutionTime, 0);
    const totalMemoryUsage = this.metrics.reduce((sum, metric) => sum + metric.memoryUsage, 0);

    // 计算最慢的节点
    const topSlowNodes = allStats
      .filter(stat => stat.totalExecutions > 0)
      .sort((a, b) => b.averageExecutionTime - a.averageExecutionTime)
      .slice(0, 10)
      .map(stat => ({
        nodeType: stat.nodeType,
        averageTime: stat.averageExecutionTime
      }));

    // 计算错误率最高的节点
    const topErrorNodes = allStats
      .filter(stat => stat.totalExecutions > 0)
      .map(stat => ({
        nodeType: stat.nodeType,
        errorRate: (stat.errorCount + stat.timeoutCount) / stat.totalExecutions
      }))
      .filter(node => node.errorRate > 0)
      .sort((a, b) => b.errorRate - a.errorRate)
      .slice(0, 10);

    // 生成优化建议
    const recommendations = this.generateRecommendations(allStats);

    return {
      generatedAt: Date.now(),
      totalNodes: allStats.length,
      totalExecutions,
      overallSuccessRate: totalExecutions > 0 ? totalSuccesses / totalExecutions : 0,
      averageExecutionTime: totalExecutions > 0 ? totalExecutionTime / totalExecutions : 0,
      topSlowNodes,
      topErrorNodes,
      memoryUsage: {
        total: totalMemoryUsage,
        average: this.metrics.length > 0 ? totalMemoryUsage / this.metrics.length : 0,
        peak: Math.max(...this.metrics.map(m => m.memoryUsage), 0)
      },
      recommendations
    };
  }

  /**
   * 生成性能优化建议
   */
  private generateRecommendations(stats: NodeStatistics[]): string[] {
    const recommendations: string[] = [];

    // 检查慢执行节点
    const slowNodes = stats.filter(stat => 
      stat.averageExecutionTime > this.performanceThresholds.slowExecutionTime
    );
    if (slowNodes.length > 0) {
      recommendations.push(
        `发现 ${slowNodes.length} 个执行缓慢的节点类型，建议优化算法或减少计算复杂度`
      );
    }

    // 检查高错误率节点
    const errorNodes = stats.filter(stat => {
      const errorRate = (stat.errorCount + stat.timeoutCount) / stat.totalExecutions;
      return errorRate > this.performanceThresholds.highErrorRate;
    });
    if (errorNodes.length > 0) {
      recommendations.push(
        `发现 ${errorNodes.length} 个高错误率的节点类型，建议检查输入验证和错误处理`
      );
    }

    // 检查内存使用
    const highMemoryNodes = stats.filter(stat => 
      stat.memoryUsage.average > this.performanceThresholds.highMemoryUsage
    );
    if (highMemoryNodes.length > 0) {
      recommendations.push(
        `发现 ${highMemoryNodes.length} 个高内存使用的节点类型，建议优化内存管理`
      );
    }

    // 通用建议
    if (recommendations.length === 0) {
      recommendations.push('系统性能良好，继续保持当前的优化水平');
    } else {
      recommendations.push('建议定期监控性能指标，及时发现和解决性能问题');
    }

    return recommendations;
  }

  /**
   * 清除历史数据
   */
  clearHistory(): void {
    this.metrics = [];
    this.statistics.clear();
  }

  /**
   * 导出性能数据
   */
  exportData(): {
    metrics: NodeExecutionMetrics[];
    statistics: NodeStatistics[];
    report: PerformanceReport;
  } {
    return {
      metrics: [...this.metrics],
      statistics: this.getAllStatistics(),
      report: this.generatePerformanceReport()
    };
  }

  /**
   * 获取实时性能指标
   */
  getRealTimeMetrics(): {
    activeNodes: number;
    executionsPerSecond: number;
    averageResponseTime: number;
    errorRate: number;
  } {
    const now = Date.now();
    const lastMinuteMetrics = this.metrics.filter(m => now - m.timestamp < 60000);
    
    const activeNodes = new Set(lastMinuteMetrics.map(m => m.nodeType)).size;
    const executionsPerSecond = lastMinuteMetrics.length / 60;
    const averageResponseTime = lastMinuteMetrics.length > 0 
      ? lastMinuteMetrics.reduce((sum, m) => sum + m.executionTime, 0) / lastMinuteMetrics.length 
      : 0;
    const errorCount = lastMinuteMetrics.filter(m => m.status !== 'success').length;
    const errorRate = lastMinuteMetrics.length > 0 ? errorCount / lastMinuteMetrics.length : 0;

    return {
      activeNodes,
      executionsPerSecond,
      averageResponseTime,
      errorRate
    };
  }
}

/**
 * 全局性能监控器实例
 */
export const globalPerformanceMonitor = new PerformanceMonitor();
