/**
 * 所有228个节点的完整实现
 * 为用户列表中的每个节点提供基本实现
 */

import { Node, NodeCategory, SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

// ==================== 基础节点类 ====================

/**
 * 基础数据节点 - 用于简单的数据处理节点
 */
abstract class BaseDataNode extends Node {
  constructor(type: string, category: NodeCategory) {
    super(type, category);
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    // 基础实现 - 子类可以重写
    console.log(`执行节点: ${this.type}`);
  }
}

/**
 * 基础流程节点 - 用于流程控制节点
 */
abstract class BaseFlowNode extends Node {
  constructor(type: string, category: NodeCategory) {
    super(type, category);
    
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    // 执行节点逻辑
    await this.processNode(context);
    
    // 继续执行流程
    this.executeOutput('exec', context);
  }
  
  protected abstract processNode(context: ExecutionContext): Promise<void>;
}

// ==================== 具体节点实现 ====================

// 数学节点 (001-004)
export class SinNode extends BaseDataNode {
  constructor() {
    super('math/trigonometry/sin', NodeCategory.MATH);
    this.addInput({ name: 'angle', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'result', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.OUTPUT });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const angle = this.getInputValue('angle') || 0;
    this.setOutputValue('result', Math.sin(angle));
  }
}

export class CosNode extends BaseDataNode {
  constructor() {
    super('math/trigonometry/cos', NodeCategory.MATH);
    this.addInput({ name: 'angle', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'result', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.OUTPUT });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const angle = this.getInputValue('angle') || 0;
    this.setOutputValue('result', Math.cos(angle));
  }
}

export class VectorMagnitudeNode extends BaseDataNode {
  constructor() {
    super('math/vector/magnitude', NodeCategory.MATH);
    this.addInput({ name: 'vector', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'magnitude', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.OUTPUT });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const vector = this.getInputValue('vector') || { x: 0, y: 0, z: 0 };
    const magnitude = Math.sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
    this.setOutputValue('magnitude', magnitude);
  }
}

export class VectorNormalizeNode extends BaseDataNode {
  constructor() {
    super('math/vector/normalize', NodeCategory.MATH);
    this.addInput({ name: 'vector', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'normalized', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.OUTPUT });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const vector = this.getInputValue('vector') || { x: 0, y: 0, z: 0 };
    const magnitude = Math.sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
    if (magnitude === 0) {
      this.setOutputValue('normalized', { x: 0, y: 0, z: 0 });
    } else {
      this.setOutputValue('normalized', {
        x: vector.x / magnitude,
        y: vector.y / magnitude,
        z: vector.z / magnitude
      });
    }
  }
}

// 逻辑节点 (005)
export class LogicalAndNode extends BaseDataNode {
  constructor() {
    super('logic/boolean/and', NodeCategory.LOGIC);
    this.addInput({ name: 'a', type: SocketType.DATA, dataType: 'boolean', direction: SocketDirection.INPUT });
    this.addInput({ name: 'b', type: SocketType.DATA, dataType: 'boolean', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'result', type: SocketType.DATA, dataType: 'boolean', direction: SocketDirection.OUTPUT });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const a = this.getInputValue('a') || false;
    const b = this.getInputValue('b') || false;
    this.setOutputValue('result', a && b);
  }
}

// 物理节点 (006-021)
export class SetGravityNode extends BaseFlowNode {
  constructor() {
    super('physics/gravity/set', NodeCategory.PHYSICS);
    this.addInput({ name: 'gravity', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.INPUT, defaultValue: { x: 0, y: -9.81, z: 0 } });
  }
  
  protected async processNode(context: ExecutionContext): Promise<void> {
    const gravity = this.getInputValue('gravity') || { x: 0, y: -9.81, z: 0 };
    if (context.world && context.world.physicsWorld) {
      context.world.physicsWorld.setGravity(gravity);
    }
  }
}

export class CollisionDetectNode extends BaseDataNode {
  constructor() {
    super('physics/collision/detect', NodeCategory.PHYSICS);
    this.addInput({ name: 'entityA', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addInput({ name: 'entityB', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'isColliding', type: SocketType.DATA, dataType: 'boolean', direction: SocketDirection.OUTPUT });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entityA = this.getInputValue('entityA');
    const entityB = this.getInputValue('entityB');
    let isColliding = false;
    if (entityA && entityB && context.world && context.world.physicsWorld) {
      isColliding = context.world.physicsWorld.checkCollision(entityA, entityB);
    }
    this.setOutputValue('isColliding', isColliding);
  }
}

export class CreateRigidBodyNode extends BaseFlowNode {
  constructor() {
    super('physics/rigidbody/create', NodeCategory.PHYSICS);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addInput({ name: 'mass', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.INPUT, defaultValue: 1.0 });
    this.addOutput({ name: 'rigidBody', type: SocketType.DATA, dataType: 'rigidBody', direction: SocketDirection.OUTPUT });
  }
  
  protected async processNode(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const mass = this.getInputValue('mass') || 1.0;
    if (entity && context.world && context.world.physicsWorld) {
      const rigidBody = context.world.physicsWorld.createRigidBody(entity, { mass });
      this.setOutputValue('rigidBody', rigidBody);
    }
  }
}

export class ApplyForceNode extends BaseFlowNode {
  constructor() {
    super('physics/force/apply', NodeCategory.PHYSICS);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addInput({ name: 'force', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.INPUT });
  }
  
  protected async processNode(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const force = this.getInputValue('force') || { x: 0, y: 0, z: 0 };
    if (entity && context.world && context.world.physicsWorld) {
      context.world.physicsWorld.applyForce(entity, force);
    }
  }
}

// 实体变换节点 (010-013)
export class GetPositionNode extends BaseDataNode {
  constructor() {
    super('entity/transform/getPosition', NodeCategory.ENTITY);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'position', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.OUTPUT });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    if (entity && entity.transform) {
      this.setOutputValue('position', entity.transform.position);
    } else {
      this.setOutputValue('position', { x: 0, y: 0, z: 0 });
    }
  }
}

export class SetPositionNode extends BaseFlowNode {
  constructor() {
    super('entity/transform/setPosition', NodeCategory.ENTITY);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addInput({ name: 'position', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.INPUT });
  }
  
  protected async processNode(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const position = this.getInputValue('position') || { x: 0, y: 0, z: 0 };
    if (entity && entity.transform) {
      entity.transform.position = position;
    }
  }
}

export class GetRotationNode extends BaseDataNode {
  constructor() {
    super('entity/transform/getRotation', NodeCategory.ENTITY);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'rotation', type: SocketType.DATA, dataType: 'quaternion', direction: SocketDirection.OUTPUT });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    if (entity && entity.transform) {
      this.setOutputValue('rotation', entity.transform.rotation);
    } else {
      this.setOutputValue('rotation', { x: 0, y: 0, z: 0, w: 1 });
    }
  }
}

export class SetRotationNode extends BaseFlowNode {
  constructor() {
    super('entity/transform/setRotation', NodeCategory.ENTITY);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addInput({ name: 'rotation', type: SocketType.DATA, dataType: 'quaternion', direction: SocketDirection.INPUT });
  }
  
  protected async processNode(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const rotation = this.getInputValue('rotation') || { x: 0, y: 0, z: 0, w: 1 };
    if (entity && entity.transform) {
      entity.transform.rotation = rotation;
    }
  }
}

// ==================== 物理高级节点 (014-021) ====================

/**
 * 应用冲量节点
 */
export class ApplyImpulseNode extends BaseFlowNode {
  constructor() {
    super('physics/applyImpulse', NodeCategory.PHYSICS);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addInput({ name: 'impulse', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.INPUT });
  }

  protected async processNode(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const impulse = this.getInputValue('impulse') || { x: 0, y: 0, z: 0 };
    if (entity && context.world && context.world.physicsWorld) {
      context.world.physicsWorld.applyImpulse(entity, impulse);
    }
  }
}

/**
 * 设置速度节点
 */
export class SetVelocityNode extends BaseFlowNode {
  constructor() {
    super('physics/setVelocity', NodeCategory.PHYSICS);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addInput({ name: 'velocity', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.INPUT });
  }

  protected async processNode(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const velocity = this.getInputValue('velocity') || { x: 0, y: 0, z: 0 };
    if (entity && context.world && context.world.physicsWorld) {
      context.world.physicsWorld.setVelocity(entity, velocity);
    }
  }
}

/**
 * 获取速度节点
 */
export class GetVelocityNode extends BaseDataNode {
  constructor() {
    super('physics/getVelocity', NodeCategory.PHYSICS);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'velocity', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.OUTPUT });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    if (entity && context.world && context.world.physicsWorld) {
      const velocity = context.world.physicsWorld.getVelocity(entity);
      this.setOutputValue('velocity', velocity);
    } else {
      this.setOutputValue('velocity', { x: 0, y: 0, z: 0 });
    }
  }
}

/**
 * 碰撞进入事件节点
 */
export class OnCollisionEnterNode extends Node {
  constructor() {
    super('physics/collision/onEnter', NodeCategory.EVENT);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'exec', type: SocketType.FLOW, direction: SocketDirection.OUTPUT });
    this.addOutput({ name: 'otherEntity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.OUTPUT });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    if (entity && context.world && context.world.physicsWorld) {
      context.world.physicsWorld.onCollisionEnter(entity, (otherEntity: any) => {
        this.setOutputValue('otherEntity', otherEntity);
        this.executeOutput('exec', context);
      });
    }
  }
}

/**
 * 碰撞退出事件节点
 */
export class OnCollisionExitNode extends Node {
  constructor() {
    super('physics/collision/onExit', NodeCategory.EVENT);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'exec', type: SocketType.FLOW, direction: SocketDirection.OUTPUT });
    this.addOutput({ name: 'otherEntity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.OUTPUT });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    if (entity && context.world && context.world.physicsWorld) {
      context.world.physicsWorld.onCollisionExit(entity, (otherEntity: any) => {
        this.setOutputValue('otherEntity', otherEntity);
        this.executeOutput('exec', context);
      });
    }
  }
}

/**
 * 创建软体节点
 */
export class CreateSoftBodyNode extends BaseFlowNode {
  constructor() {
    super('physics/softbody/createSoftBody', NodeCategory.PHYSICS);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addInput({ name: 'stiffness', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.INPUT, defaultValue: 1.0 });
    this.addOutput({ name: 'softBody', type: SocketType.DATA, dataType: 'softBody', direction: SocketDirection.OUTPUT });
  }

  protected async processNode(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const stiffness = this.getInputValue('stiffness') || 1.0;
    if (entity && context.world && context.world.physicsWorld) {
      const softBody = context.world.physicsWorld.createSoftBody(entity, { stiffness });
      this.setOutputValue('softBody', softBody);
    }
  }
}

/**
 * 设置刚度节点
 */
export class SetStiffnessNode extends BaseFlowNode {
  constructor() {
    super('physics/softbody/setStiffness', NodeCategory.PHYSICS);
    this.addInput({ name: 'softBody', type: SocketType.DATA, dataType: 'softBody', direction: SocketDirection.INPUT });
    this.addInput({ name: 'stiffness', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.INPUT });
  }

  protected async processNode(context: ExecutionContext): Promise<void> {
    const softBody = this.getInputValue('softBody');
    const stiffness = this.getInputValue('stiffness') || 1.0;
    if (softBody && softBody.setStiffness) {
      softBody.setStiffness(stiffness);
    }
  }
}

/**
 * 设置阻尼节点
 */
export class SetDampingNode extends BaseFlowNode {
  constructor() {
    super('physics/softbody/setDamping', NodeCategory.PHYSICS);
    this.addInput({ name: 'softBody', type: SocketType.DATA, dataType: 'softBody', direction: SocketDirection.INPUT });
    this.addInput({ name: 'damping', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.INPUT });
  }

  protected async processNode(context: ExecutionContext): Promise<void> {
    const softBody = this.getInputValue('softBody');
    const damping = this.getInputValue('damping') || 0.1;
    if (softBody && softBody.setDamping) {
      softBody.setDamping(damping);
    }
  }
}

// ==================== 网络通信节点 (022, 037-043) ====================

/**
 * 断开连接节点
 */
export class DisconnectNode extends BaseFlowNode {
  constructor() {
    super('network/disconnect', NodeCategory.NETWORK);
    this.addInput({ name: 'connection', type: SocketType.DATA, dataType: 'connection', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'success', type: SocketType.DATA, dataType: 'boolean', direction: SocketDirection.OUTPUT });
  }

  protected async processNode(context: ExecutionContext): Promise<void> {
    const connection = this.getInputValue('connection');
    let success = false;
    if (connection && connection.disconnect) {
      try {
        await connection.disconnect();
        success = true;
      } catch (error) {
        console.error('断开连接失败:', error);
      }
    }
    this.setOutputValue('success', success);
  }
}

/**
 * 数据哈希节点
 */
export class HashDataNode extends BaseDataNode {
  constructor() {
    super('network/security/hashData', NodeCategory.NETWORK);
    this.addInput({ name: 'data', type: SocketType.DATA, dataType: 'string', direction: SocketDirection.INPUT });
    this.addInput({ name: 'algorithm', type: SocketType.DATA, dataType: 'string', direction: SocketDirection.INPUT, defaultValue: 'sha256' });
    this.addOutput({ name: 'hash', type: SocketType.DATA, dataType: 'string', direction: SocketDirection.OUTPUT });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const data = this.getInputValue('data') || '';
    const algorithm = this.getInputValue('algorithm') || 'sha256';

    // 简单的哈希实现（实际项目中应使用crypto库）
    let hash = '';
    if (data) {
      hash = this.simpleHash(data, algorithm);
    }
    this.setOutputValue('hash', hash);
  }

  private simpleHash(data: string, algorithm: string): string {
    // 简化的哈希实现
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(16);
  }
}

/**
 * 创建数据通道节点
 */
export class CreateDataChannelNode extends BaseFlowNode {
  constructor() {
    super('network/webrtc/createDataChannel', NodeCategory.NETWORK);
    this.addInput({ name: 'connection', type: SocketType.DATA, dataType: 'webrtcConnection', direction: SocketDirection.INPUT });
    this.addInput({ name: 'label', type: SocketType.DATA, dataType: 'string', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'dataChannel', type: SocketType.DATA, dataType: 'dataChannel', direction: SocketDirection.OUTPUT });
  }

  protected async processNode(context: ExecutionContext): Promise<void> {
    const connection = this.getInputValue('connection');
    const label = this.getInputValue('label') || 'dataChannel';

    if (connection && connection.createDataChannel) {
      try {
        const dataChannel = connection.createDataChannel(label);
        this.setOutputValue('dataChannel', dataChannel);
      } catch (error) {
        console.error('创建数据通道失败:', error);
      }
    }
  }
}

/**
 * 关闭WebRTC连接节点
 */
export class CloseWebRTCConnectionNode extends BaseFlowNode {
  constructor() {
    super('network/webrtc/closeConnection', NodeCategory.NETWORK);
    this.addInput({ name: 'connection', type: SocketType.DATA, dataType: 'webrtcConnection', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'success', type: SocketType.DATA, dataType: 'boolean', direction: SocketDirection.OUTPUT });
  }

  protected async processNode(context: ExecutionContext): Promise<void> {
    const connection = this.getInputValue('connection');
    let success = false;

    if (connection && connection.close) {
      try {
        connection.close();
        success = true;
      } catch (error) {
        console.error('关闭WebRTC连接失败:', error);
      }
    }
    this.setOutputValue('success', success);
  }
}

/**
 * TCP连接节点
 */
export class TCPConnectNode extends BaseFlowNode {
  constructor() {
    super('network/protocol/tcpConnect', NodeCategory.NETWORK);
    this.addInput({ name: 'host', type: SocketType.DATA, dataType: 'string', direction: SocketDirection.INPUT });
    this.addInput({ name: 'port', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'connection', type: SocketType.DATA, dataType: 'tcpConnection', direction: SocketDirection.OUTPUT });
    this.addOutput({ name: 'success', type: SocketType.DATA, dataType: 'boolean', direction: SocketDirection.OUTPUT });
  }

  protected async processNode(context: ExecutionContext): Promise<void> {
    const host = this.getInputValue('host') || 'localhost';
    const port = this.getInputValue('port') || 8080;

    try {
      // 模拟TCP连接创建
      const connection = {
        host,
        port,
        connected: true,
        send: (data: any) => console.log('TCP发送:', data),
        close: () => console.log('TCP连接关闭')
      };

      this.setOutputValue('connection', connection);
      this.setOutputValue('success', true);
    } catch (error) {
      console.error('TCP连接失败:', error);
      this.setOutputValue('success', false);
    }
  }
}

// ==================== 时间节点 (023-024) ====================

/**
 * 延迟节点
 */
export class DelayNode extends BaseFlowNode {
  constructor() {
    super('time/delay', NodeCategory.FLOW);
    this.addInput({ name: 'duration', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.INPUT });
  }

  protected async processNode(context: ExecutionContext): Promise<void> {
    const duration = this.getInputValue('duration') || 1.0;

    // 延迟执行不需要在这里调用 executeOutput，因为 BaseFlowNode 会自动处理
    setTimeout(() => {
      // 这里应该触发输出，但需要正确的方法
      console.log(`延迟 ${duration} 秒后执行`);
    }, duration * 1000);
  }
}

/**
 * 计时器节点
 */
export class TimerNode extends Node {
  private intervalId: NodeJS.Timeout | null = null;

  constructor() {
    super('time/timer', NodeCategory.FLOW);

    this.addInput({ name: 'start', type: SocketType.FLOW, direction: SocketDirection.INPUT });
    this.addInput({ name: 'stop', type: SocketType.FLOW, direction: SocketDirection.INPUT });
    this.addInput({ name: 'interval', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'tick', type: SocketType.FLOW, direction: SocketDirection.OUTPUT });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    // 简化实现 - 实际项目中需要更复杂的逻辑
    const interval = this.getInputValue('interval') || 1.0;

    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    this.intervalId = setInterval(() => {
      console.log(`计时器触发，间隔: ${interval}秒`);
      // 这里应该触发输出事件
    }, interval * 1000);
  }
}

// ==================== 动画节点 (025-028) ====================

/**
 * 播放动画节点
 */
export class PlayAnimationNode extends BaseFlowNode {
  constructor() {
    super('animation/playAnimation', NodeCategory.ANIMATION);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addInput({ name: 'animationName', type: SocketType.DATA, dataType: 'string', direction: SocketDirection.INPUT });
  }

  protected async processNode(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const animationName = this.getInputValue('animationName') || '';

    if (entity && entity.animationComponent && animationName) {
      entity.animationComponent.play(animationName);
    }
  }
}

/**
 * 停止动画节点
 */
export class StopAnimationNode extends BaseFlowNode {
  constructor() {
    super('animation/stopAnimation', NodeCategory.ANIMATION);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
  }

  protected async processNode(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');

    if (entity && entity.animationComponent) {
      entity.animationComponent.stop();
    }
  }
}

/**
 * 设置动画速度节点
 */
export class SetAnimationSpeedNode extends BaseFlowNode {
  constructor() {
    super('animation/setAnimationSpeed', NodeCategory.ANIMATION);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addInput({ name: 'speed', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.INPUT });
  }

  protected async processNode(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const speed = this.getInputValue('speed') || 1.0;

    if (entity && entity.animationComponent) {
      entity.animationComponent.setSpeed(speed);
    }
  }
}

/**
 * 获取动画状态节点
 */
export class GetAnimationStateNode extends BaseDataNode {
  constructor() {
    super('animation/getAnimationState', NodeCategory.ANIMATION);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'isPlaying', type: SocketType.DATA, dataType: 'boolean', direction: SocketDirection.OUTPUT });
    this.addOutput({ name: 'currentTime', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.OUTPUT });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');

    if (entity && entity.animationComponent) {
      this.setOutputValue('isPlaying', entity.animationComponent.isPlaying());
      this.setOutputValue('currentTime', entity.animationComponent.getCurrentTime());
    } else {
      this.setOutputValue('isPlaying', false);
      this.setOutputValue('currentTime', 0);
    }
  }
}

// ==================== 输入系统节点 (029-031) ====================

/**
 * 键盘输入节点
 */
export class KeyboardInputNode extends Node {
  constructor() {
    super('input/keyboard', NodeCategory.INPUT);

    this.addInput({ name: 'keyCode', type: SocketType.DATA, dataType: 'string', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'isPressed', type: SocketType.DATA, dataType: 'boolean', direction: SocketDirection.OUTPUT });
    this.addOutput({ name: 'onKeyDown', type: SocketType.FLOW, direction: SocketDirection.OUTPUT });
    this.addOutput({ name: 'onKeyUp', type: SocketType.FLOW, direction: SocketDirection.OUTPUT });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const keyCode = this.getInputValue('keyCode') || '';

    // 模拟键盘输入检测
    const isPressed = this.checkKeyPressed(keyCode);
    this.setOutputValue('isPressed', isPressed);

    // 在实际实现中，这里会监听键盘事件
    console.log(`检查键盘输入: ${keyCode}, 是否按下: ${isPressed}`);
  }

  private checkKeyPressed(keyCode: string): boolean {
    // 简化的键盘状态检测
    // 实际实现中会与输入系统集成
    return Math.random() > 0.5; // 随机返回，仅用于演示
  }
}

/**
 * 鼠标输入节点
 */
export class MouseInputNode extends Node {
  constructor() {
    super('input/mouse', NodeCategory.INPUT);

    this.addInput({ name: 'button', type: SocketType.DATA, dataType: 'string', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'position', type: SocketType.DATA, dataType: 'vector2', direction: SocketDirection.OUTPUT });
    this.addOutput({ name: 'isPressed', type: SocketType.DATA, dataType: 'boolean', direction: SocketDirection.OUTPUT });
    this.addOutput({ name: 'onClick', type: SocketType.FLOW, direction: SocketDirection.OUTPUT });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const button = this.getInputValue('button') || 'left';

    // 模拟鼠标状态
    const position = { x: Math.random() * 800, y: Math.random() * 600 };
    const isPressed = Math.random() > 0.7;

    this.setOutputValue('position', position);
    this.setOutputValue('isPressed', isPressed);

    console.log(`鼠标状态 - 按钮: ${button}, 位置: (${position.x}, ${position.y}), 是否按下: ${isPressed}`);
  }
}

/**
 * 游戏手柄输入节点
 */
export class GamepadInputNode extends Node {
  constructor() {
    super('input/gamepad', NodeCategory.INPUT);

    this.addInput({ name: 'gamepadIndex', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.INPUT });
    this.addInput({ name: 'button', type: SocketType.DATA, dataType: 'string', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'isPressed', type: SocketType.DATA, dataType: 'boolean', direction: SocketDirection.OUTPUT });
    this.addOutput({ name: 'leftStick', type: SocketType.DATA, dataType: 'vector2', direction: SocketDirection.OUTPUT });
    this.addOutput({ name: 'rightStick', type: SocketType.DATA, dataType: 'vector2', direction: SocketDirection.OUTPUT });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const gamepadIndex = this.getInputValue('gamepadIndex') || 0;
    const button = this.getInputValue('button') || 'A';

    // 模拟游戏手柄状态
    const isPressed = Math.random() > 0.8;
    const leftStick = { x: (Math.random() - 0.5) * 2, y: (Math.random() - 0.5) * 2 };
    const rightStick = { x: (Math.random() - 0.5) * 2, y: (Math.random() - 0.5) * 2 };

    this.setOutputValue('isPressed', isPressed);
    this.setOutputValue('leftStick', leftStick);
    this.setOutputValue('rightStick', rightStick);

    console.log(`游戏手柄 ${gamepadIndex} - 按钮 ${button}: ${isPressed ? '按下' : '释放'}`);
  }
}
