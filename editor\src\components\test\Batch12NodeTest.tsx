/**
 * 第12批次节点测试组件
 * 验证服务器集成扩展节点（331-350）的拖拽创建和基本功能
 */

import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Typography, Tag, Alert, List, Tabs } from 'antd';
import { nodeRegistryService } from '../../services/NodeRegistryService';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

interface NodeTestResult {
  type: string;
  label: string;
  registered: boolean;
  category: string;
  tags: string[];
}

export const Batch12NodeTest: React.FC = () => {
  const [testResults, setTestResults] = useState<NodeTestResult[]>([]);
  const [loading, setLoading] = useState(false);

  // 第12批次节点列表
  const batch12Nodes = {
    assetExtension: [
      { type: 'server/asset/updateAssetInfo', label: '更新资产信息', id: 331 },
      { type: 'server/asset/moveAssetToFolder', label: '移动资产到文件夹', id: 332 },
      { type: 'server/asset/createAssetFolder', label: '创建资产文件夹', id: 333 },
      { type: 'server/asset/deleteAssetFolder', label: '删除资产文件夹', id: 334 },
      { type: 'server/asset/shareAsset', label: '分享资产', id: 335 },
      { type: 'server/asset/getAssetVersions', label: '获取资产版本', id: 336 },
      { type: 'server/asset/createAssetVersion', label: '创建资产版本', id: 337 },
      { type: 'server/asset/restoreAssetVersion', label: '恢复资产版本', id: 338 },
      { type: 'server/asset/generateAssetThumbnail', label: '生成资产缩略图', id: 339 },
      { type: 'server/asset/optimizeAsset', label: '优化资产', id: 340 }
    ],
    collaboration: [
      { type: 'server/collaboration/joinRoom', label: '加入协作房间', id: 341 },
      { type: 'server/collaboration/leaveRoom', label: '离开协作房间', id: 342 },
      { type: 'server/collaboration/sendOperation', label: '发送协作操作', id: 343 },
      { type: 'server/collaboration/receiveOperation', label: '接收协作操作', id: 344 },
      { type: 'server/collaboration/resolveConflict', label: '解决编辑冲突', id: 345 },
      { type: 'server/collaboration/getOnlineUsers', label: '获取在线用户', id: 346 },
      { type: 'server/collaboration/broadcastMessage', label: '广播消息', id: 347 },
      { type: 'server/collaboration/lockResource', label: '锁定资源', id: 348 },
      { type: 'server/collaboration/unlockResource', label: '解锁资源', id: 349 },
      { type: 'server/collaboration/syncState', label: '同步状态', id: 350 }
    ]
  };

  // 测试节点注册状态
  const testNodeRegistration = () => {
    setLoading(true);
    const results: NodeTestResult[] = [];

    // 测试资产扩展节点
    batch12Nodes.assetExtension.forEach(node => {
      const nodeInfo = nodeRegistryService.getNode(node.type);
      results.push({
        type: node.type,
        label: node.label,
        registered: !!nodeInfo,
        category: nodeInfo?.category || 'UNKNOWN',
        tags: nodeInfo?.tags || []
      });
    });

    // 测试协作服务节点
    batch12Nodes.collaboration.forEach(node => {
      const nodeInfo = nodeRegistryService.getNode(node.type);
      results.push({
        type: node.type,
        label: node.label,
        registered: !!nodeInfo,
        category: nodeInfo?.category || 'UNKNOWN',
        tags: nodeInfo?.tags || []
      });
    });

    setTestResults(results);
    setLoading(false);
  };

  // 组件挂载时自动测试
  useEffect(() => {
    testNodeRegistration();
  }, []);

  // 计算统计信息
  const stats = {
    total: testResults.length,
    registered: testResults.filter(r => r.registered).length,
    assetExtension: testResults.slice(0, 10).filter(r => r.registered).length,
    collaboration: testResults.slice(10, 20).filter(r => r.registered).length
  };

  const renderNodeList = (nodes: typeof batch12Nodes.assetExtension, startIndex: number) => (
    <List
      size="small"
      dataSource={nodes}
      renderItem={(node, index) => {
        const result = testResults[startIndex + index];
        return (
          <List.Item>
            <Space>
              <Text strong>{node.id}.</Text>
              <Text>{node.label}</Text>
              {result?.registered ? (
                <Tag color="green">已注册</Tag>
              ) : (
                <Tag color="red">未注册</Tag>
              )}
              {result?.registered && (
                <Tag color="blue">{result.category}</Tag>
              )}
            </Space>
          </List.Item>
        );
      }}
    />
  );

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>第12批次节点测试</Title>
      <Paragraph>
        测试服务器集成扩展节点（331-350）在编辑器中的注册状态和拖拽功能。
      </Paragraph>

      {/* 统计信息 */}
      <Card title="测试统计" style={{ marginBottom: '16px' }}>
        <Space size="large">
          <div>
            <Text strong>总节点数: </Text>
            <Text>{stats.total}</Text>
          </div>
          <div>
            <Text strong>已注册: </Text>
            <Text style={{ color: stats.registered === stats.total ? '#52c41a' : '#ff4d4f' }}>
              {stats.registered}/{stats.total}
            </Text>
          </div>
          <div>
            <Text strong>资产扩展: </Text>
            <Text>{stats.assetExtension}/10</Text>
          </div>
          <div>
            <Text strong>协作服务: </Text>
            <Text>{stats.collaboration}/10</Text>
          </div>
        </Space>
      </Card>

      {/* 测试结果 */}
      {stats.registered === stats.total ? (
        <Alert
          message="测试通过"
          description="第12批次所有20个节点都已成功注册到编辑器，可以通过拖拽方式创建。"
          type="success"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      ) : (
        <Alert
          message="测试失败"
          description={`还有 ${stats.total - stats.registered} 个节点未注册，请检查节点注册配置。`}
          type="error"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      )}

      {/* 节点详情 */}
      <Card title="节点详情">
        <Tabs defaultActiveKey="assetExtension">
          <TabPane tab={`资产服务扩展 (${stats.assetExtension}/10)`} key="assetExtension">
            <Paragraph>
              资产服务扩展节点提供了完整的资产管理功能，包括资产信息更新、文件夹管理、
              版本控制、分享功能、缩略图生成和资产优化等。
            </Paragraph>
            {renderNodeList(batch12Nodes.assetExtension, 0)}
          </TabPane>
          
          <TabPane tab={`协作服务 (${stats.collaboration}/10)`} key="collaboration">
            <Paragraph>
              协作服务节点提供了实时协作功能，包括房间管理、操作同步、冲突解决、
              用户管理、消息广播、资源锁定和状态同步等。
            </Paragraph>
            {renderNodeList(batch12Nodes.collaboration, 10)}
          </TabPane>
        </Tabs>
      </Card>

      {/* 操作按钮 */}
      <div style={{ marginTop: '16px' }}>
        <Space>
          <Button type="primary" loading={loading} onClick={testNodeRegistration}>
            重新测试
          </Button>
          <Button 
            onClick={() => {
              console.log('第12批次节点测试结果:', testResults);
            }}
          >
            查看详细日志
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default Batch12NodeTest;
