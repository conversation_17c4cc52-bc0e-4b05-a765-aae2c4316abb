/**
 * 完整修复第8批次节点文件中的TypeScript错误
 */

const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const files = [
  'engine/src/visualscript/presets/AudioParticleNodes.ts',
  'engine/src/visualscript/presets/AudioParticleNodes2.ts',
  'engine/src/visualscript/presets/AudioParticleNodes3.ts',
  'engine/src/visualscript/presets/AudioParticleNodes4.ts'
];

// 修复函数
function fixNodeFile(filePath) {
  console.log(`正在修复文件: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 1. 修复构造函数 - 改为使用 constructor(options: any) 和 initializeSockets()
  content = content.replace(
    /export class (\w+) extends Node \{\s*constructor\(\) \{\s*super\(\{\s*type: '([^']+)',\s*category: NodeCategory\.(\w+)\s*\}\);\s*((?:\s*this\.addInput\(\{[^}]+\}\);\s*)*)((?:\s*this\.addOutput\(\{[^}]+\}\);\s*)*)\s*\}/gms,
    function(match, className, nodeType, category, inputs, outputs) {
      // 添加执行输入和完成输出
      let executeInput = `    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

`;
      
      let completedOutput = `    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

`;
      
      return `export class ${className} extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
${executeInput}${inputs}${completedOutput}${outputs}  }`;
    }
  );
  
  // 2. 修复 dataType 属性位置
  content = content.replace(
    /type: SocketType\.DATA,(\s+direction: SocketDirection\.\w+,\s+description: '[^']+',)\s*dataType: 'number',(\s+defaultValue: [^}]+)/g,
    "type: SocketType.DATA,$1$2"
  );
  
  content = content.replace(
    /type: SocketType\.DATA,(\s+direction: SocketDirection\.\w+,\s+description: '[^']+',)\s*dataType: 'string',(\s+defaultValue: [^}]+)/g,
    "type: SocketType.DATA,$1$2"
  );
  
  // 3. 移除多余的 dataType 行
  content = content.replace(/\s*dataType: '[^']+',/g, '');
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`文件修复完成: ${filePath}`);
}

// 执行修复
files.forEach(fixNodeFile);

console.log('所有文件修复完成！');
