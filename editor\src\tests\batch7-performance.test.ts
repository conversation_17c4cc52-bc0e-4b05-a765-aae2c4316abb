/**
 * 第7批次节点性能基准测试
 * 测试动画系统扩展节点的执行性能
 */

import { nodeRegistryService } from '../services/NodeRegistryService';
import { performance } from 'perf_hooks';

describe('第7批次节点性能测试', () => {
  const PERFORMANCE_THRESHOLD = {
    NODE_CREATION: 10, // ms
    NODE_EXECUTION: 5, // ms
    BATCH_OPERATION: 100, // ms
    MEMORY_LEAK: 1000 // iterations
  };

  beforeAll(() => {
    // 确保节点注册服务已初始化
    expect(nodeRegistryService).toBeDefined();
  });

  describe('节点创建性能', () => {
    test('动画节点创建应在性能阈值内', () => {
      const animationNodeTypes = [
        'animation/curve/evaluateCurve',
        'animation/state/createStateMachine',
        'animation/state/addState',
        'animation/state/addTransition',
        'animation/state/setCurrentState'
      ];

      animationNodeTypes.forEach(nodeType => {
        const startTime = performance.now();
        
        const nodeConfig = nodeRegistryService.getNode(nodeType);
        expect(nodeConfig).toBeDefined();
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD.NODE_CREATION);
      });
    });

    test('音频节点创建应在性能阈值内', () => {
      const audioNodeTypes = [
        'audio/source/create3DAudioSource',
        'audio/effect/createReverb',
        'audio/analysis/createAnalyzer',
        'audio/recording/startRecording'
      ];

      audioNodeTypes.forEach(nodeType => {
        const startTime = performance.now();
        
        const nodeConfig = nodeRegistryService.getNode(nodeType);
        expect(nodeConfig).toBeDefined();
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD.NODE_CREATION);
      });
    });

    test('场景节点创建应在性能阈值内', () => {
      const sceneNodeTypes = [
        'scene/management/createScene',
        'scene/management/loadScene',
        'scene/culling/enableFrustumCulling',
        'scene/optimization/enableInstancing'
      ];

      sceneNodeTypes.forEach(nodeType => {
        const startTime = performance.now();
        
        const nodeConfig = nodeRegistryService.getNode(nodeType);
        expect(nodeConfig).toBeDefined();
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD.NODE_CREATION);
      });
    });
  });

  describe('批量操作性能', () => {
    test('批量获取第7批次节点应在性能阈值内', () => {
      const startTime = performance.now();
      
      const allNodes = nodeRegistryService.getAllNodes();
      const batch7Nodes = allNodes.filter(node => 
        node.type.startsWith('animation/curve/') ||
        node.type.startsWith('animation/state/') ||
        node.type.startsWith('audio/') ||
        node.type.startsWith('scene/')
      );
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(batch7Nodes.length).toBeGreaterThanOrEqual(30);
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD.BATCH_OPERATION);
    });

    test('批量标签搜索应在性能阈值内', () => {
      const searchTerms = ['动画', '音频', '场景', '优化', '效果'];
      
      const startTime = performance.now();
      
      searchTerms.forEach(term => {
        const results = nodeRegistryService.getNodesByTag(term);
        expect(results.length).toBeGreaterThan(0);
      });
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD.BATCH_OPERATION);
    });

    test('批量类型查询应在性能阈值内', () => {
      const nodeTypes = [
        'animation/curve/evaluateCurve',
        'audio/source/create3DAudioSource',
        'scene/management/createScene',
        'audio/effect/createReverb',
        'scene/optimization/enableInstancing'
      ];
      
      const startTime = performance.now();
      
      nodeTypes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
      });
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD.BATCH_OPERATION);
    });
  });

  describe('内存使用测试', () => {
    test('重复节点查询不应造成内存泄漏', () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // 执行大量节点查询操作
      for (let i = 0; i < PERFORMANCE_THRESHOLD.MEMORY_LEAK; i++) {
        nodeRegistryService.getNode('animation/curve/evaluateCurve');
        nodeRegistryService.getNodesByTag('动画');
        nodeRegistryService.getAllNodes();
        
        // 每100次迭代检查一次内存
        if (i % 100 === 0) {
          const currentMemory = process.memoryUsage().heapUsed;
          const memoryIncrease = currentMemory - initialMemory;
          
          // 内存增长不应超过10MB
          expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
        }
      }
      
      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const totalMemoryIncrease = finalMemory - initialMemory;
      
      // 最终内存增长不应超过5MB
      expect(totalMemoryIncrease).toBeLessThan(5 * 1024 * 1024);
    });

    test('节点配置对象应该被正确缓存', () => {
      const nodeType = 'animation/curve/evaluateCurve';
      
      // 多次获取同一节点配置
      const node1 = nodeRegistryService.getNode(nodeType);
      const node2 = nodeRegistryService.getNode(nodeType);
      const node3 = nodeRegistryService.getNode(nodeType);
      
      // 应该返回相同的对象引用（缓存）
      expect(node1).toBe(node2);
      expect(node2).toBe(node3);
    });
  });

  describe('并发性能测试', () => {
    test('并发节点查询应保持性能', async () => {
      const concurrentQueries = 50;
      const nodeTypes = [
        'animation/curve/evaluateCurve',
        'audio/source/create3DAudioSource',
        'scene/management/createScene'
      ];
      
      const startTime = performance.now();
      
      const promises = Array.from({ length: concurrentQueries }, (_, index) => {
        return new Promise<void>((resolve) => {
          const nodeType = nodeTypes[index % nodeTypes.length];
          const node = nodeRegistryService.getNode(nodeType);
          expect(node).toBeDefined();
          resolve();
        });
      });
      
      await Promise.all(promises);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // 并发查询总时间不应超过阈值
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD.BATCH_OPERATION);
    });

    test('并发标签搜索应保持性能', async () => {
      const concurrentSearches = 30;
      const searchTerms = ['动画', '音频', '场景', '优化', '效果'];
      
      const startTime = performance.now();
      
      const promises = Array.from({ length: concurrentSearches }, (_, index) => {
        return new Promise<void>((resolve) => {
          const term = searchTerms[index % searchTerms.length];
          const results = nodeRegistryService.getNodesByTag(term);
          expect(results.length).toBeGreaterThan(0);
          resolve();
        });
      });
      
      await Promise.all(promises);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // 并发搜索总时间不应超过阈值
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD.BATCH_OPERATION);
    });
  });

  describe('大数据量性能测试', () => {
    test('大量节点过滤应保持性能', () => {
      const startTime = performance.now();
      
      const allNodes = nodeRegistryService.getAllNodes();
      
      // 执行多种过滤操作
      const animationNodes = allNodes.filter(node => node.category === 'animation');
      const audioNodes = allNodes.filter(node => node.category === 'audio');
      const entityNodes = allNodes.filter(node => node.category === 'entity');
      
      const batch7Nodes = allNodes.filter(node => 
        node.type.startsWith('animation/') ||
        node.type.startsWith('audio/') ||
        node.type.startsWith('scene/')
      );
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(animationNodes.length).toBeGreaterThan(0);
      expect(audioNodes.length).toBeGreaterThan(0);
      expect(entityNodes.length).toBeGreaterThan(0);
      expect(batch7Nodes.length).toBeGreaterThanOrEqual(30);
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD.BATCH_OPERATION);
    });

    test('复杂查询组合应保持性能', () => {
      const startTime = performance.now();
      
      // 执行复杂的查询组合
      const complexQuery1 = nodeRegistryService.getAllNodes()
        .filter(node => node.category === 'animation')
        .filter(node => node.tags.includes('动画'))
        .filter(node => node.type.includes('state'));

      const complexQuery2 = nodeRegistryService.getAllNodes()
        .filter(node => node.category === 'audio')
        .filter(node => node.tags.includes('音频'))
        .filter(node => node.type.includes('effect'));

      const complexQuery3 = nodeRegistryService.getAllNodes()
        .filter(node => node.category === 'entity')
        .filter(node => node.tags.includes('场景'))
        .filter(node => node.type.includes('optimization'));
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(complexQuery1.length).toBeGreaterThan(0);
      expect(complexQuery2.length).toBeGreaterThan(0);
      expect(complexQuery3.length).toBeGreaterThan(0);
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD.BATCH_OPERATION);
    });
  });

  describe('性能回归测试', () => {
    test('第7批次节点不应影响现有节点性能', () => {
      // 测试现有节点的查询性能
      const existingNodeTypes = [
        'math/basic/add',
        'logic/comparison/equals',
        'entity/transform/setPosition'
      ];
      
      const startTime = performance.now();
      
      existingNodeTypes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        // 如果节点存在，验证查询性能
        if (node) {
          expect(node.type).toBe(nodeType);
        }
      });
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // 现有节点查询性能不应受影响
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD.NODE_CREATION);
    });
  });
});
