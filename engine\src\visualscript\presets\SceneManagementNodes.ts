/**
 * 场景管理系统节点（201-210）
 * 包含场景创建、加载、保存、优化等功能
 */

import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Node } from '../nodes/Node';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// 场景管理系统节点（201-210）
// ============================================================================

/**
 * 创建场景节点 (201)
 * 创建新的3D场景
 */
export class CreateSceneNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '场景名称',
      defaultValue: '新场景'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '场景'
    });
  }

  public execute(): any {
    const name = this.getInputValue('name');

    try {
      // 创建新场景
      const scene = new THREE.Scene();
      scene.name = name;

      // 添加基础环境
      scene.background = new THREE.Color(0x87CEEB); // 天空蓝
      scene.fog = new THREE.Fog(0x87CEEB, 1, 1000);

      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateSceneNode: 创建场景失败', error);
      this.setOutputValue('scene', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 加载场景节点 (202)
 * 从文件加载场景
 */
export class LoadSceneNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'filePath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '文件路径',
      defaultValue: ''
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '场景'
    });
  }

  public execute(): any {
    const filePath = this.getInputValue('filePath');

    if (!filePath) {
      console.warn('LoadSceneNode: 文件路径为空');
      this.setOutputValue('scene', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 加载场景文件
      const loader = new THREE.ObjectLoader();
      loader.load(filePath, (scene) => {
        this.setOutputValue('scene', scene);
        this.triggerFlow('completed');
      }, undefined, (error) => {
        console.error('LoadSceneNode: 加载场景失败', error);
        this.setOutputValue('scene', null);
        this.triggerFlow('completed');
      });
    } catch (error) {
      console.error('LoadSceneNode: 加载场景失败', error);
      this.setOutputValue('scene', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 保存场景节点 (203)
 * 保存场景到文件
 */
export class SaveSceneNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '场景',
      defaultValue: null
    });
    this.addInput({
      name: 'filePath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '文件路径',
      defaultValue: ''
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '成功'
    });
  }

  public execute(): any {
    const scene = this.getInputValue('scene');
    const filePath = this.getInputValue('filePath');

    if (!scene || !filePath) {
      console.warn('SaveSceneNode: 场景或文件路径为空');
      this.setOutputValue('success', false);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 序列化场景
      const sceneData = scene.toJSON();
      const jsonString = JSON.stringify(sceneData, null, 2);

      // 保存到文件（在浏览器环境中，这通常需要用户交互）
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = filePath;
      a.click();
      
      URL.revokeObjectURL(url);

      this.setOutputValue('success', true);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SaveSceneNode: 保存场景失败', error);
      this.setOutputValue('success', false);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 切换场景节点 (204)
 * 切换到指定场景
 */
export class SwitchSceneNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '渲染器',
      defaultValue: null
    });
    this.addInput({
      name: 'newScene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '新场景',
      defaultValue: null
    });
    this.addInput({
      name: 'camera',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '相机',
      defaultValue: null
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
  }

  public execute(): any {
    const renderer = this.getInputValue('renderer');
    const newScene = this.getInputValue('newScene');
    const camera = this.getInputValue('camera');

    if (!renderer || !newScene || !camera) {
      console.warn('SwitchSceneNode: 渲染器、场景或相机为空');
      this.triggerFlow('completed');
      return;
    }

    try {
      // 切换场景
      renderer.render(newScene, camera);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SwitchSceneNode: 切换场景失败', error);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 添加到场景节点 (205)
 * 将对象添加到场景
 */
export class AddToSceneNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '场景',
      defaultValue: null
    });
    this.addInput({
      name: 'object',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '对象',
      defaultValue: null
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
  }

  public execute(): any {
    const scene = this.getInputValue('scene');
    const object = this.getInputValue('object');

    if (!scene || !object) {
      console.warn('AddToSceneNode: 场景或对象为空');
      this.triggerFlow('completed');
      return;
    }

    try {
      // 添加对象到场景
      scene.add(object);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('AddToSceneNode: 添加对象到场景失败', error);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 从场景移除节点 (206)
 * 从场景移除对象
 */
export class RemoveFromSceneNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '场景',
      defaultValue: null
    });
    this.addInput({
      name: 'object',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '对象',
      defaultValue: null
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
  }

  public execute(): any {
    const scene = this.getInputValue('scene');
    const object = this.getInputValue('object');

    if (!scene || !object) {
      console.warn('RemoveFromSceneNode: 场景或对象为空');
      this.triggerFlow('completed');
      return;
    }

    try {
      // 从场景移除对象
      scene.remove(object);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('RemoveFromSceneNode: 从场景移除对象失败', error);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 启用视锥体剔除节点 (207)
 * 启用视锥体剔除优化
 */
export class EnableFrustumCullingNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '渲染器',
      defaultValue: null
    });
    this.addInput({
      name: 'enabled',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '启用',
      defaultValue: true
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
  }

  public execute(): any {
    const renderer = this.getInputValue('renderer');
    const enabled = this.getInputValue('enabled');

    if (!renderer) {
      console.warn('EnableFrustumCullingNode: 渲染器为空');
      this.triggerFlow('completed');
      return;
    }

    try {
      // 启用视锥体剔除
      if (renderer.setFrustumCulling) {
        renderer.setFrustumCulling(enabled);
      }
      this.triggerFlow('completed');
    } catch (error) {
      console.error('EnableFrustumCullingNode: 启用视锥体剔除失败', error);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 启用遮挡剔除节点 (208)
 * 启用遮挡剔除优化
 */
export class EnableOcclusionCullingNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '渲染器',
      defaultValue: null
    });
    this.addInput({
      name: 'enabled',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '启用',
      defaultValue: true
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
  }

  public execute(): any {
    const renderer = this.getInputValue('renderer');
    const enabled = this.getInputValue('enabled');

    if (!renderer) {
      console.warn('EnableOcclusionCullingNode: 渲染器为空');
      this.triggerFlow('completed');
      return;
    }

    try {
      // 启用遮挡剔除
      if (renderer.setOcclusionCulling) {
        renderer.setOcclusionCulling(enabled);
      }
      this.triggerFlow('completed');
    } catch (error) {
      console.error('EnableOcclusionCullingNode: 启用遮挡剔除失败', error);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 启用批处理节点 (209)
 * 启用渲染批处理
 */
export class EnableBatchingNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '渲染器',
      defaultValue: null
    });
    this.addInput({
      name: 'enabled',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '启用',
      defaultValue: true
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
  }

  public execute(): any {
    const renderer = this.getInputValue('renderer');
    const enabled = this.getInputValue('enabled');

    if (!renderer) {
      console.warn('EnableBatchingNode: 渲染器为空');
      this.triggerFlow('completed');
      return;
    }

    try {
      // 启用批处理
      if (renderer.setBatching) {
        renderer.setBatching(enabled);
      }
      this.triggerFlow('completed');
    } catch (error) {
      console.error('EnableBatchingNode: 启用批处理失败', error);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 启用实例化节点 (210)
 * 启用实例化渲染
 */
export class EnableInstancingNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'mesh',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '网格',
      defaultValue: null
    });
    this.addInput({
      name: 'count',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '实例数量',
      defaultValue: 100
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'instancedMesh',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '实例化网格'
    });
  }

  public execute(): any {
    const mesh = this.getInputValue('mesh');
    const count = this.getInputValue('count');

    if (!mesh) {
      console.warn('EnableInstancingNode: 网格为空');
      this.setOutputValue('instancedMesh', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 创建实例化网格
      const instancedMesh = new THREE.InstancedMesh(mesh.geometry, mesh.material, count);

      // 设置随机变换矩阵
      const matrix = new THREE.Matrix4();
      for (let i = 0; i < count; i++) {
        matrix.setPosition(
          Math.random() * 100 - 50,
          Math.random() * 100 - 50,
          Math.random() * 100 - 50
        );
        instancedMesh.setMatrixAt(i, matrix);
      }
      instancedMesh.instanceMatrix.needsUpdate = true;

      this.setOutputValue('instancedMesh', instancedMesh);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('EnableInstancingNode: 启用实例化失败', error);
      this.setOutputValue('instancedMesh', null);
      this.triggerFlow('completed');
    }
  }
}
