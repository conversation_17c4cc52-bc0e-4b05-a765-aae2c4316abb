/**
 * 错误修复验证测试
 * 验证所有修复的错误是否正确工作
 */
import { NodeRegistry } from '../visualscript/nodes/NodeRegistry';
import { Node } from '../visualscript/nodes/Node';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';
import { FontLoader } from 'three/examples/jsm/loaders/FontLoader.js';

describe('错误修复验证测试', () => {
  describe('Three.js 加载器导入', () => {
    test('应该能够正确导入 GLTFLoader', () => {
      expect(GLTFLoader).toBeDefined();
      expect(typeof GLTFLoader).toBe('function');
    });

    test('应该能够正确导入 FBXLoader', () => {
      expect(FBXLoader).toBeDefined();
      expect(typeof FBXLoader).toBe('function');
    });

    test('应该能够正确导入 OBJLoader', () => {
      expect(OBJLoader).toBeDefined();
      expect(typeof OBJLoader).toBe('function');
    });

    test('应该能够正确导入 FontLoader', () => {
      expect(FontLoader).toBeDefined();
      expect(typeof FontLoader).toBe('function');
    });
  });

  describe('NodeRegistry API', () => {
    let registry: NodeRegistry;

    beforeEach(() => {
      registry = new NodeRegistry();
    });

    test('应该有 createNode 方法', () => {
      expect(registry.createNode).toBeDefined();
      expect(typeof registry.createNode).toBe('function');
    });

    test('createNode 应该能处理不存在的节点类型', () => {
      const result = registry.createNode('nonexistent/node', {});
      expect(result).toBeUndefined();
    });

    test('应该能够注册和创建节点', () => {
      // 创建一个简单的测试节点类
      class TestNode extends Node {
        constructor(options: any) {
          super(options);
        }
        
        protected initializeSockets(): void {
          // 简单的测试实现
        }
        
        public execute(): void {
          // 简单的测试实现
        }
      }

      // 注册节点类型
      const registered = registry.registerNodeType({
        type: 'test/node',
        category: 'CUSTOM' as any,
        constructor: TestNode,
        label: '测试节点',
        description: '用于测试的节点'
      });

      expect(registered).toBe(true);

      // 创建节点实例
      const mockOptions = {
        id: 'test-node-1',
        type: 'test/node',
        graph: {} as any,
        context: {} as any
      };

      const node = registry.createNode('test/node', mockOptions);
      expect(node).toBeDefined();
      expect(node).toBeInstanceOf(TestNode);
    });
  });

  describe('Node setInputValue 方法', () => {
    test('Node 类应该有 setInputValue 方法', () => {
      // 创建一个简单的测试节点
      class TestNode extends Node {
        constructor(options: any) {
          super(options);
        }
        
        protected initializeSockets(): void {
          this.addInput({
            name: 'testInput',
            type: 'DATA' as any,
            direction: 'INPUT' as any,
            description: '测试输入'
          });
        }
        
        public execute(): void {
          // 简单的测试实现
        }
      }

      const mockOptions = {
        id: 'test-node',
        type: 'test/node',
        graph: {} as any,
        context: {} as any
      };

      const node = new TestNode(mockOptions);
      
      // 验证 setInputValue 方法存在
      expect(node.setInputValue).toBeDefined();
      expect(typeof node.setInputValue).toBe('function');
      
      // 验证 getInputValue 方法存在
      expect(node.getInputValue).toBeDefined();
      expect(typeof node.getInputValue).toBe('function');
    });
  });

  describe('加载器实例化', () => {
    test('应该能够创建 Three.js 加载器实例', () => {
      expect(() => new GLTFLoader()).not.toThrow();
      expect(() => new FBXLoader()).not.toThrow();
      expect(() => new OBJLoader()).not.toThrow();
      expect(() => new FontLoader()).not.toThrow();
    });
  });
});
