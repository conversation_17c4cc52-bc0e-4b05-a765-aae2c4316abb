/**
 * 第8批次音频与粒子系统节点统一导出
 * 节点211-240：完整的30个节点实现
 */

// 高级音频节点 (211-215)
export {
  SetSkyboxNode,
  EnableFogNode,
  SetFogColorNode,
  SetFogDensityNode,
  SetEnvironmentMapNode
} from './AudioParticleNodesBatch8';

// 粒子系统节点 (216-221)
export {
  CreateParticleSystemNode,
  CreateEmitterNode,
  SetEmissionRateNode,
  SetEmissionShapeNode,
  SetLifetimeNode,
  SetVelocityNode
} from './AudioParticleNodesBatch8';

// 粒子属性和物理节点 (222-230)
export {
  SetSizeNode,
  SetColorNode,
  AddGravityNode,
  AddWindNode,
  AddTurbulenceNode,
  EnableCollisionNode,
  SetParticleMaterialNode,
  AnimateSizeNode,
  AnimateColorNode
} from './AudioParticleNodesBatch8Part2';

// 地形系统节点 (231-237)
export {
  CreateTerrainNode,
  GenerateHeightmapNode,
  ApplyNoiseNode,
  SetTerrainTextureNode,
  BlendTexturesNode,
  EnableTerrainLODNode,
  EnableTerrainCollisionNode
} from './AudioParticleNodesBatch8Part3';

// 水体系统节点 (238-240)
export {
  CreateWaterSurfaceNode,
  AddWavesNode,
  EnableReflectionNode
} from './AudioParticleNodesBatch8Part3';

/**
 * 第8批次节点映射表
 * 用于节点注册和类型检查
 */
export const BATCH8_NODE_MAPPING = {
  // 高级音频节点 (211-215)
  'scene/skybox/setSkybox': 'SetSkyboxNode',
  'scene/fog/enableFog': 'EnableFogNode',
  'scene/fog/setFogColor': 'SetFogColorNode',
  'scene/fog/setFogDensity': 'SetFogDensityNode',
  'scene/environment/setEnvironmentMap': 'SetEnvironmentMapNode',
  
  // 粒子系统节点 (216-230)
  'particles/system/createParticleSystem': 'CreateParticleSystemNode',
  'particles/emitter/createEmitter': 'CreateEmitterNode',
  'particles/emitter/setEmissionRate': 'SetEmissionRateNode',
  'particles/emitter/setEmissionShape': 'SetEmissionShapeNode',
  'particles/particle/setLifetime': 'SetLifetimeNode',
  'particles/particle/setVelocity': 'SetVelocityNode',
  'particles/particle/setSize': 'SetSizeNode',
  'particles/particle/setColor': 'SetColorNode',
  'particles/forces/addGravity': 'AddGravityNode',
  'particles/forces/addWind': 'AddWindNode',
  'particles/forces/addTurbulence': 'AddTurbulenceNode',
  'particles/collision/enableCollision': 'EnableCollisionNode',
  'particles/material/setParticleMaterial': 'SetParticleMaterialNode',
  'particles/animation/animateSize': 'AnimateSizeNode',
  'particles/animation/animateColor': 'AnimateColorNode',
  
  // 地形系统节点 (231-237)
  'terrain/generation/createTerrain': 'CreateTerrainNode',
  'terrain/generation/generateHeightmap': 'GenerateHeightmapNode',
  'terrain/generation/applyNoise': 'ApplyNoiseNode',
  'terrain/texture/setTerrainTexture': 'SetTerrainTextureNode',
  'terrain/texture/blendTextures': 'BlendTexturesNode',
  'terrain/lod/enableTerrainLOD': 'EnableTerrainLODNode',
  'terrain/collision/enableTerrainCollision': 'EnableTerrainCollisionNode',
  
  // 水体系统节点 (238-240)
  'water/system/createWaterSurface': 'CreateWaterSurfaceNode',
  'water/waves/addWaves': 'AddWavesNode',
  'water/reflection/enableReflection': 'EnableReflectionNode'
};

/**
 * 第8批次节点类别分组
 */
export const BATCH8_NODE_CATEGORIES = {
  AUDIO_ENVIRONMENT: [
    'scene/skybox/setSkybox',
    'scene/fog/enableFog',
    'scene/fog/setFogColor',
    'scene/fog/setFogDensity',
    'scene/environment/setEnvironmentMap'
  ],
  PARTICLE_SYSTEM: [
    'particles/system/createParticleSystem',
    'particles/emitter/createEmitter',
    'particles/emitter/setEmissionRate',
    'particles/emitter/setEmissionShape'
  ],
  PARTICLE_PROPERTIES: [
    'particles/particle/setLifetime',
    'particles/particle/setVelocity',
    'particles/particle/setSize',
    'particles/particle/setColor'
  ],
  PARTICLE_PHYSICS: [
    'particles/forces/addGravity',
    'particles/forces/addWind',
    'particles/forces/addTurbulence',
    'particles/collision/enableCollision'
  ],
  PARTICLE_MATERIAL: [
    'particles/material/setParticleMaterial',
    'particles/animation/animateSize',
    'particles/animation/animateColor'
  ],
  TERRAIN_GENERATION: [
    'terrain/generation/createTerrain',
    'terrain/generation/generateHeightmap',
    'terrain/generation/applyNoise'
  ],
  TERRAIN_TEXTURE: [
    'terrain/texture/setTerrainTexture',
    'terrain/texture/blendTextures'
  ],
  TERRAIN_OPTIMIZATION: [
    'terrain/lod/enableTerrainLOD',
    'terrain/collision/enableTerrainCollision'
  ],
  WATER_SYSTEM: [
    'water/system/createWaterSurface',
    'water/waves/addWaves',
    'water/reflection/enableReflection'
  ]
};

/**
 * 获取第8批次所有节点类型
 */
export function getBatch8NodeTypes(): string[] {
  return Object.keys(BATCH8_NODE_MAPPING);
}

/**
 * 获取第8批次节点统计信息
 */
export function getBatch8Statistics() {
  const nodeTypes = getBatch8NodeTypes();
  const categories = Object.keys(BATCH8_NODE_CATEGORIES);
  
  return {
    totalNodes: nodeTypes.length,
    categories: categories.length,
    nodesByCategory: Object.fromEntries(
      categories.map(cat => [
        cat, 
        BATCH8_NODE_CATEGORIES[cat as keyof typeof BATCH8_NODE_CATEGORIES].length
      ])
    ),
    nodeRange: '211-240',
    description: '音频与粒子系统节点'
  };
}

/**
 * 验证节点类型是否属于第8批次
 */
export function isBatch8Node(nodeType: string): boolean {
  return nodeType in BATCH8_NODE_MAPPING;
}

/**
 * 获取节点所属类别
 */
export function getNodeCategory(nodeType: string): string | null {
  for (const [category, nodes] of Object.entries(BATCH8_NODE_CATEGORIES)) {
    if (nodes.includes(nodeType)) {
      return category;
    }
  }
  return null;
}
