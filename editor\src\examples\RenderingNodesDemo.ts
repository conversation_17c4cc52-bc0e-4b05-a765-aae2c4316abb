/**
 * 渲染节点演示脚本
 * 展示如何使用第4批次新增的渲染相机节点
 */

/**
 * 演示脚本数据结构
 */
export interface DemoScriptData {
  id: string;
  name: string;
  description: string;
  nodes: DemoNode[];
  connections: DemoConnection[];
}

/**
 * 演示节点数据结构
 */
export interface DemoNode {
  id: string;
  type: string;
  label: string;
  position: { x: number; y: number };
  inputs: Record<string, any>;
  outputs: Record<string, any>;
}

/**
 * 演示连接数据结构
 */
export interface DemoConnection {
  id: string;
  sourceNodeId: string;
  sourceSocket: string;
  targetNodeId: string;
  targetSocket: string;
}

/**
 * 基础相机创建演示
 */
export const basicCameraDemo: DemoScriptData = {
  id: 'basic-camera-demo',
  name: '基础相机创建演示',
  description: '演示如何创建透视相机和正交相机',
  nodes: [
    {
      id: 'start-node',
      type: 'core/events/onStart',
      label: '开始事件',
      position: { x: 100, y: 100 },
      inputs: {},
      outputs: {}
    },
    {
      id: 'perspective-camera-node',
      type: 'rendering/camera/createPerspectiveCamera',
      label: '创建透视相机',
      position: { x: 350, y: 100 },
      inputs: {
        fov: 60,
        aspect: 16/9,
        near: 0.1,
        far: 1000,
        entityName: '主相机'
      },
      outputs: {}
    },
    {
      id: 'orthographic-camera-node',
      type: 'rendering/camera/createOrthographicCamera',
      label: '创建正交相机',
      position: { x: 350, y: 300 },
      inputs: {
        left: -10,
        right: 10,
        top: 10,
        bottom: -10,
        near: 0.1,
        far: 1000,
        entityName: 'UI相机'
      },
      outputs: {}
    }
  ],
  connections: [
    {
      id: 'conn-1',
      sourceNodeId: 'start-node',
      sourceSocket: 'onComplete',
      targetNodeId: 'perspective-camera-node',
      targetSocket: 'execute'
    },
    {
      id: 'conn-2',
      sourceNodeId: 'perspective-camera-node',
      sourceSocket: 'onComplete',
      targetNodeId: 'orthographic-camera-node',
      targetSocket: 'execute'
    }
  ]
};

/**
 * 相机位置控制演示
 */
export const cameraPositionDemo: DemoScriptData = {
  id: 'camera-position-demo',
  name: '相机位置控制演示',
  description: '演示如何创建相机并设置其位置',
  nodes: [
    {
      id: 'start-node',
      type: 'core/events/onStart',
      label: '开始事件',
      position: { x: 100, y: 200 },
      inputs: {},
      outputs: {}
    },
    {
      id: 'create-camera-node',
      type: 'rendering/camera/createPerspectiveCamera',
      label: '创建透视相机',
      position: { x: 350, y: 200 },
      inputs: {
        fov: 75,
        entityName: '游戏相机'
      },
      outputs: {}
    },
    {
      id: 'set-position-node',
      type: 'rendering/camera/setCameraPosition',
      label: '设置相机位置',
      position: { x: 600, y: 200 },
      inputs: {
        position: { x: 0, y: 5, z: 10 }
      },
      outputs: {}
    },
    {
      id: 'debug-print-node',
      type: 'core/debug/print',
      label: '打印调试信息',
      position: { x: 850, y: 200 },
      inputs: {
        message: '相机创建并定位完成'
      },
      outputs: {}
    }
  ],
  connections: [
    {
      id: 'conn-1',
      sourceNodeId: 'start-node',
      sourceSocket: 'onComplete',
      targetNodeId: 'create-camera-node',
      targetSocket: 'execute'
    },
    {
      id: 'conn-2',
      sourceNodeId: 'create-camera-node',
      sourceSocket: 'onComplete',
      targetNodeId: 'set-position-node',
      targetSocket: 'execute'
    },
    {
      id: 'conn-3',
      sourceNodeId: 'create-camera-node',
      sourceSocket: 'entity',
      targetNodeId: 'set-position-node',
      targetSocket: 'entity'
    },
    {
      id: 'conn-4',
      sourceNodeId: 'set-position-node',
      sourceSocket: 'onComplete',
      targetNodeId: 'debug-print-node',
      targetSocket: 'execute'
    }
  ]
};

/**
 * 多相机系统演示
 */
export const multiCameraDemo: DemoScriptData = {
  id: 'multi-camera-demo',
  name: '多相机系统演示',
  description: '演示如何创建和管理多个相机',
  nodes: [
    {
      id: 'start-node',
      type: 'core/events/onStart',
      label: '开始事件',
      position: { x: 100, y: 300 },
      inputs: {},
      outputs: {}
    },
    // 主相机
    {
      id: 'main-camera-node',
      type: 'rendering/camera/createPerspectiveCamera',
      label: '创建主相机',
      position: { x: 350, y: 150 },
      inputs: {
        fov: 60,
        entityName: '主相机'
      },
      outputs: {}
    },
    {
      id: 'main-camera-position-node',
      type: 'rendering/camera/setCameraPosition',
      label: '设置主相机位置',
      position: { x: 600, y: 150 },
      inputs: {
        position: { x: 0, y: 2, z: 8 }
      },
      outputs: {}
    },
    // UI相机
    {
      id: 'ui-camera-node',
      type: 'rendering/camera/createOrthographicCamera',
      label: '创建UI相机',
      position: { x: 350, y: 350 },
      inputs: {
        left: -100,
        right: 100,
        top: 100,
        bottom: -100,
        entityName: 'UI相机'
      },
      outputs: {}
    },
    {
      id: 'ui-camera-position-node',
      type: 'rendering/camera/setCameraPosition',
      label: '设置UI相机位置',
      position: { x: 600, y: 350 },
      inputs: {
        position: { x: 0, y: 0, z: 1 }
      },
      outputs: {}
    },
    // 调试相机
    {
      id: 'debug-camera-node',
      type: 'rendering/camera/createPerspectiveCamera',
      label: '创建调试相机',
      position: { x: 350, y: 550 },
      inputs: {
        fov: 90,
        entityName: '调试相机'
      },
      outputs: {}
    },
    {
      id: 'debug-camera-position-node',
      type: 'rendering/camera/setCameraPosition',
      label: '设置调试相机位置',
      position: { x: 600, y: 550 },
      inputs: {
        position: { x: 10, y: 10, z: 10 }
      },
      outputs: {}
    }
  ],
  connections: [
    // 主相机连接
    {
      id: 'conn-1',
      sourceNodeId: 'start-node',
      sourceSocket: 'onComplete',
      targetNodeId: 'main-camera-node',
      targetSocket: 'execute'
    },
    {
      id: 'conn-2',
      sourceNodeId: 'main-camera-node',
      sourceSocket: 'onComplete',
      targetNodeId: 'main-camera-position-node',
      targetSocket: 'execute'
    },
    {
      id: 'conn-3',
      sourceNodeId: 'main-camera-node',
      sourceSocket: 'entity',
      targetNodeId: 'main-camera-position-node',
      targetSocket: 'entity'
    },
    // UI相机连接
    {
      id: 'conn-4',
      sourceNodeId: 'main-camera-position-node',
      sourceSocket: 'onComplete',
      targetNodeId: 'ui-camera-node',
      targetSocket: 'execute'
    },
    {
      id: 'conn-5',
      sourceNodeId: 'ui-camera-node',
      sourceSocket: 'onComplete',
      targetNodeId: 'ui-camera-position-node',
      targetSocket: 'execute'
    },
    {
      id: 'conn-6',
      sourceNodeId: 'ui-camera-node',
      sourceSocket: 'entity',
      targetNodeId: 'ui-camera-position-node',
      targetSocket: 'entity'
    },
    // 调试相机连接
    {
      id: 'conn-7',
      sourceNodeId: 'ui-camera-position-node',
      sourceSocket: 'onComplete',
      targetNodeId: 'debug-camera-node',
      targetSocket: 'execute'
    },
    {
      id: 'conn-8',
      sourceNodeId: 'debug-camera-node',
      sourceSocket: 'onComplete',
      targetNodeId: 'debug-camera-position-node',
      targetSocket: 'execute'
    },
    {
      id: 'conn-9',
      sourceNodeId: 'debug-camera-node',
      sourceSocket: 'entity',
      targetNodeId: 'debug-camera-position-node',
      targetSocket: 'entity'
    }
  ]
};

/**
 * 所有演示脚本
 */
export const renderingNodesDemos = [
  basicCameraDemo,
  cameraPositionDemo,
  multiCameraDemo
];

/**
 * 获取演示脚本
 * @param id 演示脚本ID
 * @returns 演示脚本数据
 */
export function getDemoScript(id: string): DemoScriptData | undefined {
  return renderingNodesDemos.find(demo => demo.id === id);
}

/**
 * 获取所有演示脚本列表
 * @returns 演示脚本列表
 */
export function getAllDemoScripts(): DemoScriptData[] {
  return renderingNodesDemos;
}
