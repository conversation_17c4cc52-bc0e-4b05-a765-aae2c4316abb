/**
 * 视觉脚本对象操作节点
 * 提供对象处理相关功能
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 获取对象属性节点
 * 获取对象的属性值
 */
export class ObjectGetPropertyNode extends FunctionNode {
  constructor(options: any) {
    super({ ...options, category: NodeCategory.OBJECT });
    // Category is set via constructor options
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加对象输入插槽
    this.addInput({
      name: 'object',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '对象',
      defaultValue: {}
    });

    this.addInput({
      name: 'property',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '属性名',
      defaultValue: ''
    });

    this.addInput({
      name: 'defaultValue',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '默认值（可选）'
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '属性值'
    });

    this.addOutput({
      name: 'exists',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '属性是否存在'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const object = this.getInputValue('object') as any || {};
    const property = this.getInputValue('property') as string || '';
    const defaultValue = this.getInputValue('defaultValue');

    // 获取属性值
    const exists = property in object;
    const value = exists ? object[property] : defaultValue;

    // 设置输出值
    this.setOutputValue('value', value);
    this.setOutputValue('exists', exists);

    // 触发输出流程
    this.triggerFlow('flow');

    return value;
  }
}

/**
 * 设置对象属性节点
 * 设置对象的属性值
 */
export class ObjectSetPropertyNode extends FunctionNode {
  constructor(options: any) {
    super({ ...options, category: NodeCategory.OBJECT });
    // Category is set via constructor options
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加对象输入插槽
    this.addInput({
      name: 'object',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '对象',
      defaultValue: {}
    });

    this.addInput({
      name: 'property',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '属性名',
      defaultValue: ''
    });

    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '属性值'
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '修改后的对象'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const object = { ...(this.getInputValue('object') as any || {}) };
    const property = this.getInputValue('property') as string || '';
    const value = this.getInputValue('value');

    // 设置属性值
    if (property) {
      object[property] = value;
    }

    // 设置输出值
    this.setOutputValue('result', object);

    // 触发输出流程
    this.triggerFlow('flow');

    return object;
  }
}

/**
 * 检查对象属性节点
 * 检查对象是否有指定属性
 */
export class ObjectHasPropertyNode extends FunctionNode {
  constructor(options: any) {
    super({ ...options, category: NodeCategory.OBJECT });
    // Category is set via constructor options
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加对象输入插槽
    this.addInput({
      name: 'object',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '对象',
      defaultValue: {}
    });

    this.addInput({
      name: 'property',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '属性名',
      defaultValue: ''
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'hasProperty',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否有该属性'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const object = this.getInputValue('object') as any || {};
    const property = this.getInputValue('property') as string || '';

    // 检查属性是否存在
    const hasProperty = property in object;

    // 设置输出值
    this.setOutputValue('hasProperty', hasProperty);

    // 触发输出流程
    this.triggerFlow('flow');

    return hasProperty;
  }
}

/**
 * 获取对象键列表节点
 * 获取对象的所有键
 */
export class ObjectKeysNode extends FunctionNode {
  constructor(options: any) {
    super({ ...options, category: NodeCategory.OBJECT });
    // Category is set via constructor options
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加对象输入插槽
    this.addInput({
      name: 'object',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '对象',
      defaultValue: {}
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'keys',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '键列表'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const object = this.getInputValue('object') as any || {};

    // 获取对象的键
    const keys = Object.keys(object);

    // 设置输出值
    this.setOutputValue('keys', keys);

    // 触发输出流程
    this.triggerFlow('flow');

    return keys;
  }
}

/**
 * 获取对象值列表节点
 * 获取对象的所有值
 */
export class ObjectValuesNode extends FunctionNode {
  constructor(options: any) {
    super({ ...options, category: NodeCategory.OBJECT });
    // Category is set via constructor options
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加对象输入插槽
    this.addInput({
      name: 'object',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '对象',
      defaultValue: {}
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'values',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '值列表'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const object = this.getInputValue('object') as any || {};

    // 获取对象的值
    const values = Object.values(object);

    // 设置输出值
    this.setOutputValue('values', values);

    // 触发输出流程
    this.triggerFlow('flow');

    return values;
  }
}

/**
 * 对象合并节点
 * 合并多个对象
 */
export class ObjectMergeNode extends FunctionNode {
  constructor(options: any) {
    super({ ...options, category: NodeCategory.OBJECT });
    // Category is set via constructor options
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加对象输入插槽
    this.addInput({
      name: 'target',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '目标对象',
      defaultValue: {}
    });

    this.addInput({
      name: 'source',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '源对象',
      defaultValue: {}
    });

    this.addInput({
      name: 'deep',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '深度合并',
      defaultValue: false
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '合并后的对象'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const target = this.getInputValue('target') as any || {};
    const source = this.getInputValue('source') as any || {};
    const deep = this.getInputValue('deep') as boolean || false;

    // 合并对象
    let result: any;
    if (deep) {
      result = this.deepMerge(target, source);
    } else {
      result = { ...target, ...source };
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }

  /**
   * 深度合并对象
   * @param target 目标对象
   * @param source 源对象
   * @returns 合并后的对象
   */
  private deepMerge(target: any, source: any): any {
    const result = { ...target };

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (
          typeof source[key] === 'object' &&
          source[key] !== null &&
          !Array.isArray(source[key]) &&
          typeof result[key] === 'object' &&
          result[key] !== null &&
          !Array.isArray(result[key])
        ) {
          result[key] = this.deepMerge(result[key], source[key]);
        } else {
          result[key] = source[key];
        }
      }
    }

    return result;
  }
}

/**
 * 对象克隆节点
 * 克隆对象
 */
export class ObjectCloneNode extends FunctionNode {
  constructor(options: any) {
    super({ ...options, category: NodeCategory.OBJECT });
    // Category is set via constructor options
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加对象输入插槽
    this.addInput({
      name: 'object',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要克隆的对象',
      defaultValue: {}
    });

    this.addInput({
      name: 'deep',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '深度克隆',
      defaultValue: false
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '克隆后的对象'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const object = this.getInputValue('object') as any || {};
    const deep = this.getInputValue('deep') as boolean || false;

    // 克隆对象
    let result: any;
    if (deep) {
      result = this.deepClone(object);
    } else {
      result = { ...object };
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }

  /**
   * 深度克隆对象
   * @param obj 要克隆的对象
   * @returns 克隆后的对象
   */
  private deepClone(obj: any): any {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime());
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.deepClone(item));
    }

    const cloned: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = this.deepClone(obj[key]);
      }
    }

    return cloned;
  }
}

/**
 * 注册对象操作节点
 * @param registry 节点注册表
 */
export function registerObjectNodes(registry: NodeRegistry): void {
  // 注册获取对象属性节点
  registry.registerNodeType({
    type: 'object/getProperty',
    category: NodeCategory.OBJECT,
    constructor: ObjectGetPropertyNode,
    label: '获取属性',
    description: '获取对象的属性值',
    icon: 'get',
    color: '#795548',
    tags: ['object', 'property', 'get']
  });

  // 注册设置对象属性节点
  registry.registerNodeType({
    type: 'object/setProperty',
    category: NodeCategory.OBJECT,
    constructor: ObjectSetPropertyNode,
    label: '设置属性',
    description: '设置对象的属性值',
    icon: 'set',
    color: '#795548',
    tags: ['object', 'property', 'set']
  });

  // 注册检查对象属性节点
  registry.registerNodeType({
    type: 'object/hasProperty',
    category: NodeCategory.OBJECT,
    constructor: ObjectHasPropertyNode,
    label: '检查属性',
    description: '检查对象是否有指定属性',
    icon: 'check',
    color: '#795548',
    tags: ['object', 'property', 'has']
  });

  // 注册获取对象键列表节点
  registry.registerNodeType({
    type: 'object/keys',
    category: NodeCategory.OBJECT,
    constructor: ObjectKeysNode,
    label: '获取键列表',
    description: '获取对象的所有键',
    icon: 'keys',
    color: '#795548',
    tags: ['object', 'keys', 'list']
  });

  // 注册获取对象值列表节点
  registry.registerNodeType({
    type: 'object/values',
    category: NodeCategory.OBJECT,
    constructor: ObjectValuesNode,
    label: '获取值列表',
    description: '获取对象的所有值',
    icon: 'values',
    color: '#795548',
    tags: ['object', 'values', 'list']
  });

  // 注册对象合并节点
  registry.registerNodeType({
    type: 'object/merge',
    category: NodeCategory.OBJECT,
    constructor: ObjectMergeNode,
    label: '对象合并',
    description: '合并多个对象',
    icon: 'merge',
    color: '#795548',
    tags: ['object', 'merge', 'combine']
  });

  // 注册对象克隆节点
  registry.registerNodeType({
    type: 'object/clone',
    category: NodeCategory.OBJECT,
    constructor: ObjectCloneNode,
    label: '对象克隆',
    description: '克隆对象',
    icon: 'clone',
    color: '#795548',
    tags: ['object', 'clone', 'copy']
  });
}
