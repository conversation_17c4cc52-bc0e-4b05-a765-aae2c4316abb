/**
 * 视觉脚本节点测试运行器
 */
import { runNewNodesTest, getNewNodesStatistics } from './NewNodesTest';

/**
 * 运行所有测试
 */
function runAllTests(): void {
  console.log('=== 视觉脚本节点测试 ===\n');

  // 运行新节点测试
  const newNodesResult = runNewNodesTest();
  
  // 获取节点统计
  const stats = getNewNodesStatistics();
  
  console.log('\n=== 节点统计 ===');
  console.log(`总节点数: ${stats.totalNodes}`);
  console.log(`类别数: ${stats.categories}`);
  console.log('\n类别分布:');
  stats.nodesByCategory.forEach((cat: any) => {
    console.log(`- ${cat.category}: ${cat.count}个节点`);
  });
  
  console.log('\n=== 测试总结 ===');
  console.log(`新节点测试: ${newNodesResult ? '✅ 通过' : '❌ 失败'}`);
  console.log(`\n总体结果: ${newNodesResult ? '✅ 所有测试通过' : '❌ 部分测试失败'}`);
}

// 运行测试
runAllTests();
