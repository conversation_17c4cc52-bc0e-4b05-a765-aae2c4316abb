/**
 * 第4批次节点测试组件
 * 用于测试新增的渲染相机节点功能
 */
import React, { useState, useEffect } from 'react';
import { Card, Button, Space, message, Descriptions, Tag, Alert, Divider } from 'antd';
import { 
  PlayCircleOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  CameraOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { engineNodeIntegration } from '../../services/EngineNodeIntegration';
import { nodeRegistryService } from '../../services/NodeRegistryService';

/**
 * 第4批次节点测试组件
 */
const Batch4NodeTest: React.FC = () => {
  const [testResults, setTestResults] = useState<{
    nodeRegistration: boolean;
    engineIntegration: boolean;
    scriptExecution: boolean;
  }>({
    nodeRegistration: false,
    engineIntegration: false,
    scriptExecution: false
  });

  const [isLoading, setIsLoading] = useState(false);
  const [integrationStatus, setIntegrationStatus] = useState<any>(null);

  // 检查节点注册状态
  useEffect(() => {
    checkNodeRegistration();
    checkEngineIntegration();
  }, []);

  /**
   * 检查节点注册状态
   */
  const checkNodeRegistration = () => {
    try {
      const batch4Nodes = [
        'rendering/camera/createPerspectiveCamera',
        'rendering/camera/createOrthographicCamera',
        'rendering/camera/setCameraPosition'
      ];

      const allRegistered = batch4Nodes.every(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        return node !== undefined;
      });

      setTestResults(prev => ({
        ...prev,
        nodeRegistration: allRegistered
      }));

      if (allRegistered) {
        message.success('节点注册检查通过');
      } else {
        message.warning('部分节点未注册');
      }
    } catch (error) {
      console.error('检查节点注册失败:', error);
      message.error('节点注册检查失败');
    }
  };

  /**
   * 检查引擎集成状态
   */
  const checkEngineIntegration = () => {
    try {
      const status = engineNodeIntegration.getStatus();
      setIntegrationStatus(status);

      setTestResults(prev => ({
        ...prev,
        engineIntegration: status.isInitialized && status.engineConnected
      }));

      if (status.isInitialized && status.engineConnected) {
        message.success('引擎集成检查通过');
      } else {
        message.warning('引擎集成未完成');
      }
    } catch (error) {
      console.error('检查引擎集成失败:', error);
      message.error('引擎集成检查失败');
    }
  };

  /**
   * 执行脚本测试
   */
  const executeScriptTest = async () => {
    setIsLoading(true);
    try {
      const success = await engineNodeIntegration.executeTestScript();
      
      setTestResults(prev => ({
        ...prev,
        scriptExecution: success
      }));

      if (success) {
        message.success('脚本执行测试通过');
      } else {
        message.error('脚本执行测试失败');
      }
    } catch (error) {
      console.error('脚本执行测试失败:', error);
      message.error('脚本执行测试失败');
      setTestResults(prev => ({
        ...prev,
        scriptExecution: false
      }));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 运行完整测试
   */
  const runFullTest = async () => {
    setIsLoading(true);
    try {
      // 重新检查所有状态
      checkNodeRegistration();
      checkEngineIntegration();
      
      // 等待一下确保状态更新
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 执行脚本测试
      await executeScriptTest();
      
      message.success('完整测试执行完成');
    } catch (error) {
      console.error('完整测试失败:', error);
      message.error('完整测试失败');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 获取测试状态图标
   */
  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircleOutlined style={{ color: '#52c41a' }} />
    ) : (
      <ExclamationCircleOutlined style={{ color: '#faad14' }} />
    );
  };

  /**
   * 获取测试状态标签
   */
  const getStatusTag = (status: boolean) => {
    return status ? (
      <Tag color="success">通过</Tag>
    ) : (
      <Tag color="warning">未通过</Tag>
    );
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card 
        title={
          <Space>
            <CameraOutlined />
            第4批次节点测试 - 渲染相机节点
          </Space>
        }
        extra={
          <Space>
            <Button 
              type="primary" 
              icon={<PlayCircleOutlined />}
              onClick={runFullTest}
              loading={isLoading}
            >
              运行完整测试
            </Button>
          </Space>
        }
      >
        {/* 测试概述 */}
        <Alert
          message="第4批次节点测试"
          description="测试新增的3个渲染相机节点的注册、集成和执行功能"
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        {/* 节点列表 */}
        <Card size="small" title="测试节点列表" style={{ marginBottom: '24px' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Tag color="blue">118</Tag>
              <strong>rendering/camera/createPerspectiveCamera</strong> - 创建透视相机
            </div>
            <div>
              <Tag color="blue">119</Tag>
              <strong>rendering/camera/createOrthographicCamera</strong> - 创建正交相机
            </div>
            <div>
              <Tag color="blue">120</Tag>
              <strong>rendering/camera/setCameraPosition</strong> - 设置相机位置
            </div>
          </Space>
        </Card>

        {/* 测试结果 */}
        <Card size="small" title="测试结果" style={{ marginBottom: '24px' }}>
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item 
              label={
                <Space>
                  {getStatusIcon(testResults.nodeRegistration)}
                  节点注册测试
                </Space>
              }
            >
              <Space>
                {getStatusTag(testResults.nodeRegistration)}
                检查节点是否正确注册到节点注册服务
              </Space>
            </Descriptions.Item>
            
            <Descriptions.Item 
              label={
                <Space>
                  {getStatusIcon(testResults.engineIntegration)}
                  引擎集成测试
                </Space>
              }
            >
              <Space>
                {getStatusTag(testResults.engineIntegration)}
                检查节点是否正确集成到引擎
              </Space>
            </Descriptions.Item>
            
            <Descriptions.Item 
              label={
                <Space>
                  {getStatusIcon(testResults.scriptExecution)}
                  脚本执行测试
                </Space>
              }
            >
              <Space>
                {getStatusTag(testResults.scriptExecution)}
                检查节点是否能正确执行
              </Space>
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 集成状态详情 */}
        {integrationStatus && (
          <Card size="small" title="集成状态详情">
            <Descriptions column={2} bordered size="small">
              <Descriptions.Item label="初始化状态">
                {integrationStatus.isInitialized ? (
                  <Tag color="success">已初始化</Tag>
                ) : (
                  <Tag color="error">未初始化</Tag>
                )}
              </Descriptions.Item>
              
              <Descriptions.Item label="引擎连接">
                {integrationStatus.engineConnected ? (
                  <Tag color="success">已连接</Tag>
                ) : (
                  <Tag color="error">未连接</Tag>
                )}
              </Descriptions.Item>
              
              <Descriptions.Item label="注册节点数量" span={2}>
                <Tag color="blue">{integrationStatus.registeredNodes.length}</Tag>
              </Descriptions.Item>
            </Descriptions>
            
            <Divider />
            
            <div>
              <strong>已注册节点:</strong>
              <div style={{ marginTop: '8px' }}>
                {integrationStatus.registeredNodes.map((nodeType: string) => (
                  <Tag key={nodeType} color="green" style={{ marginBottom: '4px' }}>
                    {nodeType}
                  </Tag>
                ))}
              </div>
            </div>
          </Card>
        )}

        {/* 操作按钮 */}
        <div style={{ marginTop: '24px', textAlign: 'center' }}>
          <Space>
            <Button onClick={checkNodeRegistration}>
              重新检查节点注册
            </Button>
            <Button onClick={checkEngineIntegration}>
              重新检查引擎集成
            </Button>
            <Button 
              onClick={executeScriptTest}
              loading={isLoading}
              icon={<EyeOutlined />}
            >
              执行脚本测试
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default Batch4NodeTest;
