/**
 * 视觉脚本预设节点
 * 提供各种预设节点的注册
 */
import { NodeRegistry } from '../nodes/NodeRegistry';
import { registerMathNodes } from './MathNodes';
import { registerLogicNodes } from './LogicNodes';
import { registerEntityNodes } from './EntityNodes';
import { registerPhysicsNodes } from './PhysicsNodes';
import { registerNewAnimationNodes } from './AnimationNodes';

/**
 * 注册所有预设节点
 * @param registry 节点注册表
 */
export function registerAllPresetNodes(registry: NodeRegistry): void {
  // 注册数学节点
  registerMathNodes(registry);

  // 注册逻辑节点
  registerLogicNodes(registry);

  // 注册实体节点
  registerEntityNodes(registry);

  // 注册物理节点（第6批次：151-165）
  registerPhysicsNodes(registry);

  // 注册动画节点（第6批次：166-180）
  registerNewAnimationNodes(registry);

  // 可以在这里添加更多节点类型的注册
}

export * from './MathNodes';
export * from './LogicNodes';
export * from './EntityNodes';
export * from './PhysicsNodes';
export * from './AnimationNodes';
