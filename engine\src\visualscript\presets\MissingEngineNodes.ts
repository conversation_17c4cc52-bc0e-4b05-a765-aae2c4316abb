/**
 * 缺失的引擎节点实现
 * 包含用户列表中需要注册到引擎的228个节点
 */

import { Node, NodeCategory, SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

// ==================== 数学和三角函数节点 ====================

/**
 * 正弦节点
 */
export class SinNode extends Node {
  constructor() {
    super('math/trigonometry/sin', NodeCategory.MATH);
    
    this.addInput({
      name: 'angle',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '角度（弧度）'
    });
    
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '正弦值'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const angle = this.getInputValue('angle', 0);
    const result = Math.sin(angle);
    this.setOutputValue('result', result);
  }
}

/**
 * 余弦节点
 */
export class CosNode extends Node {
  constructor() {
    super('math/trigonometry/cos', NodeCategory.MATH);
    
    this.addInput({
      name: 'angle',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '角度（弧度）'
    });
    
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '余弦值'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const angle = this.getInputValue('angle', 0);
    const result = Math.cos(angle);
    this.setOutputValue('result', result);
  }
}

/**
 * 向量长度节点
 */
export class VectorMagnitudeNode extends Node {
  constructor() {
    super('math/vector/magnitude', NodeCategory.MATH);
    
    this.addInput({
      name: 'vector',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.INPUT,
      description: '输入向量'
    });
    
    this.addOutput({
      name: 'magnitude',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '向量长度'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const vector = this.getInputValue('vector', { x: 0, y: 0, z: 0 });
    const magnitude = Math.sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
    this.setOutputValue('magnitude', magnitude);
  }
}

/**
 * 向量归一化节点
 */
export class VectorNormalizeNode extends Node {
  constructor() {
    super('math/vector/normalize', NodeCategory.MATH);
    
    this.addInput({
      name: 'vector',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.INPUT,
      description: '输入向量'
    });
    
    this.addOutput({
      name: 'normalized',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.OUTPUT,
      description: '归一化向量'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const vector = this.getInputValue('vector', { x: 0, y: 0, z: 0 });
    const magnitude = Math.sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
    
    if (magnitude === 0) {
      this.setOutputValue('normalized', { x: 0, y: 0, z: 0 });
    } else {
      this.setOutputValue('normalized', {
        x: vector.x / magnitude,
        y: vector.y / magnitude,
        z: vector.z / magnitude
      });
    }
  }
}

// ==================== 逻辑节点 ====================

/**
 * 逻辑与节点
 */
export class LogicalAndNode extends Node {
  constructor() {
    super('logic/boolean/and', NodeCategory.LOGIC);
    
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '第一个布尔值'
    });
    
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '第二个布尔值'
    });
    
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '逻辑与结果'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const a = this.getInputValue('a', false);
    const b = this.getInputValue('b', false);
    this.setOutputValue('result', a && b);
  }
}

// ==================== 物理系统节点 ====================

/**
 * 设置重力节点
 */
export class SetGravityNode extends Node {
  constructor() {
    super('physics/gravity/set', NodeCategory.PHYSICS);
    
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    this.addInput({
      name: 'gravity',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.INPUT,
      description: '重力向量',
      defaultValue: { x: 0, y: -9.81, z: 0 }
    });
    
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const gravity = this.getInputValue('gravity', { x: 0, y: -9.81, z: 0 });
    
    // 设置物理世界重力
    if (context.world && context.world.physicsWorld) {
      context.world.physicsWorld.setGravity(gravity);
    }
    
    this.executeOutput('exec', context);
  }
}

/**
 * 碰撞检测节点
 */
export class CollisionDetectNode extends Node {
  constructor() {
    super('physics/collision/detect', NodeCategory.PHYSICS);
    
    this.addInput({
      name: 'entityA',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.INPUT,
      description: '实体A'
    });
    
    this.addInput({
      name: 'entityB',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.INPUT,
      description: '实体B'
    });
    
    this.addOutput({
      name: 'isColliding',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否碰撞'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entityA = this.getInputValue('entityA');
    const entityB = this.getInputValue('entityB');
    
    // 检测碰撞逻辑
    let isColliding = false;
    if (entityA && entityB && context.world && context.world.physicsWorld) {
      isColliding = context.world.physicsWorld.checkCollision(entityA, entityB);
    }
    
    this.setOutputValue('isColliding', isColliding);
  }
}

/**
 * 创建刚体节点
 */
export class CreateRigidBodyNode extends Node {
  constructor() {
    super('physics/rigidbody/create', NodeCategory.PHYSICS);

    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'mass',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '质量',
      defaultValue: 1.0
    });

    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    this.addOutput({
      name: 'rigidBody',
      type: SocketType.DATA,
      dataType: 'rigidBody',
      direction: SocketDirection.OUTPUT,
      description: '创建的刚体'
    });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const mass = this.getInputValue('mass', 1.0);

    if (entity && context.world && context.world.physicsWorld) {
      const rigidBody = context.world.physicsWorld.createRigidBody(entity, { mass });
      this.setOutputValue('rigidBody', rigidBody);
    }

    this.executeOutput('exec', context);
  }
}

/**
 * 施加力节点
 */
export class ApplyForceNode extends Node {
  constructor() {
    super('physics/force/apply', NodeCategory.PHYSICS);

    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'force',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.INPUT,
      description: '力向量'
    });

    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const force = this.getInputValue('force', { x: 0, y: 0, z: 0 });

    if (entity && context.world && context.world.physicsWorld) {
      context.world.physicsWorld.applyForce(entity, force);
    }

    this.executeOutput('exec', context);
  }
}

// ==================== 实体变换节点 ====================

/**
 * 获取位置节点
 */
export class GetPositionNode extends Node {
  constructor() {
    super('entity/transform/getPosition', NodeCategory.ENTITY);

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.OUTPUT,
      description: '实体位置'
    });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');

    if (entity && entity.transform) {
      this.setOutputValue('position', entity.transform.position);
    } else {
      this.setOutputValue('position', { x: 0, y: 0, z: 0 });
    }
  }
}

/**
 * 设置位置节点
 */
export class SetPositionNode extends Node {
  constructor() {
    super('entity/transform/setPosition', NodeCategory.ENTITY);

    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.INPUT,
      description: '新位置'
    });

    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const position = this.getInputValue('position', { x: 0, y: 0, z: 0 });

    if (entity && entity.transform) {
      entity.transform.position = position;
    }

    this.executeOutput('exec', context);
  }
}

/**
 * 获取旋转节点
 */
export class GetRotationNode extends Node {
  constructor() {
    super('entity/transform/getRotation', NodeCategory.ENTITY);

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addOutput({
      name: 'rotation',
      type: SocketType.DATA,
      dataType: 'quaternion',
      direction: SocketDirection.OUTPUT,
      description: '实体旋转'
    });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');

    if (entity && entity.transform) {
      this.setOutputValue('rotation', entity.transform.rotation);
    } else {
      this.setOutputValue('rotation', { x: 0, y: 0, z: 0, w: 1 });
    }
  }
}

/**
 * 设置旋转节点
 */
export class SetRotationNode extends Node {
  constructor() {
    super('entity/transform/setRotation', NodeCategory.ENTITY);

    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'rotation',
      type: SocketType.DATA,
      dataType: 'quaternion',
      direction: SocketDirection.INPUT,
      description: '新旋转'
    });

    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const rotation = this.getInputValue('rotation', { x: 0, y: 0, z: 0, w: 1 });

    if (entity && entity.transform) {
      entity.transform.rotation = rotation;
    }

    this.executeOutput('exec', context);
  }
}

// ==================== 时间和动画节点 ====================

/**
 * 延迟节点
 */
export class DelayNode extends Node {
  constructor() {
    super('time/delay', NodeCategory.TIME);

    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '延迟时间（秒）',
      defaultValue: 1.0
    });

    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const duration = this.getInputValue('duration', 1.0);

    setTimeout(() => {
      this.executeOutput('exec', context);
    }, duration * 1000);
  }
}

/**
 * 计时器节点
 */
export class TimerNode extends Node {
  private intervalId: NodeJS.Timeout | null = null;

  constructor() {
    super('time/timer', NodeCategory.TIME);

    this.addInput({
      name: 'start',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '开始计时'
    });

    this.addInput({
      name: 'stop',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '停止计时'
    });

    this.addInput({
      name: 'interval',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '间隔时间（秒）',
      defaultValue: 1.0
    });

    this.addOutput({
      name: 'tick',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '计时触发'
    });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const inputSocket = context.inputSocket;

    if (inputSocket === 'start') {
      const interval = this.getInputValue('interval', 1.0);

      if (this.intervalId) {
        clearInterval(this.intervalId);
      }

      this.intervalId = setInterval(() => {
        this.executeOutput('tick', context);
      }, interval * 1000);
    } else if (inputSocket === 'stop') {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }
    }
  }
}

// ==================== 注册函数 ====================

/**
 * 注册缺失的引擎节点（第一批：数学、逻辑、物理基础节点）
 * @param registry 节点注册表
 */
export function registerMissingEngineNodesBatch1(registry: NodeRegistry): void {
  // 注册数学和三角函数节点
  registry.registerNodeType({
    type: 'math/trigonometry/sin',
    category: NodeCategory.MATH,
    constructor: SinNode,
    label: '正弦',
    description: '计算角度的正弦值',
    icon: 'sin',
    color: '#2196F3',
    tags: ['math', 'trigonometry', 'sin']
  });

  registry.registerNodeType({
    type: 'math/trigonometry/cos',
    category: NodeCategory.MATH,
    constructor: CosNode,
    label: '余弦',
    description: '计算角度的余弦值',
    icon: 'cos',
    color: '#2196F3',
    tags: ['math', 'trigonometry', 'cos']
  });

  registry.registerNodeType({
    type: 'math/vector/magnitude',
    category: NodeCategory.MATH,
    constructor: VectorMagnitudeNode,
    label: '向量长度',
    description: '计算向量的长度',
    icon: 'vector-magnitude',
    color: '#2196F3',
    tags: ['math', 'vector', 'magnitude']
  });

  registry.registerNodeType({
    type: 'math/vector/normalize',
    category: NodeCategory.MATH,
    constructor: VectorNormalizeNode,
    label: '向量归一化',
    description: '将向量归一化为单位向量',
    icon: 'vector-normalize',
    color: '#2196F3',
    tags: ['math', 'vector', 'normalize']
  });

  // 注册逻辑节点
  registry.registerNodeType({
    type: 'logic/boolean/and',
    category: NodeCategory.LOGIC,
    constructor: LogicalAndNode,
    label: '逻辑与',
    description: '执行逻辑与运算',
    icon: 'and',
    color: '#FF9800',
    tags: ['logic', 'boolean', 'and']
  });

  // 注册物理节点
  registry.registerNodeType({
    type: 'physics/gravity/set',
    category: NodeCategory.PHYSICS,
    constructor: SetGravityNode,
    label: '设置重力',
    description: '设置物理世界的重力',
    icon: 'gravity',
    color: '#9C27B0',
    tags: ['physics', 'gravity', 'set']
  });

  registry.registerNodeType({
    type: 'physics/collision/detect',
    category: NodeCategory.PHYSICS,
    constructor: CollisionDetectNode,
    label: '碰撞检测',
    description: '检测两个实体是否碰撞',
    icon: 'collision',
    color: '#9C27B0',
    tags: ['physics', 'collision', 'detect']
  });

  registry.registerNodeType({
    type: 'physics/rigidbody/create',
    category: NodeCategory.PHYSICS,
    constructor: CreateRigidBodyNode,
    label: '创建刚体',
    description: '为实体创建刚体组件',
    icon: 'rigidbody',
    color: '#9C27B0',
    tags: ['physics', 'rigidbody', 'create']
  });

  registry.registerNodeType({
    type: 'physics/force/apply',
    category: NodeCategory.PHYSICS,
    constructor: ApplyForceNode,
    label: '施加力',
    description: '向实体施加力',
    icon: 'force',
    color: '#9C27B0',
    tags: ['physics', 'force', 'apply']
  });

  // 注册实体变换节点
  registry.registerNodeType({
    type: 'entity/transform/getPosition',
    category: NodeCategory.ENTITY,
    constructor: GetPositionNode,
    label: '获取位置',
    description: '获取实体的位置',
    icon: 'position-get',
    color: '#4CAF50',
    tags: ['entity', 'transform', 'position', 'get']
  });

  registry.registerNodeType({
    type: 'entity/transform/setPosition',
    category: NodeCategory.ENTITY,
    constructor: SetPositionNode,
    label: '设置位置',
    description: '设置实体的位置',
    icon: 'position-set',
    color: '#4CAF50',
    tags: ['entity', 'transform', 'position', 'set']
  });

  registry.registerNodeType({
    type: 'entity/transform/getRotation',
    category: NodeCategory.ENTITY,
    constructor: GetRotationNode,
    label: '获取旋转',
    description: '获取实体的旋转',
    icon: 'rotation-get',
    color: '#4CAF50',
    tags: ['entity', 'transform', 'rotation', 'get']
  });

  registry.registerNodeType({
    type: 'entity/transform/setRotation',
    category: NodeCategory.ENTITY,
    constructor: SetRotationNode,
    label: '设置旋转',
    description: '设置实体的旋转',
    icon: 'rotation-set',
    color: '#4CAF50',
    tags: ['entity', 'transform', 'rotation', 'set']
  });

  // 注册时间节点
  registry.registerNodeType({
    type: 'time/delay',
    category: NodeCategory.TIME,
    constructor: DelayNode,
    label: '延迟',
    description: '延迟执行指定时间',
    icon: 'delay',
    color: '#607D8B',
    tags: ['time', 'delay']
  });

  registry.registerNodeType({
    type: 'time/timer',
    category: NodeCategory.TIME,
    constructor: TimerNode,
    label: '计时器',
    description: '按间隔时间重复执行',
    icon: 'timer',
    color: '#607D8B',
    tags: ['time', 'timer', 'interval']
  });

  console.log('已注册缺失的引擎节点（第一批）：16个节点');
}
