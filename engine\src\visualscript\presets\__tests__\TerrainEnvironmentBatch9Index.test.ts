/**
 * 第9批次地形与环境系统索引测试
 * 测试索引文件的导出和映射是否正确
 */

import {
  // 地形与环境系统节点 (241-250)
  EnableRefractionNode,
  CreateVegetationNode,
  AddGrassNode,
  AddTreesNode,
  CreateWeatherSystemNode,
  EnableRainNode,
  EnableSnowNode,
  SetWindDirectionNode,
  SetWindStrengthNode,
  SetTimeOfDayNode,
  
  // 编辑器项目管理节点 (251-270)
  CreateProjectNode,
  OpenProjectNode,
  SaveProjectNode,
  CloseProjectNode,
  CreateProjectTemplateNode,
  ImportProjectNode,
  SetProjectSettingsNode,
  GetProjectInfoNode,
  AddAssetNode,
  RemoveAssetNode,
  GetAssetListNode,
  CreateSceneNode,
  DeleteSceneNode,
  GetSceneListNode,
  SetActiveSceneNode,
  GetActiveSceneNode,
  BuildProjectNode,
  PublishProjectNode,
  BackupProjectNode,
  RestoreProjectNode,
  
  // 映射和工具函数
  BATCH9_NODE_MAPPING,
  BATCH9_NODE_CATEGORIES,
  getBatch9NodeTypes,
  getBatch9NodeCount,
  validateBatch9Nodes,
  BATCH9_NODE_DESCRIPTIONS
} from '../TerrainEnvironmentBatch9Index';

describe('第9批次地形与环境系统索引测试', () => {
  // 模拟节点选项
  const mockOptions = {
    id: 'test-node',
    type: 'test',
    category: 'test',
    metadata: { positionX: 0, positionY: 0 },
    graph: null,
    context: null
  };

  describe('地形与环境系统节点导出 (241-250)', () => {
    test('所有地形与环境系统节点都应该能够导入', () => {
      expect(EnableRefractionNode).toBeDefined();
      expect(CreateVegetationNode).toBeDefined();
      expect(AddGrassNode).toBeDefined();
      expect(AddTreesNode).toBeDefined();
      expect(CreateWeatherSystemNode).toBeDefined();
      expect(EnableRainNode).toBeDefined();
      expect(EnableSnowNode).toBeDefined();
      expect(SetWindDirectionNode).toBeDefined();
      expect(SetWindStrengthNode).toBeDefined();
      expect(SetTimeOfDayNode).toBeDefined();
    });

    test('所有地形与环境系统节点都应该能够实例化', () => {
      const nodes = [
        new EnableRefractionNode(mockOptions),
        new CreateVegetationNode(mockOptions),
        new AddGrassNode(mockOptions),
        new AddTreesNode(mockOptions),
        new CreateWeatherSystemNode(mockOptions),
        new EnableRainNode(mockOptions),
        new EnableSnowNode(mockOptions),
        new SetWindDirectionNode(mockOptions),
        new SetWindStrengthNode(mockOptions),
        new SetTimeOfDayNode(mockOptions)
      ];

      nodes.forEach(node => {
        expect(node).toBeDefined();
        expect(node.id).toBe('test-node');
      });
    });
  });

  describe('编辑器项目管理节点导出 (251-270)', () => {
    test('所有编辑器项目管理节点都应该能够导入', () => {
      expect(CreateProjectNode).toBeDefined();
      expect(OpenProjectNode).toBeDefined();
      expect(SaveProjectNode).toBeDefined();
      expect(CloseProjectNode).toBeDefined();
      expect(CreateProjectTemplateNode).toBeDefined();
      expect(ImportProjectNode).toBeDefined();
      expect(SetProjectSettingsNode).toBeDefined();
      expect(GetProjectInfoNode).toBeDefined();
      expect(AddAssetNode).toBeDefined();
      expect(RemoveAssetNode).toBeDefined();
      expect(GetAssetListNode).toBeDefined();
      expect(CreateSceneNode).toBeDefined();
      expect(DeleteSceneNode).toBeDefined();
      expect(GetSceneListNode).toBeDefined();
      expect(SetActiveSceneNode).toBeDefined();
      expect(GetActiveSceneNode).toBeDefined();
      expect(BuildProjectNode).toBeDefined();
      expect(PublishProjectNode).toBeDefined();
      expect(BackupProjectNode).toBeDefined();
      expect(RestoreProjectNode).toBeDefined();
    });

    test('所有编辑器项目管理节点都应该能够实例化', () => {
      const nodes = [
        new CreateProjectNode(mockOptions),
        new OpenProjectNode(mockOptions),
        new SaveProjectNode(mockOptions),
        new CloseProjectNode(mockOptions),
        new CreateProjectTemplateNode(mockOptions),
        new ImportProjectNode(mockOptions),
        new SetProjectSettingsNode(mockOptions),
        new GetProjectInfoNode(mockOptions),
        new AddAssetNode(mockOptions),
        new RemoveAssetNode(mockOptions),
        new GetAssetListNode(mockOptions),
        new CreateSceneNode(mockOptions),
        new DeleteSceneNode(mockOptions),
        new GetSceneListNode(mockOptions),
        new SetActiveSceneNode(mockOptions),
        new GetActiveSceneNode(mockOptions),
        new BuildProjectNode(mockOptions),
        new PublishProjectNode(mockOptions),
        new BackupProjectNode(mockOptions),
        new RestoreProjectNode(mockOptions)
      ];

      nodes.forEach(node => {
        expect(node).toBeDefined();
        expect(node.id).toBe('test-node');
      });
    });
  });

  describe('节点映射和工具函数', () => {
    test('节点映射应该包含所有30个节点', () => {
      expect(BATCH9_NODE_MAPPING).toBeDefined();
      expect(Object.keys(BATCH9_NODE_MAPPING)).toHaveLength(30);
    });

    test('节点类别分组应该正确', () => {
      expect(BATCH9_NODE_CATEGORIES).toBeDefined();
      expect(BATCH9_NODE_CATEGORIES.WATER_EFFECTS).toBeDefined();
      expect(BATCH9_NODE_CATEGORIES.VEGETATION_SYSTEM).toBeDefined();
      expect(BATCH9_NODE_CATEGORIES.WEATHER_SYSTEM).toBeDefined();
      expect(BATCH9_NODE_CATEGORIES.ENVIRONMENT_CONTROL).toBeDefined();
      expect(BATCH9_NODE_CATEGORIES.PROJECT_MANAGEMENT).toBeDefined();
      expect(BATCH9_NODE_CATEGORIES.ASSET_MANAGEMENT).toBeDefined();
      expect(BATCH9_NODE_CATEGORIES.SCENE_MANAGEMENT).toBeDefined();
    });

    test('getBatch9NodeTypes 应该返回所有节点类型', () => {
      const nodeTypes = getBatch9NodeTypes();
      expect(nodeTypes).toHaveLength(30);
      expect(nodeTypes).toContain('water/refraction/enableRefraction');
      expect(nodeTypes).toContain('editor/project/createProject');
    });

    test('getBatch9NodeCount 应该返回30', () => {
      expect(getBatch9NodeCount()).toBe(30);
    });

    test('validateBatch9Nodes 应该返回true', () => {
      expect(validateBatch9Nodes()).toBe(true);
    });

    test('节点描述应该包含所有30个节点', () => {
      expect(BATCH9_NODE_DESCRIPTIONS).toBeDefined();
      expect(Object.keys(BATCH9_NODE_DESCRIPTIONS)).toHaveLength(30);
    });
  });

  describe('节点数量验证', () => {
    test('应该总共有30个节点类', () => {
      const allNodeClasses = [
        // 地形与环境系统节点 (241-250)
        EnableRefractionNode, CreateVegetationNode, AddGrassNode, AddTreesNode,
        CreateWeatherSystemNode, EnableRainNode, EnableSnowNode, SetWindDirectionNode,
        SetWindStrengthNode, SetTimeOfDayNode,
        
        // 编辑器项目管理节点 (251-270)
        CreateProjectNode, OpenProjectNode, SaveProjectNode, CloseProjectNode,
        CreateProjectTemplateNode, ImportProjectNode, SetProjectSettingsNode, GetProjectInfoNode,
        AddAssetNode, RemoveAssetNode, GetAssetListNode, CreateSceneNode,
        DeleteSceneNode, GetSceneListNode, SetActiveSceneNode, GetActiveSceneNode,
        BuildProjectNode, PublishProjectNode, BackupProjectNode, RestoreProjectNode
      ];
      
      expect(allNodeClasses.length).toBe(30);
    });
  });
});
