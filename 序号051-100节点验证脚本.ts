/**
 * 序号051-100节点验证脚本
 * 验证这50个节点是否都能在编辑器中通过拖拽方式使用
 */

import { NodeRegistryService } from './editor/src/services/NodeRegistryService';

/**
 * 序号051-100的50个节点列表
 */
const BATCH2_3_NODES = [
  // 第2批次：时间节点 (051-052)
  { id: 51, type: 'time/delay', name: '延迟' },
  { id: 52, type: 'time/timer', name: '计时器' },
  
  // 第2批次：动画节点 (053-056)
  { id: 53, type: 'animation/playAnimation', name: '播放动画' },
  { id: 54, type: 'animation/stopAnimation', name: '停止动画' },
  { id: 55, type: 'animation/setAnimationSpeed', name: '设置动画速度' },
  { id: 56, type: 'animation/getAnimationState', name: '获取动画状态' },
  
  // 第2批次：输入节点 (057-059)
  { id: 57, type: 'input/keyboard', name: '键盘输入' },
  { id: 58, type: 'input/mouse', name: '鼠标输入' },
  { id: 59, type: 'input/gamepad', name: '游戏手柄输入' },
  
  // 第2批次：音频节点 (060-064)
  { id: 60, type: 'audio/playAudio', name: '播放音频' },
  { id: 61, type: 'audio/stopAudio', name: '停止音频' },
  { id: 62, type: 'audio/setVolume', name: '设置音量' },
  { id: 63, type: 'audio/analyzer', name: '音频分析' },
  { id: 64, type: 'audio/audio3D', name: '3D音频' },
  
  // 第2批次：调试节点 (065-069)
  { id: 65, type: 'debug/breakpoint', name: '断点' },
  { id: 66, type: 'debug/log', name: '日志' },
  { id: 67, type: 'debug/performanceTimer', name: '性能计时' },
  { id: 68, type: 'debug/variableWatch', name: '变量监视' },
  { id: 69, type: 'debug/assert', name: '断言' },
  
  // 第2批次：网络安全节点 (070-074)
  { id: 70, type: 'network/security/encryptData', name: '数据加密' },
  { id: 71, type: 'network/security/decryptData', name: '数据解密' },
  { id: 72, type: 'network/security/hashData', name: '数据哈希' },
  { id: 73, type: 'network/security/authenticateUser', name: '用户认证' },
  { id: 74, type: 'network/security/validateSession', name: '验证会话' },
  
  // 第2批次：WebRTC节点 (075-078)
  { id: 75, type: 'network/webrtc/createConnection', name: '创建WebRTC连接' },
  { id: 76, type: 'network/webrtc/sendDataChannelMessage', name: '发送数据通道消息' },
  { id: 77, type: 'network/webrtc/createDataChannel', name: '创建数据通道' },
  { id: 78, type: 'network/webrtc/closeConnection', name: '关闭WebRTC连接' },
  
  // 第2批次：AI情感节点 (079-080)
  { id: 79, type: 'ai/emotion/analyze', name: '情感分析' },
  { id: 80, type: 'ai/emotion/driveAnimation', name: '情感驱动动画' },
  
  // 第2批次：AI自然语言处理节点 (081-084)
  { id: 81, type: 'ai/nlp/classifyText', name: '文本分类' },
  { id: 82, type: 'ai/nlp/recognizeEntities', name: '命名实体识别' },
  { id: 83, type: 'ai/nlp/analyzeSentiment', name: '情感分析' },
  { id: 84, type: 'ai/nlp/extractKeywords', name: '关键词提取' },
  
  // 第2批次：网络协议节点 (085-087)
  { id: 85, type: 'network/protocol/udpSend', name: 'UDP发送' },
  { id: 86, type: 'network/protocol/httpRequest', name: 'HTTP请求' },
  { id: 87, type: 'network/protocol/tcpConnect', name: 'TCP连接' },
  
  // 第3批次：字符串操作节点 (088-095)
  { id: 88, type: 'string/concat', name: '字符串连接' },
  { id: 89, type: 'string/substring', name: '子字符串' },
  { id: 90, type: 'string/replace', name: '字符串替换' },
  { id: 91, type: 'string/split', name: '字符串分割' },
  { id: 92, type: 'string/length', name: '字符串长度' },
  { id: 93, type: 'string/toUpperCase', name: '转大写' },
  { id: 94, type: 'string/toLowerCase', name: '转小写' },
  { id: 95, type: 'string/trim', name: '去除空格' },
  
  // 第3批次：数组操作节点 (096-100)
  { id: 96, type: 'array/push', name: '数组添加' },
  { id: 97, type: 'array/pop', name: '数组弹出' },
  { id: 98, type: 'array/length', name: '数组长度' },
  { id: 99, type: 'array/get', name: '获取元素' },
  { id: 100, type: 'array/set', name: '设置元素' }
];

/**
 * 验证结果接口
 */
interface ValidationResult {
  nodeId: number;
  nodeType: string;
  nodeName: string;
  editorRegistered: boolean;
  draggable: boolean;
  status: 'success' | 'warning' | 'error';
  issues: string[];
}

/**
 * 验证序号051-100的50个节点
 */
export function validateBatch2And3Nodes(): {
  summary: {
    total: number;
    success: number;
    warning: number;
    error: number;
  };
  results: ValidationResult[];
  recommendations: string[];
} {
  console.log('🔍 开始验证序号051-100的50个节点...');
  
  const nodeRegistry = NodeRegistryService.getInstance();
  
  const results: ValidationResult[] = [];
  let successCount = 0;
  let warningCount = 0;
  let errorCount = 0;

  // 获取所有已注册的节点
  const allRegisteredNodes = nodeRegistry.getAllNodes();
  const registeredNodeTypes = new Set(allRegisteredNodes.map(node => node.type));

  // 验证每个节点
  BATCH2_3_NODES.forEach(node => {
    const result: ValidationResult = {
      nodeId: node.id,
      nodeType: node.type,
      nodeName: node.name,
      editorRegistered: false,
      draggable: false,
      status: 'error',
      issues: []
    };

    // 检查编辑器注册状态
    result.editorRegistered = registeredNodeTypes.has(node.type);
    if (!result.editorRegistered) {
      result.issues.push('未在编辑器中注册');
    }

    // 检查拖拽功能（基于编辑器注册状态）
    result.draggable = result.editorRegistered;
    if (!result.draggable) {
      result.issues.push('无法拖拽使用');
    }

    // 确定状态
    if (result.editorRegistered && result.draggable) {
      result.status = 'success';
      successCount++;
    } else if (result.editorRegistered || result.draggable) {
      result.status = 'warning';
      warningCount++;
    } else {
      result.status = 'error';
      errorCount++;
    }

    results.push(result);
  });

  // 生成建议
  const recommendations: string[] = [];
  if (errorCount > 0) {
    recommendations.push(`需要修复 ${errorCount} 个节点的注册问题`);
  }
  if (warningCount > 0) {
    recommendations.push(`需要完善 ${warningCount} 个节点的集成`);
  }
  if (successCount === BATCH2_3_NODES.length) {
    recommendations.push('🎉 所有节点都已正确注册和集成！');
  }

  // 输出结果
  console.log('\n📊 验证结果统计:');
  console.log(`总计: ${BATCH2_3_NODES.length}个节点`);
  console.log(`✅ 成功: ${successCount}个 (${Math.round(successCount/BATCH2_3_NODES.length*100)}%)`);
  console.log(`⚠️ 警告: ${warningCount}个 (${Math.round(warningCount/BATCH2_3_NODES.length*100)}%)`);
  console.log(`❌ 错误: ${errorCount}个 (${Math.round(errorCount/BATCH2_3_NODES.length*100)}%)`);

  return {
    summary: {
      total: BATCH2_3_NODES.length,
      success: successCount,
      warning: warningCount,
      error: errorCount
    },
    results,
    recommendations
  };
}

/**
 * 运行验证（如果直接执行此文件）
 */
if (require.main === module) {
  validateBatch2And3Nodes();
}
