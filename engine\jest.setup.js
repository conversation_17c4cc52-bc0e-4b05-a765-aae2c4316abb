/**
 * Jest测试设置文件
 * 在所有测试运行前执行的全局设置
 */

// 扩展Jest匹配器
import 'jest-extended';

// 全局测试配置
global.console = {
  ...console,
  // 在测试中静默某些日志
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// 模拟浏览器环境的全局对象
global.window = {
  requestAnimationFrame: jest.fn(),
  cancelAnimationFrame: jest.fn()
};

// 模拟性能API
global.performance = {
  now: jest.fn(() => Date.now()),
  mark: jest.fn(),
  measure: jest.fn()
};

// 设置测试超时
jest.setTimeout(10000);

// 全局测试钩子
beforeEach(() => {
  // 清除所有模拟调用
  jest.clearAllMocks();
});

afterEach(() => {
  // 清理定时器
  jest.clearAllTimers();
});

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  throw reason;
});

// 测试工具函数
global.testUtils = {
  // 创建模拟节点选项
  createMockNodeOptions: (overrides = {}) => ({
    id: 'test-node',
    position: { x: 0, y: 0 },
    ...overrides
  }),
  
  // 创建模拟执行上下文
  createMockExecutionContext: (overrides = {}) => ({
    inputSocket: null,
    performanceMonitor: {
      recordNodeExecution: jest.fn()
    },
    world: null,
    ...overrides
  }),
  
  // 等待异步操作
  waitFor: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // 验证向量相等（带容差）
  expectVectorToBeCloseTo: (actual, expected, precision = 5) => {
    expect(actual.x).toBeCloseTo(expected.x, precision);
    expect(actual.y).toBeCloseTo(expected.y, precision);
    expect(actual.z).toBeCloseTo(expected.z, precision);
  }
};

console.log('🧪 Jest测试环境已初始化');
