/**
 * 批量修复第8批次节点文件中的TypeScript错误
 */

const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const files = [
  'engine/src/visualscript/presets/AudioParticleNodes.ts',
  'engine/src/visualscript/presets/AudioParticleNodes2.ts',
  'engine/src/visualscript/presets/AudioParticleNodes3.ts',
  'engine/src/visualscript/presets/AudioParticleNodes4.ts'
];

// 修复函数
function fixNodeFile(filePath) {
  console.log(`正在修复文件: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 修复构造函数调用 - 将 super('type', 'name', category) 改为 super({type: 'type', category: category})
  content = content.replace(
    /super\('([^']+)',\s*'[^']+',\s*NodeCategory\.(\w+)\);/g,
    "super({\n      type: '$1',\n      category: NodeCategory.$2\n    });"
  );
  
  // 修复 SocketType.NUMBER 为 SocketType.DATA 并添加 dataType: 'number'
  content = content.replace(
    /type: SocketType\.NUMBER,(\s+direction: SocketDirection\.\w+,\s+description: '[^']+',)/g,
    "type: SocketType.DATA,$1\n      dataType: 'number',"
  );
  
  // 修复 SocketType.STRING 为 SocketType.DATA 并添加 dataType: 'string'
  content = content.replace(
    /type: SocketType\.STRING,(\s+direction: SocketDirection\.\w+,\s+description: '[^']+',)/g,
    "type: SocketType.DATA,$1\n      dataType: 'string',"
  );
  
  // 处理没有默认值的情况
  content = content.replace(
    /type: SocketType\.NUMBER,(\s+direction: SocketDirection\.\w+,\s+description: '[^']+'\s+})/g,
    "type: SocketType.DATA,$1"
  );
  
  content = content.replace(
    /type: SocketType\.STRING,(\s+direction: SocketDirection\.\w+,\s+description: '[^']+'\s+})/g,
    "type: SocketType.DATA,$1"
  );
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`文件修复完成: ${filePath}`);
}

// 执行修复
files.forEach(fixNodeFile);

console.log('所有文件修复完成！');
