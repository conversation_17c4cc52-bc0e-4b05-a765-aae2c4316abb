/**
 * 全面统计视觉脚本系统中的所有节点
 * 包括引擎层面的节点和编辑器层面的节点
 */

const fs = require('fs');
const path = require('path');

console.log('开始全面分析视觉脚本系统中的所有节点...');

// 统计结果
const nodeStats = {
  engineNodes: {}, // 引擎层面的节点（VisualScriptSystem注册的）
  editorNodes: {}, // 编辑器层面的节点（NodeRegistryService注册的）
  totalEngineNodes: 0,
  totalEditorNodes: 0,
  totalNodes: 0
};

// 1. 统计引擎层面的节点（从预设文件中）
function countEngineNodes() {
  console.log('\n=== 统计引擎层面的节点 ===');
  
  const presetDir = 'engine/src/visualscript/presets';
  const presetFiles = [
    'CoreNodes.ts',
    'LogicNodes.ts', 
    'EntityNodes.ts',
    'MathNodes.ts',
    'TimeNodes.ts',
    'PhysicsNodes.ts',
    'SoftBodyNodes.ts',
    'AnimationNodes.ts',
    'InputNodes.ts',
    'AudioNodes.ts',
    'NetworkNodes.ts',
    'AINodes.ts',
    'DebugNodes.ts',
    'NetworkSecurityNodes.ts',
    'WebRTCNodes.ts',
    'AIEmotionNodes.ts',
    'AINLPNodes.ts',
    'NetworkProtocolNodes.ts',
    'StringNodes.ts',
    'ArrayNodes.ts',
    'ObjectNodes.ts',
    'VariableNodes.ts',
    'RenderingNodes.ts'
  ];

  presetFiles.forEach(fileName => {
    const filePath = path.join(presetDir, fileName);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 统计registerNodeType调用
      const registerPattern = /registry\.registerNodeType\(\{[\s\S]*?\}\);/g;
      const matches = content.match(registerPattern) || [];
      
      const nodeCategory = fileName.replace('.ts', '');
      nodeStats.engineNodes[nodeCategory] = matches.length;
      nodeStats.totalEngineNodes += matches.length;
      
      console.log(`${nodeCategory}: ${matches.length}个节点`);
    } else {
      console.log(`${fileName}: 文件不存在`);
    }
  });
}

// 2. 统计编辑器层面的节点（从NodeRegistryService中）
function countEditorNodes() {
  console.log('\n=== 统计编辑器层面的节点 ===');
  
  const filePath = 'editor/src/services/NodeRegistryService.ts';
  if (!fs.existsSync(filePath)) {
    console.log('NodeRegistryService.ts 文件不存在');
    return;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  let currentBatch = 'Unknown';
  const batchNodes = {};
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 检测批次
    if (line.includes('initializeDefaultNodes')) {
      currentBatch = 'Default';
    } else if (line.includes('initializeBatch4Nodes')) {
      currentBatch = 'Batch4';
    } else if (line.includes('initializeBatch5Nodes')) {
      currentBatch = 'Batch5';
    } else if (line.includes('initializeBatch7Nodes')) {
      currentBatch = 'Batch7';
    } else if (line.includes('initializeBatch8Nodes')) {
      currentBatch = 'Batch8';
    }
    
    // 统计registerNode调用
    if (line.includes('this.registerNode({')) {
      if (!batchNodes[currentBatch]) {
        batchNodes[currentBatch] = 0;
      }
      batchNodes[currentBatch]++;
      nodeStats.totalEditorNodes++;
    }
  }
  
  nodeStats.editorNodes = batchNodes;
  
  Object.entries(batchNodes).forEach(([batch, count]) => {
    console.log(`${batch}: ${count}个节点`);
  });
}

// 3. 分析预设节点文件的详细内容
function analyzePresetFiles() {
  console.log('\n=== 详细分析预设节点文件 ===');
  
  const presetDir = 'engine/src/visualscript/presets';
  const importantFiles = ['CoreNodes.ts', 'LogicNodes.ts', 'EntityNodes.ts', 'MathNodes.ts', 'PhysicsNodes.ts'];
  
  importantFiles.forEach(fileName => {
    const filePath = path.join(presetDir, fileName);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 提取节点类型
      const typePattern = /type:\s*['"`]([^'"`]+)['"`]/g;
      const types = [];
      let match;
      
      while ((match = typePattern.exec(content)) !== null) {
        types.push(match[1]);
      }
      
      console.log(`\n${fileName} (${types.length}个节点):`);
      types.slice(0, 5).forEach((type, index) => {
        console.log(`  ${index + 1}. ${type}`);
      });
      if (types.length > 5) {
        console.log(`  ... 还有${types.length - 5}个节点`);
      }
    }
  });
}

// 4. 检查是否有遗漏的批次
function checkMissingBatches() {
  console.log('\n=== 检查遗漏的批次 ===');
  
  // 根据文档应该有的批次
  const expectedBatches = [
    { name: '第1批次', range: '001-030', count: 30, description: '现有核心节点' },
    { name: '第2批次', range: '031-060', count: 30, description: '现有基础节点' },
    { name: '第3批次', range: '061-090', count: 30, description: '现有扩展节点' },
    { name: '第4批次', range: '091-120', count: 30, description: '渲染相机' },
    { name: '第5批次', range: '121-150', count: 30, description: '渲染系统核心' },
    { name: '第6批次', range: '151-180', count: 30, description: '物理系统' },
    { name: '第7批次', range: '181-210', count: 30, description: '动画系统扩展' },
    { name: '第8批次', range: '211-240', count: 30, description: '音频与粒子系统' }
  ];
  
  console.log('预期批次:');
  expectedBatches.forEach(batch => {
    console.log(`  ${batch.name} (${batch.range}): ${batch.count}个 - ${batch.description}`);
  });
  
  console.log('\n实际在NodeRegistryService中注册的批次:');
  Object.entries(nodeStats.editorNodes).forEach(([batch, count]) => {
    console.log(`  ${batch}: ${count}个节点`);
  });
}

// 执行统计
countEngineNodes();
countEditorNodes();
analyzePresetFiles();
checkMissingBatches();

// 计算总数
nodeStats.totalNodes = nodeStats.totalEngineNodes + nodeStats.totalEditorNodes;

console.log('\n=== 最终统计结果 ===');
console.log(`引擎层面节点总数: ${nodeStats.totalEngineNodes}个`);
console.log(`编辑器层面节点总数: ${nodeStats.totalEditorNodes}个`);
console.log(`系统总节点数: ${nodeStats.totalNodes}个`);

console.log('\n=== 关键发现 ===');
console.log('1. 系统中存在两套节点注册机制:');
console.log('   - 引擎层面: VisualScriptSystem 注册预设节点（用于脚本执行）');
console.log('   - 编辑器层面: NodeRegistryService 注册界面节点（用于拖拽创建）');

console.log('\n2. 可能的问题:');
console.log('   - 引擎层面的节点没有完全暴露到编辑器界面');
console.log('   - 第1、2、3、6批次的节点可能只在引擎层面注册，编辑器无法使用');

console.log('\n3. 建议:');
console.log('   - 需要将引擎层面的节点同步注册到编辑器层面');
console.log('   - 确保所有240个节点都能在编辑器中拖拽使用');

console.log('\n✅ 分析完成');
console.log(`当前编辑器可用节点数: ${nodeStats.totalEditorNodes}个`);
console.log(`引擎可执行节点数: ${nodeStats.totalEngineNodes}个`);
