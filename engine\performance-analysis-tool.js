/**
 * 视觉脚本性能分析工具
 * 提供性能数据分析和可视化报告
 */

console.log('🔍 启动视觉脚本性能分析工具...');

// 模拟性能监控数据
class MockPerformanceData {
  static generateSampleData() {
    const nodeTypes = [
      'math/trigonometry/sin',
      'math/trigonometry/cos',
      'math/vector/magnitude',
      'math/vector/normalize',
      'logic/boolean/and',
      'physics/gravity/set',
      'physics/collision/detect',
      'time/delay',
      'animation/playAnimation',
      'audio/playAudio'
    ];

    const metrics = [];
    const statistics = new Map();

    // 生成模拟指标数据
    for (let i = 0; i < 1000; i++) {
      const nodeType = nodeTypes[Math.floor(Math.random() * nodeTypes.length)];
      const executionTime = Math.random() * 100 + 1; // 1-101ms
      const memoryUsage = Math.random() * 1024 * 1024 * 10; // 0-10MB
      const status = Math.random() > 0.1 ? 'success' : (Math.random() > 0.5 ? 'error' : 'timeout');

      metrics.push({
        nodeId: `node-${i}`,
        nodeType,
        executionTime,
        memoryUsage,
        status,
        timestamp: Date.now() - Math.random() * 3600000, // 过去1小时内
        errorMessage: status !== 'success' ? '模拟错误' : undefined
      });

      // 更新统计信息
      if (!statistics.has(nodeType)) {
        statistics.set(nodeType, {
          nodeType,
          totalExecutions: 0,
          successCount: 0,
          errorCount: 0,
          timeoutCount: 0,
          totalExecutionTime: 0,
          maxExecutionTime: 0,
          minExecutionTime: Infinity,
          memoryUsage: { total: 0, max: 0, min: Infinity }
        });
      }

      const stats = statistics.get(nodeType);
      stats.totalExecutions++;
      stats.totalExecutionTime += executionTime;
      stats.maxExecutionTime = Math.max(stats.maxExecutionTime, executionTime);
      stats.minExecutionTime = Math.min(stats.minExecutionTime, executionTime);
      stats.memoryUsage.total += memoryUsage;
      stats.memoryUsage.max = Math.max(stats.memoryUsage.max, memoryUsage);
      stats.memoryUsage.min = Math.min(stats.memoryUsage.min, memoryUsage);

      if (status === 'success') stats.successCount++;
      else if (status === 'error') stats.errorCount++;
      else stats.timeoutCount++;
    }

    // 计算平均值
    statistics.forEach(stats => {
      stats.averageExecutionTime = stats.totalExecutionTime / stats.totalExecutions;
      stats.memoryUsage.average = stats.memoryUsage.total / stats.totalExecutions;
    });

    return { metrics, statistics: Array.from(statistics.values()) };
  }
}

// 性能分析器
class PerformanceAnalyzer {
  constructor(data) {
    this.metrics = data.metrics;
    this.statistics = data.statistics;
  }

  // 分析执行时间趋势
  analyzeExecutionTimeTrends() {
    console.log('\n📊 执行时间趋势分析:');
    
    const sortedStats = this.statistics
      .sort((a, b) => b.averageExecutionTime - a.averageExecutionTime);

    console.log('最慢的5个节点类型:');
    sortedStats.slice(0, 5).forEach((stat, index) => {
      console.log(`  ${index + 1}. ${stat.nodeType}: ${stat.averageExecutionTime.toFixed(2)}ms`);
    });

    console.log('\n最快的5个节点类型:');
    sortedStats.slice(-5).reverse().forEach((stat, index) => {
      console.log(`  ${index + 1}. ${stat.nodeType}: ${stat.averageExecutionTime.toFixed(2)}ms`);
    });
  }

  // 分析错误率
  analyzeErrorRates() {
    console.log('\n❌ 错误率分析:');
    
    const errorStats = this.statistics
      .map(stat => ({
        nodeType: stat.nodeType,
        errorRate: (stat.errorCount + stat.timeoutCount) / stat.totalExecutions,
        totalErrors: stat.errorCount + stat.timeoutCount,
        totalExecutions: stat.totalExecutions
      }))
      .filter(stat => stat.errorRate > 0)
      .sort((a, b) => b.errorRate - a.errorRate);

    if (errorStats.length === 0) {
      console.log('  🎉 没有发现错误！所有节点都运行正常。');
      return;
    }

    console.log('错误率最高的节点类型:');
    errorStats.slice(0, 5).forEach((stat, index) => {
      console.log(`  ${index + 1}. ${stat.nodeType}: ${(stat.errorRate * 100).toFixed(1)}% (${stat.totalErrors}/${stat.totalExecutions})`);
    });
  }

  // 分析内存使用
  analyzeMemoryUsage() {
    console.log('\n💾 内存使用分析:');
    
    const memoryStats = this.statistics
      .sort((a, b) => b.memoryUsage.average - a.memoryUsage.average);

    console.log('内存使用最高的5个节点类型:');
    memoryStats.slice(0, 5).forEach((stat, index) => {
      const avgMB = (stat.memoryUsage.average / (1024 * 1024)).toFixed(2);
      const maxMB = (stat.memoryUsage.max / (1024 * 1024)).toFixed(2);
      console.log(`  ${index + 1}. ${stat.nodeType}: 平均 ${avgMB}MB, 峰值 ${maxMB}MB`);
    });
  }

  // 分析执行频率
  analyzeExecutionFrequency() {
    console.log('\n🔄 执行频率分析:');
    
    const frequencyStats = this.statistics
      .sort((a, b) => b.totalExecutions - a.totalExecutions);

    console.log('执行最频繁的5个节点类型:');
    frequencyStats.slice(0, 5).forEach((stat, index) => {
      console.log(`  ${index + 1}. ${stat.nodeType}: ${stat.totalExecutions} 次执行`);
    });
  }

  // 生成性能热点分析
  analyzePerformanceHotspots() {
    console.log('\n🔥 性能热点分析:');
    
    const hotspots = this.statistics
      .map(stat => ({
        nodeType: stat.nodeType,
        totalTime: stat.totalExecutionTime,
        averageTime: stat.averageExecutionTime,
        executions: stat.totalExecutions,
        impact: stat.totalExecutionTime * stat.totalExecutions // 影响因子
      }))
      .sort((a, b) => b.impact - a.impact);

    console.log('性能影响最大的5个节点类型:');
    hotspots.slice(0, 5).forEach((hotspot, index) => {
      console.log(`  ${index + 1}. ${hotspot.nodeType}:`);
      console.log(`     总执行时间: ${hotspot.totalTime.toFixed(2)}ms`);
      console.log(`     平均执行时间: ${hotspot.averageTime.toFixed(2)}ms`);
      console.log(`     执行次数: ${hotspot.executions}`);
      console.log(`     影响因子: ${hotspot.impact.toFixed(0)}`);
    });
  }

  // 生成优化建议
  generateOptimizationRecommendations() {
    console.log('\n💡 优化建议:');
    
    const recommendations = [];

    // 检查慢执行节点
    const slowNodes = this.statistics.filter(stat => stat.averageExecutionTime > 50);
    if (slowNodes.length > 0) {
      recommendations.push(`发现 ${slowNodes.length} 个执行缓慢的节点类型，建议优化算法或启用缓存`);
    }

    // 检查高错误率节点
    const errorNodes = this.statistics.filter(stat => {
      const errorRate = (stat.errorCount + stat.timeoutCount) / stat.totalExecutions;
      return errorRate > 0.05;
    });
    if (errorNodes.length > 0) {
      recommendations.push(`发现 ${errorNodes.length} 个高错误率的节点类型，建议改善错误处理和输入验证`);
    }

    // 检查高内存使用节点
    const highMemoryNodes = this.statistics.filter(stat => 
      stat.memoryUsage.average > 5 * 1024 * 1024
    );
    if (highMemoryNodes.length > 0) {
      recommendations.push(`发现 ${highMemoryNodes.length} 个高内存使用的节点类型，建议优化内存管理`);
    }

    // 检查频繁执行节点
    const frequentNodes = this.statistics.filter(stat => stat.totalExecutions > 100);
    if (frequentNodes.length > 0) {
      recommendations.push(`发现 ${frequentNodes.length} 个频繁执行的节点类型，建议启用批处理或结果缓存`);
    }

    if (recommendations.length === 0) {
      recommendations.push('系统性能良好，继续保持当前的优化水平');
    }

    recommendations.forEach((rec, index) => {
      console.log(`  ${index + 1}. ${rec}`);
    });
  }

  // 生成性能报告摘要
  generateSummaryReport() {
    console.log('\n📋 性能报告摘要:');
    
    const totalExecutions = this.statistics.reduce((sum, stat) => sum + stat.totalExecutions, 0);
    const totalSuccesses = this.statistics.reduce((sum, stat) => sum + stat.successCount, 0);
    const totalExecutionTime = this.statistics.reduce((sum, stat) => sum + stat.totalExecutionTime, 0);
    const totalMemoryUsage = this.metrics.reduce((sum, metric) => sum + metric.memoryUsage, 0);

    console.log(`总节点类型数: ${this.statistics.length}`);
    console.log(`总执行次数: ${totalExecutions}`);
    console.log(`总成功率: ${((totalSuccesses / totalExecutions) * 100).toFixed(1)}%`);
    console.log(`平均执行时间: ${(totalExecutionTime / totalExecutions).toFixed(2)}ms`);
    console.log(`总内存使用: ${(totalMemoryUsage / (1024 * 1024)).toFixed(2)}MB`);
    console.log(`数据收集时间范围: 过去1小时`);
  }
}

// 性能可视化工具
class PerformanceVisualizer {
  static generateTextChart(data, title, maxWidth = 50) {
    console.log(`\n📈 ${title}:`);
    
    const maxValue = Math.max(...data.map(item => item.value));
    
    data.forEach(item => {
      const barLength = Math.round((item.value / maxValue) * maxWidth);
      const bar = '█'.repeat(barLength) + '░'.repeat(maxWidth - barLength);
      console.log(`  ${item.label.padEnd(25)} ${bar} ${item.value.toFixed(1)}`);
    });
  }

  static generateExecutionTimeChart(statistics) {
    const data = statistics
      .sort((a, b) => b.averageExecutionTime - a.averageExecutionTime)
      .slice(0, 10)
      .map(stat => ({
        label: stat.nodeType.split('/').pop(),
        value: stat.averageExecutionTime
      }));

    this.generateTextChart(data, '平均执行时间 (ms)');
  }

  static generateMemoryUsageChart(statistics) {
    const data = statistics
      .sort((a, b) => b.memoryUsage.average - a.memoryUsage.average)
      .slice(0, 10)
      .map(stat => ({
        label: stat.nodeType.split('/').pop(),
        value: stat.memoryUsage.average / (1024 * 1024) // 转换为MB
      }));

    this.generateTextChart(data, '平均内存使用 (MB)');
  }
}

// 主分析流程
function runPerformanceAnalysis() {
  console.log('🚀 开始性能分析');
  console.log('='.repeat(60));

  // 生成模拟数据
  const data = MockPerformanceData.generateSampleData();
  console.log(`📊 已生成 ${data.metrics.length} 条性能指标数据`);
  console.log(`📊 涵盖 ${data.statistics.length} 种节点类型`);

  // 创建分析器
  const analyzer = new PerformanceAnalyzer(data);

  // 执行各种分析
  analyzer.generateSummaryReport();
  analyzer.analyzeExecutionTimeTrends();
  analyzer.analyzeErrorRates();
  analyzer.analyzeMemoryUsage();
  analyzer.analyzeExecutionFrequency();
  analyzer.analyzePerformanceHotspots();
  analyzer.generateOptimizationRecommendations();

  // 生成可视化图表
  PerformanceVisualizer.generateExecutionTimeChart(data.statistics);
  PerformanceVisualizer.generateMemoryUsageChart(data.statistics);

  console.log('\n' + '='.repeat(60));
  console.log('✅ 性能分析完成！');
  console.log('💡 建议定期运行此分析工具以监控系统性能');
}

// 运行分析
runPerformanceAnalysis();
