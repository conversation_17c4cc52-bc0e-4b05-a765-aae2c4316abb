/**
 * 序号201-240节点验证脚本
 * 验证第7批次（201-210）和第8批次（211-240）节点的注册和集成状态
 */

// 模拟NodeRegistryService来检查节点注册状态
const fs = require('fs');
const path = require('path');

/**
 * 序号201-240的节点配置
 */
const BATCH7_8_NODES = [
  // 第7批次扩展：场景管理系统节点 (201-210)
  { id: 201, type: 'scene/management/createScene', name: '创建场景' },
  { id: 202, type: 'scene/management/loadScene', name: '加载场景' },
  { id: 203, type: 'scene/management/saveScene', name: '保存场景' },
  { id: 204, type: 'scene/management/switchScene', name: '切换场景' },
  { id: 205, type: 'scene/management/addToScene', name: '添加到场景' },
  { id: 206, type: 'scene/management/removeFromScene', name: '从场景移除' },
  { id: 207, type: 'scene/culling/enableFrustumCulling', name: '启用视锥体剔除' },
  { id: 208, type: 'scene/culling/enableOcclusionCulling', name: '启用遮挡剔除' },
  { id: 209, type: 'scene/optimization/enableBatching', name: '启用批处理' },
  { id: 210, type: 'scene/optimization/enableInstancing', name: '启用实例化' },
  
  // 第8批次：场景环境、粒子系统和地形环境系统节点 (211-240)
  // 场景环境节点 (211-215)
  { id: 211, type: 'scene/skybox/setSkybox', name: '设置天空盒' },
  { id: 212, type: 'scene/fog/enableFog', name: '启用雾效' },
  { id: 213, type: 'scene/fog/setFogColor', name: '设置雾颜色' },
  { id: 214, type: 'scene/fog/setFogDensity', name: '设置雾密度' },
  { id: 215, type: 'scene/environment/setEnvironmentMap', name: '设置环境贴图' },
  
  // 粒子系统节点 (216-230)
  { id: 216, type: 'particles/system/createParticleSystem', name: '创建粒子系统' },
  { id: 217, type: 'particles/emitter/createEmitter', name: '创建发射器' },
  { id: 218, type: 'particles/emitter/setEmissionRate', name: '设置发射速率' },
  { id: 219, type: 'particles/emitter/setEmissionShape', name: '设置发射形状' },
  { id: 220, type: 'particles/particle/setLifetime', name: '设置粒子寿命' },
  { id: 221, type: 'particles/particle/setVelocity', name: '设置粒子速度' },
  { id: 222, type: 'particles/particle/setSize', name: '设置粒子大小' },
  { id: 223, type: 'particles/particle/setColor', name: '设置粒子颜色' },
  { id: 224, type: 'particles/forces/addGravity', name: '添加重力' },
  { id: 225, type: 'particles/forces/addWind', name: '添加风力' },
  { id: 226, type: 'particles/forces/addTurbulence', name: '添加湍流' },
  { id: 227, type: 'particles/collision/enableCollision', name: '启用粒子碰撞' },
  { id: 228, type: 'particles/material/setParticleMaterial', name: '设置粒子材质' },
  { id: 229, type: 'particles/animation/animateSize', name: '动画粒子大小' },
  { id: 230, type: 'particles/animation/animateColor', name: '动画粒子颜色' },
  
  // 地形和环境系统节点 (231-240)
  { id: 231, type: 'terrain/generation/createTerrain', name: '创建地形' },
  { id: 232, type: 'terrain/generation/generateHeightmap', name: '生成高度图' },
  { id: 233, type: 'terrain/generation/applyNoise', name: '应用噪声' },
  { id: 234, type: 'terrain/texture/setTerrainTexture', name: '设置地形纹理' },
  { id: 235, type: 'terrain/texture/blendTextures', name: '混合纹理' },
  { id: 236, type: 'terrain/lod/enableTerrainLOD', name: '启用地形LOD' },
  { id: 237, type: 'terrain/collision/enableTerrainCollision', name: '启用地形碰撞' },
  { id: 238, type: 'water/system/createWaterSurface', name: '创建水面' },
  { id: 239, type: 'water/waves/addWaves', name: '添加波浪' },
  { id: 240, type: 'water/reflection/enableReflection', name: '启用水面反射' }
];

/**
 * 检查NodeRegistryService中是否注册了指定节点类型
 */
function checkNodeRegistration() {
  try {
    // 读取NodeRegistryService文件
    const nodeRegistryPath = path.join(__dirname, 'editor', 'src', 'services', 'NodeRegistryService.ts');
    const content = fs.readFileSync(nodeRegistryPath, 'utf8');
    
    const registeredTypes = new Set();
    
    // 查找所有registerNode调用中的type字段
    const registerNodeRegex = /registerNode\s*\(\s*\{[^}]*type:\s*['"`]([^'"`]+)['"`]/g;
    let match;
    
    while ((match = registerNodeRegex.exec(content)) !== null) {
      registeredTypes.add(match[1]);
    }
    
    return registeredTypes;
  } catch (error) {
    console.error('读取NodeRegistryService文件失败:', error);
    return new Set();
  }
}

/**
 * 验证序号201-240的40个节点
 */
function validateBatch7And8Nodes() {
  console.log('🔍 开始验证序号201-240的40个节点...');
  
  const registeredTypes = checkNodeRegistration();
  
  const results = [];
  let successCount = 0;
  let warningCount = 0;
  let errorCount = 0;

  // 验证每个节点
  BATCH7_8_NODES.forEach(node => {
    const result = {
      nodeId: node.id,
      nodeType: node.type,
      nodeName: node.name,
      editorRegistered: false,
      draggable: false,
      status: 'error',
      issues: []
    };

    // 检查编辑器注册状态
    result.editorRegistered = registeredTypes.has(node.type);
    if (!result.editorRegistered) {
      result.issues.push('未在编辑器中注册');
    }

    // 检查拖拽功能（基于编辑器注册状态）
    result.draggable = result.editorRegistered;
    if (!result.draggable) {
      result.issues.push('无法拖拽使用');
    }

    // 确定状态
    if (result.editorRegistered && result.draggable) {
      result.status = 'success';
      successCount++;
    } else if (result.editorRegistered || result.draggable) {
      result.status = 'warning';
      warningCount++;
    } else {
      result.status = 'error';
      errorCount++;
    }

    results.push(result);
  });

  // 生成建议
  const recommendations = [];
  if (errorCount > 0) {
    recommendations.push(`需要修复 ${errorCount} 个节点的注册问题`);
  }
  if (warningCount > 0) {
    recommendations.push(`需要检查 ${warningCount} 个节点的部分功能`);
  }
  if (successCount === BATCH7_8_NODES.length) {
    recommendations.push('所有节点都已正确注册和集成！');
  }

  const successRate = ((successCount / BATCH7_8_NODES.length) * 100).toFixed(1);

  console.log('\n📊 验证结果统计:');
  console.log(`总计: ${BATCH7_8_NODES.length}个节点`);
  console.log(`✅ 成功: ${successCount}个 (${successRate}%)`);
  console.log(`⚠️ 警告: ${warningCount}个`);
  console.log(`❌ 错误: ${errorCount}个`);

  return {
    summary: {
      total: BATCH7_8_NODES.length,
      success: successCount,
      warning: warningCount,
      error: errorCount,
      successRate: `${successRate}%`
    },
    results,
    recommendations
  };
}

/**
 * 打印详细验证结果
 */
function printDetailedResults(validationResult) {
  console.log('\n📋 详细验证结果:');
  
  // 按批次分组显示
  const batch7Results = validationResult.results.filter(r => r.nodeId >= 201 && r.nodeId <= 210);
  const batch8Results = validationResult.results.filter(r => r.nodeId >= 211 && r.nodeId <= 240);
  
  console.log('\n🎯 第7批次扩展 - 场景管理系统节点 (201-210):');
  batch7Results.forEach(result => {
    const statusIcon = result.status === 'success' ? '✅' : 
                      result.status === 'warning' ? '⚠️' : '❌';
    console.log(`${statusIcon} ${result.nodeId.toString().padStart(3, '0')}. ${result.nodeName}`);
    if (result.issues.length > 0) {
      result.issues.forEach(issue => console.log(`    - ${issue}`));
    }
  });
  
  console.log('\n🎯 第8批次 - 场景环境、粒子系统和地形环境系统节点 (211-240):');
  batch8Results.forEach(result => {
    const statusIcon = result.status === 'success' ? '✅' : 
                      result.status === 'warning' ? '⚠️' : '❌';
    console.log(`${statusIcon} ${result.nodeId.toString().padStart(3, '0')}. ${result.nodeName}`);
    if (result.issues.length > 0) {
      result.issues.forEach(issue => console.log(`    - ${issue}`));
    }
  });

  console.log('\n💡 建议:');
  validationResult.recommendations.forEach(rec => {
    console.log(`- ${rec}`);
  });
}

// 运行验证
const result = validateBatch7And8Nodes();
printDetailedResults(result);

console.log('\n🎯 总结:');
if (result.summary.success === result.summary.total) {
  console.log('✅ 所有40个序号201-240的节点都已正确注册和集成！');
  console.log('✅ 场景管理、粒子系统、地形和环境系统功能已完全可用！');
} else {
  console.log(`❌ 还有 ${result.summary.error} 个节点需要修复注册问题`);
}
