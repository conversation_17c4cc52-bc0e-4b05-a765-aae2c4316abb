{"name": "dl-engine-core", "version": "0.1.0", "description": "DL（Digital Learning）引擎核心库", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.umd.js", "types": "./dist/index.d.ts"}}, "scripts": {"dev": "vite", "build": "vite build && node build-types.js", "preview": "vite preview", "type-check": "tsc --noEmit --project .", "type-check:file": "tsc --noEmit --downlevelIteration", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:core": "vitest run tests/core", "test:physics": "vitest run tests/physics", "test:rendering": "vitest run tests/rendering", "test:input": "vitest run tests/input", "test:scene": "vitest run tests/scene", "test:network": "vitest run tests/network", "test:interaction": "vitest run tests/interaction", "test:visualscript": "vitest run tests/visualscript", "test:avatar": "vitest run tests/avatar", "test:mocap": "vitest run tests/mocap", "test:compatibility": "ts-node tests/compatibility/index.ts", "test:compatibility:core": "ts-node tests/compatibility/index.ts core", "test:compatibility:physics": "ts-node tests/compatibility/index.ts physics", "test:compatibility:rendering": "ts-node tests/compatibility/index.ts rendering", "test:compatibility:animation": "ts-node tests/compatibility/index.ts animation", "test:compatibility:input": "ts-node tests/compatibility/index.ts input", "test:compatibility:scene": "ts-node tests/compatibility/index.ts scene", "test:compatibility:network": "ts-node tests/compatibility/index.ts network", "test:compatibility:visualscript": "ts-node tests/compatibility/index.ts visualscript", "test:compatibility:interaction": "ts-node tests/compatibility/index.ts interaction", "test:compatibility:avatar": "ts-node tests/compatibility/index.ts avatar", "test:compatibility:mocap": "ts-node tests/compatibility/index.ts mocap", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "docs": "typedoc --out docs src"}, "keywords": ["engine", "3d", "webgl", "threejs"], "author": "IR Engine Team", "license": "MIT", "dependencies": {"@msgpack/msgpack": "^3.1.2", "cannon-es": "^0.20.0", "cbor-web": "^10.0.3", "i18next": "^22.4.15", "lz-string": "^1.5.0", "three": "^0.152.2"}, "devDependencies": {"@types/cannon": "^0.1.13", "@types/node": "^20.17.50", "@types/three": "^0.152.0", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "@vitest/coverage-c8": "^0.31.0", "@vitest/ui": "^0.31.0", "eslint": "^8.40.0", "jsdom": "^22.0.0", "ts-node": "^10.9.1", "typedoc": "^0.24.7", "typescript": "^5.0.4", "vite": "^4.3.5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^0.31.0"}}