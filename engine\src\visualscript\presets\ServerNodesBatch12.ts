﻿/**
 * 第12批次：服务器集成扩展节点（331-350）
 * 包含资产服务扩展和协作服务节点
 */

import { Node } from '../Node';

// ==================== 资产服务扩展节点 (331-340) ====================

/**
 * 331. 更新资产信息节点
 */
export class UpdateAssetInfoNode extends Node {
  constructor() {
    super('server/asset/updateAssetInfo', '更新资产信息', '更新资产元数据');
    this.addInput('assetId', 'string', '资产ID');
    this.addInput('name', 'string', '资产名称');
    this.addInput('description', 'string', '资产描述');
    this.addInput('tags', 'array', '标签列表');
    this.addInput('metadata', 'object', '元数据');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('assetInfo', 'object', '更新后的资产信息');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var assetId = inputs.assetId;
      var name = inputs.name;
      var description = inputs.description;
      var tags = inputs.tags;
      var metadata = inputs.metadata;
      var userId = inputs.userId;

      var updatedAssetInfo = {
        id: assetId,
        name: name || ('资产 ' + assetId),
        description: description || '资产描述',
        tags: tags || [],
        metadata: metadata || {},
        updatedAt: new Date().toISOString(),
        updatedBy: userId
      };

      console.log('更新资产信息: ' + assetId + ' by ' + userId);

      return {
        success: true,
        assetInfo: updatedAssetInfo,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        assetInfo: null,
        error: error instanceof Error ? error.message : '更新失败'
      };
    }
  }
}

/**
 * 332. 移动资产到文件夹节点
 */
export class MoveAssetToFolderNode extends Node {
  constructor() {
    super('server/asset/moveAssetToFolder', '移动资产到文件夹', '组织资产文件结构');
    this.addInput('assetId', 'string', '资产ID');
    this.addInput('targetFolderId', 'string', '目标文件夹ID');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('newPath', 'string', '新路径');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var assetId = inputs.assetId;
      var targetFolderId = inputs.targetFolderId;
      var userId = inputs.userId;

      var newPath = '/' + targetFolderId + '/' + assetId;

      console.log('移动资产: ' + assetId + ' 到文件夹 ' + targetFolderId + ' by ' + userId);

      return {
        success: true,
        newPath: newPath,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        newPath: null,
        error: error instanceof Error ? error.message : '移动失败'
      };
    }
  }
}

/**
 * 333. 创建资产文件夹节点
 */
export class CreateAssetFolderNode extends Node {
  constructor() {
    super('server/asset/createAssetFolder', '创建资产文件夹', '创建资产组织文件夹');
    this.addInput('name', 'string', '文件夹名称');
    this.addInput('parentFolderId', 'string', '父文件夹ID');
    this.addInput('description', 'string', '文件夹描述');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('folderId', 'string', '文件夹ID');
    this.addOutput('folderPath', 'string', '文件夹路径');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var name = inputs.name;
      var parentFolderId = inputs.parentFolderId;
      var description = inputs.description;
      var userId = inputs.userId;

      var folderId = 'folder_' + Date.now();
      var folderPath = parentFolderId ? ('/' + parentFolderId + '/' + name) : ('/' + name);

      console.log('创建资产文件夹: ' + name + ' by ' + userId);

      return {
        success: true,
        folderId: folderId,
        folderPath: folderPath,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        folderId: null,
        folderPath: null,
        error: error instanceof Error ? error.message : '创建失败'
      };
    }
  }
}

/**
 * 334. 删除资产文件夹节点
 */
export class DeleteAssetFolderNode extends Node {
  constructor() {
    super('server/asset/deleteAssetFolder', '删除资产文件夹', '删除资产文件夹');
    this.addInput('folderId', 'string', '文件夹ID');
    this.addInput('force', 'boolean', '强制删除');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('deletedCount', 'number', '删除的文件数量');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var folderId = inputs.folderId;
      var force = inputs.force;
      var userId = inputs.userId;

      var deletedCount = force ? Math.floor(Math.random() * 10) : 0;

      console.log('删除资产文件夹: ' + folderId + ' (force=' + force + ') by ' + userId);

      return {
        success: true,
        deletedCount: deletedCount,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        deletedCount: 0,
        error: error instanceof Error ? error.message : '删除失败'
      };
    }
  }
}

/**
 * 335. 分享资产节点
 */
export class ShareAssetNode extends Node {
  constructor() {
    super('server/asset/shareAsset', '分享资产', '分享资产给其他用户');
    this.addInput('assetId', 'string', '资产ID');
    this.addInput('targetUserId', 'string', '目标用户ID');
    this.addInput('permission', 'string', '权限级别');
    this.addInput('message', 'string', '分享消息');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('shareId', 'string', '分享ID');
    this.addOutput('shareUrl', 'string', '分享链接');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var assetId = inputs.assetId;
      var targetUserId = inputs.targetUserId;
      var permission = inputs.permission;
      var message = inputs.message;
      var userId = inputs.userId;

      var shareId = 'share_' + Date.now();
      var shareUrl = 'https://example.com/shared/' + shareId;

      console.log('分享资产: ' + assetId + ' 给用户 ' + targetUserId + ' by ' + userId);

      return {
        success: true,
        shareId: shareId,
        shareUrl: shareUrl,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        shareId: null,
        shareUrl: null,
        error: error instanceof Error ? error.message : '分享失败'
      };
    }
  }
}

/**
 * 336. 获取资产版本节点
 */
export class GetAssetVersionsNode extends Node {
  constructor() {
    super('server/asset/getAssetVersions', '获取资产版本', '获取资产历史版本');
    this.addInput('assetId', 'string', '资产ID');
    this.addInput('limit', 'number', '版本数量限制');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('versions', 'array', '版本列表');
    this.addOutput('currentVersion', 'string', '当前版本');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var assetId = inputs.assetId;
      var limit = inputs.limit || 10;
      var userId = inputs.userId;

      // 模拟版本列表
      var versions = [];
      var versionCount = Math.min(limit, 5);
      for (var i = 0; i < versionCount; i++) {
        versions.push({
          version: 'v1.' + (i + 1),
          createdAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
          size: Math.floor(Math.random() * 1000000),
          description: '版本 ' + (i + 1) + ' 更新'
        });
      }

      var currentVersion = versions.length > 0 ? versions[0].version : 'v1.0';

      console.log('获取资产版本: ' + assetId + ' by ' + userId);

      return {
        success: true,
        versions: versions,
        currentVersion: currentVersion,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        versions: [],
        currentVersion: null,
        error: error instanceof Error ? error.message : '获取失败'
      };
    }
  }
}

/**
 * 337. 创建资产版本节点
 */
export class CreateAssetVersionNode extends Node {
  constructor() {
    super('server/asset/createAssetVersion', '创建资产版本', '创建新的资产版本');
    this.addInput('assetId', 'string', '资产ID');
    this.addInput('versionName', 'string', '版本名称');
    this.addInput('description', 'string', '版本描述');
    this.addInput('fileData', 'object', '文件数据');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('versionId', 'string', '版本ID');
    this.addOutput('version', 'string', '版本号');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var assetId = inputs.assetId;
      var versionName = inputs.versionName;
      var description = inputs.description;
      var fileData = inputs.fileData;
      var userId = inputs.userId;

      var versionId = 'version_' + Date.now();
      var version = versionName || ('v' + Date.now());

      console.log('创建资产版本: ' + assetId + ' ' + version + ' by ' + userId);

      return {
        success: true,
        versionId: versionId,
        version: version,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        versionId: null,
        version: null,
        error: error instanceof Error ? error.message : '创建失败'
      };
    }
  }
}

/**
 * 338. 恢复资产版本节点
 */
export class RestoreAssetVersionNode extends Node {
  constructor() {
    super('server/asset/restoreAssetVersion', '恢复资产版本', '恢复到指定资产版本');
    this.addInput('assetId', 'string', '资产ID');
    this.addInput('versionId', 'string', '版本ID');
    this.addInput('createBackup', 'boolean', '创建备份');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('restoredVersion', 'string', '恢复的版本');
    this.addOutput('backupVersion', 'string', '备份版本');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var assetId = inputs.assetId;
      var versionId = inputs.versionId;
      var createBackup = inputs.createBackup;
      var userId = inputs.userId;

      var restoredVersion = 'v' + Date.now();
      var backupVersion = createBackup ? ('backup_' + Date.now()) : null;

      console.log('恢复资产版本: ' + assetId + ' 到 ' + versionId + ' by ' + userId);

      return {
        success: true,
        restoredVersion: restoredVersion,
        backupVersion: backupVersion,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        restoredVersion: null,
        backupVersion: null,
        error: error instanceof Error ? error.message : '恢复失败'
      };
    }
  }
}

/**
 * 339. 生成资产缩略图节点
 */
export class GenerateAssetThumbnailNode extends Node {
  constructor() {
    super('server/asset/generateAssetThumbnail', '生成资产缩略图', '服务器生成资产预览图');
    this.addInput('assetId', 'string', '资产ID');
    this.addInput('size', 'string', '缩略图尺寸');
    this.addInput('quality', 'number', '图片质量');
    this.addInput('format', 'string', '输出格式');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('thumbnailUrl', 'string', '缩略图URL');
    this.addOutput('thumbnailSize', 'number', '缩略图大小');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var assetId = inputs.assetId;
      var size = inputs.size || '256x256';
      var quality = inputs.quality || 80;
      var format = inputs.format || 'jpg';
      var userId = inputs.userId;

      var thumbnailUrl = 'https://example.com/thumbnails/' + assetId + '_' + size + '.' + format;
      var thumbnailSize = Math.floor(Math.random() * 50000) + 10000;

      console.log('生成资产缩略图: ' + assetId + ' ' + size + ' by ' + userId);

      return {
        success: true,
        thumbnailUrl: thumbnailUrl,
        thumbnailSize: thumbnailSize,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        thumbnailUrl: null,
        thumbnailSize: 0,
        error: error instanceof Error ? error.message : '生成失败'
      };
    }
  }
}

/**
 * 340. 优化资产节点
 */
export class OptimizeAssetNode extends Node {
  constructor() {
    super('server/asset/optimizeAsset', '优化资产', '服务器端资产优化处理');
    this.addInput('assetId', 'string', '资产ID');
    this.addInput('optimizationType', 'string', '优化类型');
    this.addInput('quality', 'number', '优化质量');
    this.addInput('preserveMetadata', 'boolean', '保留元数据');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('optimizedAssetId', 'string', '优化后资产ID');
    this.addOutput('originalSize', 'number', '原始大小');
    this.addOutput('optimizedSize', 'number', '优化后大小');
    this.addOutput('compressionRatio', 'number', '压缩比');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var assetId = inputs.assetId;
      var optimizationType = inputs.optimizationType;
      var quality = inputs.quality || 80;
      var preserveMetadata = inputs.preserveMetadata;
      var userId = inputs.userId;

      var optimizedAssetId = assetId + '_optimized';
      var originalSize = Math.floor(Math.random() * 1000000) + 100000;
      var optimizedSize = Math.floor(originalSize * (quality / 100));
      var compressionRatio = parseFloat(((originalSize - optimizedSize) / originalSize * 100).toFixed(2));

      console.log('优化资产: ' + assetId + ' 类型=' + optimizationType + ' by ' + userId);

      return {
        success: true,
        optimizedAssetId: optimizedAssetId,
        originalSize: originalSize,
        optimizedSize: optimizedSize,
        compressionRatio: compressionRatio,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        optimizedAssetId: null,
        originalSize: 0,
        optimizedSize: 0,
        compressionRatio: 0,
        error: error instanceof Error ? error.message : '优化失败'
      };
    }
  }
}

// ==================== 协作服务节点 (341-350) ====================

/**
 * 341. 加入协作房间节点
 */
export class JoinCollaborationRoomNode extends Node {
  constructor() {
    super('server/collaboration/joinRoom', '加入协作房间', '加入项目协作房间');
    this.addInput('projectId', 'string', '项目ID');
    this.addInput('userId', 'string', '用户ID');
    this.addInput('userName', 'string', '用户名称');
    this.addInput('role', 'string', '用户角色');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('roomId', 'string', '房间ID');
    this.addOutput('sessionId', 'string', '会话ID');
    this.addOutput('onlineUsers', 'array', '在线用户列表');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var projectId = inputs.projectId;
      var userId = inputs.userId;
      var userName = inputs.userName;
      var role = inputs.role;

      var roomId = 'room_' + projectId;
      var sessionId = 'session_' + Date.now();

      // 模拟在线用户列表
      var onlineUsers = [
        { id: userId, name: userName, role: role, joinedAt: new Date().toISOString() },
        { id: 'user_1', name: '用户1', role: 'editor', joinedAt: new Date(Date.now() - 60000).toISOString() },
        { id: 'user_2', name: '用户2', role: 'viewer', joinedAt: new Date(Date.now() - 120000).toISOString() }
      ];

      console.log('用户 ' + userName + ' 加入协作房间: ' + roomId);

      return {
        success: true,
        roomId: roomId,
        sessionId: sessionId,
        onlineUsers: onlineUsers,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        roomId: null,
        sessionId: null,
        onlineUsers: [],
        error: error instanceof Error ? error.message : '加入失败'
      };
    }
  }
}

/**
 * 342. 离开协作房间节点
 */
export class LeaveCollaborationRoomNode extends Node {
  constructor() {
    super('server/collaboration/leaveRoom', '离开协作房间', '离开项目协作房间');
    this.addInput('roomId', 'string', '房间ID');
    this.addInput('sessionId', 'string', '会话ID');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('leftAt', 'string', '离开时间');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var roomId = inputs.roomId;
      var sessionId = inputs.sessionId;
      var userId = inputs.userId;

      var leftAt = new Date().toISOString();

      console.log('用户 ' + userId + ' 离开协作房间: ' + roomId);

      return {
        success: true,
        leftAt: leftAt,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        leftAt: null,
        error: error instanceof Error ? error.message : '离开失败'
      };
    }
  }
}

/**
 * 343. 发送协作操作节点
 */
export class SendCollaborationOperationNode extends Node {
  constructor() {
    super('server/collaboration/sendOperation', '发送协作操作', '发送编辑操作到其他用户');
    this.addInput('roomId', 'string', '房间ID');
    this.addInput('operation', 'object', '操作数据');
    this.addInput('operationType', 'string', '操作类型');
    this.addInput('targetResource', 'string', '目标资源');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('operationId', 'string', '操作ID');
    this.addOutput('timestamp', 'string', '时间戳');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var roomId = inputs.roomId;
      var operation = inputs.operation;
      var operationType = inputs.operationType;
      var targetResource = inputs.targetResource;
      var userId = inputs.userId;

      var operationId = 'op_' + Date.now();
      var timestamp = new Date().toISOString();

      console.log('发送协作操作: ' + operationType + ' 在房间 ' + roomId + ' by ' + userId);

      return {
        success: true,
        operationId: operationId,
        timestamp: timestamp,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        operationId: null,
        timestamp: null,
        error: error instanceof Error ? error.message : '发送失败'
      };
    }
  }
}

/**
 * 344. 接收协作操作节点
 */
export class ReceiveCollaborationOperationNode extends Node {
  constructor() {
    super('server/collaboration/receiveOperation', '接收协作操作', '接收其他用户的编辑操作');
    this.addInput('roomId', 'string', '房间ID');
    this.addInput('userId', 'string', '用户ID');
    this.addInput('lastOperationId', 'string', '最后操作ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('operations', 'array', '操作列表');
    this.addOutput('hasMore', 'boolean', '是否有更多');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var roomId = inputs.roomId;
      var userId = inputs.userId;
      var lastOperationId = inputs.lastOperationId;

      // 模拟接收到的操作列表
      var operations = [
        {
          id: 'op_' + (Date.now() - 1000),
          type: 'edit',
          resource: 'scene/entity_1',
          data: { position: { x: 10, y: 20, z: 30 } },
          userId: 'user_1',
          timestamp: new Date(Date.now() - 1000).toISOString()
        },
        {
          id: 'op_' + (Date.now() - 500),
          type: 'create',
          resource: 'scene/entity_2',
          data: { type: 'cube', material: 'default' },
          userId: 'user_2',
          timestamp: new Date(Date.now() - 500).toISOString()
        }
      ];

      console.log('接收协作操作: 房间 ' + roomId + ' by ' + userId);

      return {
        success: true,
        operations: operations,
        hasMore: false,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        operations: [],
        hasMore: false,
        error: error instanceof Error ? error.message : '接收失败'
      };
    }
  }
}

/**
 * 345. 解决编辑冲突节点
 */
export class ResolveCollaborationConflictNode extends Node {
  constructor() {
    super('server/collaboration/resolveConflict', '解决编辑冲突', '自动解决编辑冲突');
    this.addInput('conflictId', 'string', '冲突ID');
    this.addInput('localOperation', 'object', '本地操作');
    this.addInput('remoteOperation', 'object', '远程操作');
    this.addInput('resolutionStrategy', 'string', '解决策略');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('resolvedOperation', 'object', '解决后的操作');
    this.addOutput('resolutionType', 'string', '解决类型');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var conflictId = inputs.conflictId;
      var localOperation = inputs.localOperation;
      var remoteOperation = inputs.remoteOperation;
      var resolutionStrategy = inputs.resolutionStrategy;
      var userId = inputs.userId;

      // 简单的冲突解决逻辑
      var resolvedOperation;
      var resolutionType;

      if (resolutionStrategy === 'local_wins') {
        resolvedOperation = localOperation;
        resolutionType = '本地优先';
      } else if (resolutionStrategy === 'remote_wins') {
        resolvedOperation = remoteOperation;
        resolutionType = '远程优先';
      } else if (resolutionStrategy === 'merge') {
        resolvedOperation = {};
        for (var key in remoteOperation) {
          if (remoteOperation.hasOwnProperty(key)) {
            resolvedOperation[key] = remoteOperation[key];
          }
        }
        for (var key in localOperation) {
          if (localOperation.hasOwnProperty(key)) {
            resolvedOperation[key] = localOperation[key];
          }
        }
        resolutionType = '合并操作';
      } else {
        resolvedOperation = remoteOperation;
        resolutionType = '默认远程优先';
      }

      console.log('解决编辑冲突: ' + conflictId + ' 策略=' + resolutionStrategy + ' by ' + userId);

      return {
        success: true,
        resolvedOperation: resolvedOperation,
        resolutionType: resolutionType,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        resolvedOperation: null,
        resolutionType: null,
        error: error instanceof Error ? error.message : '解决失败'
      };
    }
  }
}

/**
 * 346. 获取在线用户节点
 */
export class GetOnlineUsersNode extends Node {
  constructor() {
    super('server/collaboration/getOnlineUsers', '获取在线用户', '获取当前在线协作用户');
    this.addInput('roomId', 'string', '房间ID');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('onlineUsers', 'array', '在线用户列表');
    this.addOutput('totalCount', 'number', '总用户数');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var roomId = inputs.roomId;
      var userId = inputs.userId;

      // 模拟在线用户列表
      var onlineUsers = [
        {
          id: 'user_1',
          name: '张三',
          role: 'editor',
          avatar: 'https://example.com/avatar1.jpg',
          status: 'active',
          lastActivity: new Date().toISOString()
        },
        {
          id: 'user_2',
          name: '李四',
          role: 'viewer',
          avatar: 'https://example.com/avatar2.jpg',
          status: 'idle',
          lastActivity: new Date(Date.now() - 60000).toISOString()
        },
        {
          id: userId,
          name: '当前用户',
          role: 'owner',
          avatar: 'https://example.com/avatar3.jpg',
          status: 'active',
          lastActivity: new Date().toISOString()
        }
      ];

      console.log('获取在线用户: 房间 ' + roomId + ' by ' + userId);

      return {
        success: true,
        onlineUsers: onlineUsers,
        totalCount: onlineUsers.length,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        onlineUsers: [],
        totalCount: 0,
        error: error instanceof Error ? error.message : '获取失败'
      };
    }
  }
}

/**
 * 347. 广播消息节点
 */
export class BroadcastCollaborationMessageNode extends Node {
  constructor() {
    super('server/collaboration/broadcastMessage', '广播消息', '向所有协作用户广播消息');
    this.addInput('roomId', 'string', '房间ID');
    this.addInput('message', 'string', '消息内容');
    this.addInput('messageType', 'string', '消息类型');
    this.addInput('targetUsers', 'array', '目标用户');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('messageId', 'string', '消息ID');
    this.addOutput('deliveredCount', 'number', '送达用户数');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var roomId = inputs.roomId;
      var message = inputs.message;
      var messageType = inputs.messageType;
      var targetUsers = inputs.targetUsers;
      var userId = inputs.userId;

      var messageId = 'msg_' + Date.now();
      var deliveredCount = targetUsers ? targetUsers.length : Math.floor(Math.random() * 5) + 1;

      console.log('广播消息: ' + messageType + ' 在房间 ' + roomId + ' by ' + userId);

      return {
        success: true,
        messageId: messageId,
        deliveredCount: deliveredCount,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        messageId: null,
        deliveredCount: 0,
        error: error instanceof Error ? error.message : '广播失败'
      };
    }
  }
}

/**
 * 348. 锁定资源节点
 */
export class LockCollaborationResourceNode extends Node {
  constructor() {
    super('server/collaboration/lockResource', '锁定资源', '锁定编辑资源防止冲突');
    this.addInput('resourceId', 'string', '资源ID');
    this.addInput('resourceType', 'string', '资源类型');
    this.addInput('lockDuration', 'number', '锁定时长');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('lockId', 'string', '锁定ID');
    this.addOutput('expiresAt', 'string', '过期时间');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var resourceId = inputs.resourceId;
      var resourceType = inputs.resourceType;
      var lockDuration = inputs.lockDuration || 300;
      var userId = inputs.userId;

      var lockId = 'lock_' + Date.now();
      var expiresAt = new Date(Date.now() + lockDuration * 1000).toISOString();

      console.log('锁定资源: ' + resourceType + '/' + resourceId + ' by ' + userId);

      return {
        success: true,
        lockId: lockId,
        expiresAt: expiresAt,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        lockId: null,
        expiresAt: null,
        error: error instanceof Error ? error.message : '锁定失败'
      };
    }
  }
}

/**
 * 349. 解锁资源节点
 */
export class UnlockCollaborationResourceNode extends Node {
  constructor() {
    super('server/collaboration/unlockResource', '解锁资源', '解锁编辑资源');
    this.addInput('lockId', 'string', '锁定ID');
    this.addInput('resourceId', 'string', '资源ID');
    this.addInput('force', 'boolean', '强制解锁');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('unlockedAt', 'string', '解锁时间');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var lockId = inputs.lockId;
      var resourceId = inputs.resourceId;
      var force = inputs.force;
      var userId = inputs.userId;

      var unlockedAt = new Date().toISOString();

      console.log('解锁资源: ' + resourceId + ' (lockId=' + lockId + ') by ' + userId);

      return {
        success: true,
        unlockedAt: unlockedAt,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        unlockedAt: null,
        error: error instanceof Error ? error.message : '解锁失败'
      };
    }
  }
}

/**
 * 350. 同步状态节点
 */
export class SyncCollaborationStateNode extends Node {
  constructor() {
    super('server/collaboration/syncState', '同步状态', '同步协作状态到所有用户');
    this.addInput('roomId', 'string', '房间ID');
    this.addInput('stateData', 'object', '状态数据');
    this.addInput('syncType', 'string', '同步类型');
    this.addInput('targetUsers', 'array', '目标用户');
    this.addInput('userId', 'string', '用户ID');
    this.addOutput('success', 'boolean', '是否成功');
    this.addOutput('syncId', 'string', '同步ID');
    this.addOutput('syncedUsers', 'array', '已同步用户');
    this.addOutput('timestamp', 'string', '同步时间');
    this.addOutput('error', 'string', '错误信息');
  }

  execute(inputs: any): any {
    try {
      var roomId = inputs.roomId;
      var stateData = inputs.stateData;
      var syncType = inputs.syncType;
      var targetUsers = inputs.targetUsers;
      var userId = inputs.userId;

      var syncId = 'sync_' + Date.now();
      var timestamp = new Date().toISOString();

      // 模拟已同步用户列表
      var syncedUsers = targetUsers || ['user_1', 'user_2', 'user_3'];

      console.log('同步状态: ' + syncType + ' 在房间 ' + roomId + ' by ' + userId);

      return {
        success: true,
        syncId: syncId,
        syncedUsers: syncedUsers,
        timestamp: timestamp,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        syncId: null,
        syncedUsers: [],
        timestamp: null,
        error: error instanceof Error ? error.message : '同步失败'
      };
    }
  }
}
