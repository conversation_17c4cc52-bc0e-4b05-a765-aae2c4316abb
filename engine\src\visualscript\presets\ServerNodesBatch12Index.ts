/**
 * 第12批次：服务器集成扩展节点统一导出
 * 节点331-350：完整的20个节点实现
 */

// 资产服务扩展节点 (331-340)
export {
  UpdateAssetInfoNode,
  MoveAssetToFolderNode,
  CreateAssetFolderNode,
  DeleteAssetFolderNode,
  ShareAssetNode,
  GetAssetVersionsNode,
  CreateAssetVersionNode,
  RestoreAssetVersionNode,
  GenerateAssetThumbnailNode,
  OptimizeAssetNode
} from './ServerNodesBatch12';

// 协作服务节点 (341-350)
export {
  JoinCollaborationRoomNode,
  LeaveCollaborationRoomNode,
  SendCollaborationOperationNode,
  ReceiveCollaborationOperationNode,
  ResolveCollaborationConflictNode,
  GetOnlineUsersNode,
  BroadcastCollaborationMessageNode,
  LockCollaborationResourceNode,
  UnlockCollaborationResourceNode,
  SyncCollaborationStateNode
} from './ServerNodesBatch12';

/**
 * 第12批次节点映射表
 * 用于节点注册和类型检查
 */
export const BATCH12_NODE_MAPPING = {
  // 资产服务扩展节点 (331-340)
  'server/asset/updateAssetInfo': 'UpdateAssetInfoNode',
  'server/asset/moveAssetToFolder': 'MoveAssetToFolderNode',
  'server/asset/createAssetFolder': 'CreateAssetFolderNode',
  'server/asset/deleteAssetFolder': 'DeleteAssetFolderNode',
  'server/asset/shareAsset': 'ShareAssetNode',
  'server/asset/getAssetVersions': 'GetAssetVersionsNode',
  'server/asset/createAssetVersion': 'CreateAssetVersionNode',
  'server/asset/restoreAssetVersion': 'RestoreAssetVersionNode',
  'server/asset/generateAssetThumbnail': 'GenerateAssetThumbnailNode',
  'server/asset/optimizeAsset': 'OptimizeAssetNode',

  // 协作服务节点 (341-350)
  'server/collaboration/joinRoom': 'JoinCollaborationRoomNode',
  'server/collaboration/leaveRoom': 'LeaveCollaborationRoomNode',
  'server/collaboration/sendOperation': 'SendCollaborationOperationNode',
  'server/collaboration/receiveOperation': 'ReceiveCollaborationOperationNode',
  'server/collaboration/resolveConflict': 'ResolveCollaborationConflictNode',
  'server/collaboration/getOnlineUsers': 'GetOnlineUsersNode',
  'server/collaboration/broadcastMessage': 'BroadcastCollaborationMessageNode',
  'server/collaboration/lockResource': 'LockCollaborationResourceNode',
  'server/collaboration/unlockResource': 'UnlockCollaborationResourceNode',
  'server/collaboration/syncState': 'SyncCollaborationStateNode'
};

/**
 * 第12批次节点配置信息
 * 用于编辑器注册和显示
 */
export const BATCH12_NODE_CONFIGS = {
  // 资产服务扩展节点 (331-340)
  'UpdateAssetInfoNode': {
    id: 331,
    label: '更新资产信息',
    category: 'NETWORK',
    description: '更新资产元数据',
    icon: 'edit',
    color: '#52c41a',
    tags: ['服务器', '资产', '更新']
  },
  'MoveAssetToFolderNode': {
    id: 332,
    label: '移动资产到文件夹',
    category: 'NETWORK',
    description: '组织资产文件结构',
    icon: 'folder-move',
    color: '#52c41a',
    tags: ['服务器', '资产', '移动']
  },
  'CreateAssetFolderNode': {
    id: 333,
    label: '创建资产文件夹',
    category: 'NETWORK',
    description: '创建资产组织文件夹',
    icon: 'folder-add',
    color: '#52c41a',
    tags: ['服务器', '资产', '创建']
  },
  'DeleteAssetFolderNode': {
    id: 334,
    label: '删除资产文件夹',
    category: 'NETWORK',
    description: '删除资产文件夹',
    icon: 'folder-delete',
    color: '#52c41a',
    tags: ['服务器', '资产', '删除']
  },
  'ShareAssetNode': {
    id: 335,
    label: '分享资产',
    category: 'NETWORK',
    description: '分享资产给其他用户',
    icon: 'share-alt',
    color: '#52c41a',
    tags: ['服务器', '资产', '分享']
  },
  'GetAssetVersionsNode': {
    id: 336,
    label: '获取资产版本',
    category: 'NETWORK',
    description: '获取资产历史版本',
    icon: 'history',
    color: '#52c41a',
    tags: ['服务器', '资产', '版本']
  },
  'CreateAssetVersionNode': {
    id: 337,
    label: '创建资产版本',
    category: 'NETWORK',
    description: '创建新的资产版本',
    icon: 'plus-circle',
    color: '#52c41a',
    tags: ['服务器', '资产', '版本']
  },
  'RestoreAssetVersionNode': {
    id: 338,
    label: '恢复资产版本',
    category: 'NETWORK',
    description: '恢复到指定资产版本',
    icon: 'rollback',
    color: '#52c41a',
    tags: ['服务器', '资产', '恢复']
  },
  'GenerateAssetThumbnailNode': {
    id: 339,
    label: '生成资产缩略图',
    category: 'NETWORK',
    description: '服务器生成资产预览图',
    icon: 'picture',
    color: '#52c41a',
    tags: ['服务器', '资产', '缩略图']
  },
  'OptimizeAssetNode': {
    id: 340,
    label: '优化资产',
    category: 'NETWORK',
    description: '服务器端资产优化处理',
    icon: 'thunderbolt',
    color: '#52c41a',
    tags: ['服务器', '资产', '优化']
  },

  // 协作服务节点 (341-350)
  'JoinCollaborationRoomNode': {
    id: 341,
    label: '加入协作房间',
    category: 'NETWORK',
    description: '加入项目协作房间',
    icon: 'team',
    color: '#1890ff',
    tags: ['服务器', '协作', '房间']
  },
  'LeaveCollaborationRoomNode': {
    id: 342,
    label: '离开协作房间',
    category: 'NETWORK',
    description: '离开项目协作房间',
    icon: 'logout',
    color: '#1890ff',
    tags: ['服务器', '协作', '房间']
  },
  'SendCollaborationOperationNode': {
    id: 343,
    label: '发送协作操作',
    category: 'NETWORK',
    description: '发送编辑操作到其他用户',
    icon: 'send',
    color: '#1890ff',
    tags: ['服务器', '协作', '操作']
  },
  'ReceiveCollaborationOperationNode': {
    id: 344,
    label: '接收协作操作',
    category: 'NETWORK',
    description: '接收其他用户的编辑操作',
    icon: 'inbox',
    color: '#1890ff',
    tags: ['服务器', '协作', '操作']
  },
  'ResolveCollaborationConflictNode': {
    id: 345,
    label: '解决编辑冲突',
    category: 'NETWORK',
    description: '自动解决编辑冲突',
    icon: 'branches',
    color: '#1890ff',
    tags: ['服务器', '协作', '冲突']
  },
  'GetOnlineUsersNode': {
    id: 346,
    label: '获取在线用户',
    category: 'NETWORK',
    description: '获取当前在线协作用户',
    icon: 'user',
    color: '#1890ff',
    tags: ['服务器', '协作', '用户']
  },
  'BroadcastCollaborationMessageNode': {
    id: 347,
    label: '广播消息',
    category: 'NETWORK',
    description: '向所有协作用户广播消息',
    icon: 'sound',
    color: '#1890ff',
    tags: ['服务器', '协作', '消息']
  },
  'LockCollaborationResourceNode': {
    id: 348,
    label: '锁定资源',
    category: 'NETWORK',
    description: '锁定编辑资源防止冲突',
    icon: 'lock',
    color: '#1890ff',
    tags: ['服务器', '协作', '锁定']
  },
  'UnlockCollaborationResourceNode': {
    id: 349,
    label: '解锁资源',
    category: 'NETWORK',
    description: '解锁编辑资源',
    icon: 'unlock',
    color: '#1890ff',
    tags: ['服务器', '协作', '解锁']
  },
  'SyncCollaborationStateNode': {
    id: 350,
    label: '同步状态',
    category: 'NETWORK',
    description: '同步协作状态到所有用户',
    icon: 'sync',
    color: '#1890ff',
    tags: ['服务器', '协作', '同步']
  }
};

/**
 * 第12批次节点总数
 */
export const BATCH12_NODE_COUNT = 20;

/**
 * 第12批次节点范围
 */
export const BATCH12_NODE_RANGE = { start: 331, end: 350 };
