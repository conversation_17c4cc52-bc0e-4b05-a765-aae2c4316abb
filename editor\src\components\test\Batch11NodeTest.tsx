/**
 * 第11批次节点测试组件
 * 验证服务器端功能节点（301-330）的拖拽创建和基本功能
 */

import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Typography, Divider, Tag, Alert, List } from 'antd';
import { nodeRegistryService } from '../../services/NodeRegistryService';

const { Title, Text, Paragraph } = Typography;

interface NodeTestResult {
  type: string;
  label: string;
  registered: boolean;
  category: string;
  tags: string[];
}

export const Batch11NodeTest: React.FC = () => {
  const [testResults, setTestResults] = useState<NodeTestResult[]>([]);
  const [loading, setLoading] = useState(false);

  // 第11批次节点类型列表
  const batch11NodeTypes = [
    // 用户服务节点 (301-310)
    'server/user/registerUser',
    'server/user/loginUser',
    'server/user/logoutUser',
    'server/user/updateUserProfile',
    'server/user/changePassword',
    'server/user/resetPassword',
    'server/user/getUserInfo',
    'server/user/deleteUser',
    'server/user/setUserRole',
    'server/user/validateToken',
    
    // 项目服务节点 (311-325)
    'server/project/createProject',
    'server/project/deleteProject',
    'server/project/updateProject',
    'server/project/getProjectList',
    'server/project/getProjectDetails',
    'server/project/shareProject',
    'server/project/unshareProject',
    'server/project/setProjectPermission',
    'server/project/forkProject',
    'server/project/archiveProject',
    'server/project/restoreProject',
    'server/project/exportProjectData',
    'server/project/importProjectData',
    'server/project/getProjectStats',
    'server/project/backupProject',
    
    // 资产服务节点 (326-330)
    'server/asset/uploadAsset',
    'server/asset/downloadAsset',
    'server/asset/deleteAsset',
    'server/asset/getAssetList',
    'server/asset/getAssetInfo'
  ];

  // 测试节点注册状态
  const testNodeRegistration = () => {
    setLoading(true);
    
    try {
      const results: NodeTestResult[] = batch11NodeTypes.map(nodeType => {
        const nodeInfo = nodeRegistryService.getNode(nodeType);
        
        return {
          type: nodeType,
          label: nodeInfo?.label || '未知',
          registered: !!nodeInfo,
          category: nodeInfo?.category || '未知',
          tags: nodeInfo?.tags || []
        };
      });
      
      setTestResults(results);
    } catch (error) {
      console.error('测试节点注册失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时自动测试
  useEffect(() => {
    testNodeRegistration();
  }, []);

  // 计算统计信息
  const registeredCount = testResults.filter(result => result.registered).length;
  const totalCount = testResults.length;
  const registrationRate = totalCount > 0 ? (registeredCount / totalCount * 100).toFixed(1) : '0';

  // 按功能分组
  const userServiceNodes = testResults.filter(result => result.type.includes('/user/'));
  const projectServiceNodes = testResults.filter(result => result.type.includes('/project/'));
  const assetServiceNodes = testResults.filter(result => result.type.includes('/asset/'));

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>第11批次节点测试</Title>
      <Paragraph>
        测试服务器端功能节点（301-330）的注册状态和基本功能。
        本批次包含30个节点，涵盖用户服务、项目服务和资产服务。
      </Paragraph>

      {/* 测试统计 */}
      <Card title="测试统计" style={{ marginBottom: '24px' }}>
        <Space size="large">
          <div>
            <Text strong>总节点数：</Text>
            <Tag color="blue">{totalCount}</Tag>
          </div>
          <div>
            <Text strong>已注册：</Text>
            <Tag color="green">{registeredCount}</Tag>
          </div>
          <div>
            <Text strong>注册率：</Text>
            <Tag color={registrationRate === '100.0' ? 'green' : 'orange'}>
              {registrationRate}%
            </Tag>
          </div>
        </Space>

        {registrationRate === '100.0' ? (
          <Alert
            message="✅ 所有节点注册成功"
            description="第11批次的30个服务器端功能节点已全部注册到编辑器中，可以通过拖拽方式使用。"
            type="success"
            style={{ marginTop: '16px' }}
          />
        ) : (
          <Alert
            message="⚠️ 部分节点未注册"
            description={`还有 ${totalCount - registeredCount} 个节点未成功注册，请检查实现和配置。`}
            type="warning"
            style={{ marginTop: '16px' }}
          />
        )}
      </Card>

      {/* 操作按钮 */}
      <Card style={{ marginBottom: '24px' }}>
        <Space>
          <Button 
            type="primary" 
            onClick={testNodeRegistration}
            loading={loading}
          >
            重新测试
          </Button>
          <Button 
            onClick={() => {
              console.log('第11批次节点测试结果:', testResults);
            }}
          >
            输出到控制台
          </Button>
        </Space>
      </Card>

      {/* 用户服务节点 */}
      <Card title="👤 用户服务节点 (301-310)" style={{ marginBottom: '24px' }}>
        <List
          size="small"
          dataSource={userServiceNodes}
          renderItem={(item) => (
            <List.Item>
              <Space>
                <Tag color={item.registered ? 'green' : 'red'}>
                  {item.registered ? '✅' : '❌'}
                </Tag>
                <Text strong>{item.label}</Text>
                <Text type="secondary">({item.type})</Text>
                <div>
                  {item.tags.map(tag => (
                    <Tag key={tag}>{tag}</Tag>
                  ))}
                </div>
              </Space>
            </List.Item>
          )}
        />
      </Card>

      {/* 项目服务节点 */}
      <Card title="📁 项目服务节点 (311-325)" style={{ marginBottom: '24px' }}>
        <List
          size="small"
          dataSource={projectServiceNodes}
          renderItem={(item) => (
            <List.Item>
              <Space>
                <Tag color={item.registered ? 'green' : 'red'}>
                  {item.registered ? '✅' : '❌'}
                </Tag>
                <Text strong>{item.label}</Text>
                <Text type="secondary">({item.type})</Text>
                <div>
                  {item.tags.map(tag => (
                    <Tag key={tag}>{tag}</Tag>
                  ))}
                </div>
              </Space>
            </List.Item>
          )}
        />
      </Card>

      {/* 资产服务节点 */}
      <Card title="📦 资产服务节点 (326-330)" style={{ marginBottom: '24px' }}>
        <List
          size="small"
          dataSource={assetServiceNodes}
          renderItem={(item) => (
            <List.Item>
              <Space>
                <Tag color={item.registered ? 'green' : 'red'}>
                  {item.registered ? '✅' : '❌'}
                </Tag>
                <Text strong>{item.label}</Text>
                <Text type="secondary">({item.type})</Text>
                <div>
                  {item.tags.map(tag => (
                    <Tag key={tag}>{tag}</Tag>
                  ))}
                </div>
              </Space>
            </List.Item>
          )}
        />
      </Card>

      {/* 使用说明 */}
      <Card title="📝 使用说明">
        <Paragraph>
          <Title level={4}>如何测试节点功能：</Title>
          <ol>
            <li><strong>检查注册状态</strong>：确保所有节点都显示为"✅已注册"</li>
            <li><strong>打开节点面板</strong>：在编辑器中查看"网络"分类下的服务器节点</li>
            <li><strong>拖拽创建</strong>：将节点拖拽到画布上创建节点实例</li>
            <li><strong>配置参数</strong>：设置节点的输入参数</li>
            <li><strong>执行测试</strong>：运行脚本验证节点执行逻辑</li>
          </ol>
        </Paragraph>

        <Divider />

        <Paragraph>
          <Title level={4}>节点功能说明：</Title>
          <ul>
            <li><strong>用户服务</strong>：用户注册、登录、权限管理等功能</li>
            <li><strong>项目服务</strong>：项目创建、分享、协作、备份等功能</li>
            <li><strong>资产服务</strong>：文件上传、下载、管理等功能</li>
          </ul>
        </Paragraph>
      </Card>
    </div>
  );
};

export default Batch11NodeTest;
