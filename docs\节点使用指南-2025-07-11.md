# 视觉脚本节点使用指南 - 2025年7月11日

## 📖 概述

本文档提供了视觉脚本系统中所有228个节点的详细使用说明、参数配置和示例代码。所有节点都已成功注册到引擎中，可在编辑器中通过拖拽方式使用。

## 🎯 文档结构

- **基础使用**: 节点的基本概念和使用方法
- **分类说明**: 按功能模块分类的节点详解
- **示例代码**: 实际使用场景和代码示例
- **最佳实践**: 性能优化和使用建议
- **故障排除**: 常见问题和解决方案

## 🔧 基础概念

### 节点类型
- **数据节点**: 处理数据计算和转换
- **流程节点**: 控制执行流程和逻辑
- **事件节点**: 响应系统事件和用户交互
- **资源节点**: 管理资产和资源

### 连接类型
- **数据连接**: 传递数据值（蓝色）
- **流程连接**: 控制执行顺序（白色）
- **事件连接**: 触发事件响应（红色）

## 📐 数学和三角函数节点 (001-004)

### 1. 正弦节点 (math/trigonometry/sin)

**功能**: 计算角度的正弦值

**输入参数**:
- `angle` (number): 角度值（弧度制）

**输出结果**:
- `result` (number): 正弦值 (-1 到 1)

**使用示例**:
```javascript
// 计算 30 度的正弦值
const angle = Math.PI / 6; // 30度转弧度
// 输出: 0.5
```

**应用场景**:
- 波形动画
- 周期性运动
- 物理模拟

### 2. 余弦节点 (math/trigonometry/cos)

**功能**: 计算角度的余弦值

**输入参数**:
- `angle` (number): 角度值（弧度制）

**输出结果**:
- `result` (number): 余弦值 (-1 到 1)

**使用示例**:
```javascript
// 计算 60 度的余弦值
const angle = Math.PI / 3; // 60度转弧度
// 输出: 0.5
```

**应用场景**:
- 圆周运动
- 振荡效果
- 相位计算

### 3. 向量长度节点 (math/vector/magnitude)

**功能**: 计算3D向量的长度（模）

**输入参数**:
- `vector` (vector3): 3D向量 {x, y, z}

**输出结果**:
- `magnitude` (number): 向量长度（非负数）

**使用示例**:
```javascript
// 计算向量 (3, 4, 0) 的长度
const vector = { x: 3, y: 4, z: 0 };
// 输出: 5 (3-4-5直角三角形)
```

**应用场景**:
- 距离计算
- 速度大小
- 碰撞检测

### 4. 向量归一化节点 (math/vector/normalize)

**功能**: 将向量转换为单位向量（长度为1）

**输入参数**:
- `vector` (vector3): 输入向量 {x, y, z}

**输出结果**:
- `normalized` (vector3): 归一化向量
- `originalMagnitude` (number): 原始向量长度

**使用示例**:
```javascript
// 归一化向量 (3, 4, 0)
const vector = { x: 3, y: 4, z: 0 };
// 输出: { x: 0.6, y: 0.8, z: 0 }
// 原始长度: 5
```

**应用场景**:
- 方向计算
- 单位向量生成
- 法向量处理

## 🔗 逻辑节点 (005)

### 5. 逻辑与节点 (logic/boolean/and)

**功能**: 执行逻辑与运算

**输入参数**:
- `a` (boolean): 第一个布尔值
- `b` (boolean): 第二个布尔值

**输出结果**:
- `result` (boolean): 逻辑与运算结果
- `truthTable` (string): 真值表描述

**使用示例**:
```javascript
// 逻辑与运算
const a = true;
const b = false;
// 输出: false
// 真值表: "T AND F = F"
```

**真值表**:
| A | B | A AND B |
|---|---|---------|
| T | T | T       |
| T | F | F       |
| F | T | F       |
| F | F | F       |

**应用场景**:
- 条件判断
- 权限验证
- 状态检查

## ⚡ 物理系统节点 (006-021)

### 6. 设置重力节点 (physics/gravity/set)

**功能**: 设置物理世界的重力

**输入参数**:
- `exec` (flow): 执行输入
- `gravity` (vector3): 重力向量，默认 {x: 0, y: -9.81, z: 0}

**输出结果**:
- `exec` (flow): 执行输出

**使用示例**:
```javascript
// 设置月球重力（约为地球的1/6）
const moonGravity = { x: 0, y: -1.635, z: 0 };
```

**应用场景**:
- 重力模拟
- 环境设置
- 物理参数调整

### 7. 碰撞检测节点 (physics/collision/detect)

**功能**: 检测两个实体是否发生碰撞

**输入参数**:
- `entityA` (entity): 实体A
- `entityB` (entity): 实体B

**输出结果**:
- `isColliding` (boolean): 是否碰撞

**使用示例**:
```javascript
// 检测玩家和敌人是否碰撞
// 如果碰撞，触发伤害逻辑
```

**应用场景**:
- 碰撞检测
- 触发器
- 交互判断

### 8. 创建刚体节点 (physics/rigidbody/create)

**功能**: 为实体创建刚体物理组件

**输入参数**:
- `exec` (flow): 执行输入
- `entity` (entity): 目标实体
- `mass` (number): 质量，默认 1.0

**输出结果**:
- `exec` (flow): 执行输出
- `rigidBody` (rigidBody): 创建的刚体

**使用示例**:
```javascript
// 为箱子创建重量为10kg的刚体
const mass = 10.0;
```

**应用场景**:
- 物理对象创建
- 动态物体
- 重力影响

## ⏰ 时间节点 (023-024)

### 23. 延迟节点 (time/delay)

**功能**: 延迟执行指定时间

**输入参数**:
- `exec` (flow): 执行输入
- `duration` (number): 延迟时间（秒）

**输出结果**:
- `exec` (flow): 延迟后执行

**使用示例**:
```javascript
// 延迟3秒后执行下一个动作
const delay = 3.0;
```

**应用场景**:
- 定时执行
- 动画延迟
- 事件调度

### 24. 计时器节点 (time/timer)

**功能**: 按间隔时间重复执行

**输入参数**:
- `start` (flow): 开始计时
- `stop` (flow): 停止计时
- `interval` (number): 间隔时间（秒）

**输出结果**:
- `tick` (flow): 计时器触发

**使用示例**:
```javascript
// 每秒触发一次
const interval = 1.0;
```

**应用场景**:
- 周期性任务
- 游戏循环
- 状态更新

## 🎬 动画节点 (025-028)

### 25. 播放动画节点 (animation/playAnimation)

**功能**: 播放指定的动画

**输入参数**:
- `exec` (flow): 执行输入
- `entity` (entity): 目标实体
- `animationName` (string): 动画名称

**输出结果**:
- `exec` (flow): 执行输出

**使用示例**:
```javascript
// 播放角色的跑步动画
const animationName = "run";
```

**应用场景**:
- 角色动画
- 物体动画
- UI动画

## 🎮 输入系统节点 (029-031)

### 29. 键盘输入节点 (input/keyboard)

**功能**: 检测键盘输入状态

**输入参数**:
- `keyCode` (string): 按键代码

**输出结果**:
- `isPressed` (boolean): 是否按下
- `onKeyDown` (flow): 按键按下事件
- `onKeyUp` (flow): 按键释放事件

**使用示例**:
```javascript
// 检测空格键是否按下
const keyCode = "Space";
```

**应用场景**:
- 玩家控制
- 快捷键
- 输入响应

## 🔊 音频系统节点 (032-036)

### 32. 播放音频节点 (audio/playAudio)

**功能**: 播放音频文件

**输入参数**:
- `exec` (flow): 执行输入
- `audioClip` (audioClip): 音频片段
- `volume` (number): 音量 (0-1)

**输出结果**:
- `exec` (flow): 执行输出

**使用示例**:
```javascript
// 播放背景音乐，音量50%
const volume = 0.5;
```

**应用场景**:
- 背景音乐
- 音效播放
- 语音提示

## 📝 最佳实践

### 性能优化
1. **避免频繁计算**: 缓存计算结果
2. **合理使用定时器**: 避免过短的间隔时间
3. **及时清理资源**: 停用不需要的节点

### 错误处理
1. **数据验证**: 检查输入参数的有效性
2. **异常捕获**: 处理可能的运行时错误
3. **默认值**: 为输入参数提供合理的默认值

### 调试技巧
1. **日志输出**: 使用控制台输出调试信息
2. **分步测试**: 逐个测试节点功能
3. **性能监控**: 关注节点执行时间

## 🔧 故障排除

### 常见问题

**Q: 节点无法执行？**
A: 检查输入连接是否正确，确保数据类型匹配

**Q: 数学计算结果不准确？**
A: 验证输入参数的数据类型和数值范围

**Q: 动画播放失败？**
A: 确认动画名称正确，实体包含动画组件

**Q: 音频无法播放？**
A: 检查音频文件格式和路径是否正确

### 调试步骤
1. 检查节点连接
2. 验证输入参数
3. 查看控制台错误
4. 测试简化场景
5. 逐步增加复杂度

## 📚 扩展阅读

- [视觉脚本系统架构文档](./architecture.md)
- [节点开发指南](./node-development.md)
- [性能优化指南](./performance.md)
- [API参考文档](./api-reference.md)

---
*文档版本: 1.0*  
*最后更新: 2025年7月11日*  
*维护者: Augment Agent*
