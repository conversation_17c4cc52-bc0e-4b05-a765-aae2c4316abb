/**
 * 增强版228个节点实现
 * 为关键节点提供真正的功能实现，而不仅仅是占位符
 */

import { Node, NodeCategory, SocketType, SocketDirection, NodeOptions } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

// ==================== 增强版数学节点 ====================

/**
 * 增强版正弦节点 - 提供真正的数学计算功能
 */
export class EnhancedSinNode extends Node {
  constructor(options: NodeOptions) {
    super({
      ...options,
      type: 'math/trigonometry/sin',
      category: NodeCategory.MATH
    });
    
    this.addInput({
      name: 'angle',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '角度（弧度）',
      defaultValue: 0
    });
    
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '正弦值 (-1 到 1)'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    try {
      const angle = this.getInputValue('angle') || 0;
      
      // 数据类型验证
      if (typeof angle !== 'number' || isNaN(angle)) {
        throw new Error(`无效的角度值: ${angle}，必须是数字`);
      }
      
      // 计算正弦值
      const result = Math.sin(angle);
      
      // 输出结果
      this.setOutputValue('result', result);
      
      // 性能监控
      if (context.performanceMonitor) {
        context.performanceMonitor.recordNodeExecution(this.type, 'success');
      }
      
    } catch (error) {
      console.error(`正弦节点执行错误:`, error);
      this.setOutputValue('result', 0);
      
      if (context.performanceMonitor) {
        context.performanceMonitor.recordNodeExecution(this.type, 'error', error.message);
      }
    }
  }
}

/**
 * 增强版余弦节点
 */
export class EnhancedCosNode extends Node {
  constructor(options: NodeOptions) {
    super({
      ...options,
      type: 'math/trigonometry/cos',
      category: NodeCategory.MATH
    });
    
    this.addInput({
      name: 'angle',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '角度（弧度）',
      defaultValue: 0
    });
    
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '余弦值 (-1 到 1)'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    try {
      const angle = this.getInputValue('angle') || 0;
      
      if (typeof angle !== 'number' || isNaN(angle)) {
        throw new Error(`无效的角度值: ${angle}，必须是数字`);
      }
      
      const result = Math.cos(angle);
      this.setOutputValue('result', result);
      
      if (context.performanceMonitor) {
        context.performanceMonitor.recordNodeExecution(this.type, 'success');
      }
      
    } catch (error) {
      console.error(`余弦节点执行错误:`, error);
      this.setOutputValue('result', 0);
      
      if (context.performanceMonitor) {
        context.performanceMonitor.recordNodeExecution(this.type, 'error', error.message);
      }
    }
  }
}

/**
 * 增强版向量长度节点
 */
export class EnhancedVectorMagnitudeNode extends Node {
  constructor(options: NodeOptions) {
    super({
      ...options,
      type: 'math/vector/magnitude',
      category: NodeCategory.MATH
    });
    
    this.addInput({
      name: 'vector',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.INPUT,
      description: '输入向量 {x, y, z}',
      defaultValue: { x: 0, y: 0, z: 0 }
    });
    
    this.addOutput({
      name: 'magnitude',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '向量长度（非负数）'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    try {
      const vector = this.getInputValue('vector') || { x: 0, y: 0, z: 0 };
      
      // 验证向量格式
      if (!this.isValidVector3(vector)) {
        throw new Error(`无效的向量格式: ${JSON.stringify(vector)}，必须包含 x, y, z 数值属性`);
      }
      
      // 计算向量长度
      const magnitude = Math.sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
      
      this.setOutputValue('magnitude', magnitude);
      
      if (context.performanceMonitor) {
        context.performanceMonitor.recordNodeExecution(this.type, 'success');
      }
      
    } catch (error) {
      console.error(`向量长度节点执行错误:`, error);
      this.setOutputValue('magnitude', 0);
      
      if (context.performanceMonitor) {
        context.performanceMonitor.recordNodeExecution(this.type, 'error', error.message);
      }
    }
  }
  
  private isValidVector3(vector: any): boolean {
    return vector && 
           typeof vector === 'object' &&
           typeof vector.x === 'number' && !isNaN(vector.x) &&
           typeof vector.y === 'number' && !isNaN(vector.y) &&
           typeof vector.z === 'number' && !isNaN(vector.z);
  }
}

/**
 * 增强版向量归一化节点
 */
export class EnhancedVectorNormalizeNode extends Node {
  constructor(options: NodeOptions) {
    super({
      ...options,
      type: 'math/vector/normalize',
      category: NodeCategory.MATH
    });
    
    this.addInput({
      name: 'vector',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.INPUT,
      description: '输入向量 {x, y, z}',
      defaultValue: { x: 0, y: 0, z: 0 }
    });
    
    this.addOutput({
      name: 'normalized',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.OUTPUT,
      description: '归一化向量（长度为1）'
    });
    
    this.addOutput({
      name: 'originalMagnitude',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '原始向量长度'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    try {
      const vector = this.getInputValue('vector') || { x: 0, y: 0, z: 0 };
      
      if (!this.isValidVector3(vector)) {
        throw new Error(`无效的向量格式: ${JSON.stringify(vector)}`);
      }
      
      // 计算向量长度
      const magnitude = Math.sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
      
      // 处理零向量情况
      if (magnitude === 0) {
        console.warn('尝试归一化零向量，返回零向量');
        this.setOutputValue('normalized', { x: 0, y: 0, z: 0 });
        this.setOutputValue('originalMagnitude', 0);
        return;
      }
      
      // 归一化向量
      const normalized = {
        x: vector.x / magnitude,
        y: vector.y / magnitude,
        z: vector.z / magnitude
      };
      
      this.setOutputValue('normalized', normalized);
      this.setOutputValue('originalMagnitude', magnitude);
      
      if (context.performanceMonitor) {
        context.performanceMonitor.recordNodeExecution(this.type, 'success');
      }
      
    } catch (error) {
      console.error(`向量归一化节点执行错误:`, error);
      this.setOutputValue('normalized', { x: 0, y: 0, z: 0 });
      this.setOutputValue('originalMagnitude', 0);
      
      if (context.performanceMonitor) {
        context.performanceMonitor.recordNodeExecution(this.type, 'error', error.message);
      }
    }
  }
  
  private isValidVector3(vector: any): boolean {
    return vector && 
           typeof vector === 'object' &&
           typeof vector.x === 'number' && !isNaN(vector.x) &&
           typeof vector.y === 'number' && !isNaN(vector.y) &&
           typeof vector.z === 'number' && !isNaN(vector.z);
  }
}

// ==================== 增强版逻辑节点 ====================

/**
 * 增强版逻辑与节点
 */
export class EnhancedLogicalAndNode extends Node {
  constructor(options: NodeOptions) {
    super({
      ...options,
      type: 'logic/boolean/and',
      category: NodeCategory.LOGIC
    });
    
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '第一个布尔值',
      defaultValue: false
    });
    
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '第二个布尔值',
      defaultValue: false
    });
    
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '逻辑与运算结果'
    });
    
    this.addOutput({
      name: 'truthTable',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '真值表描述'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    try {
      const a = this.getInputValue('a') || false;
      const b = this.getInputValue('b') || false;
      
      // 转换为布尔值（支持真值转换）
      const boolA = Boolean(a);
      const boolB = Boolean(b);
      
      // 执行逻辑与运算
      const result = boolA && boolB;
      
      // 生成真值表描述
      const truthTable = `${boolA ? 'T' : 'F'} AND ${boolB ? 'T' : 'F'} = ${result ? 'T' : 'F'}`;
      
      this.setOutputValue('result', result);
      this.setOutputValue('truthTable', truthTable);
      
      if (context.performanceMonitor) {
        context.performanceMonitor.recordNodeExecution(this.type, 'success');
      }
      
    } catch (error) {
      console.error(`逻辑与节点执行错误:`, error);
      this.setOutputValue('result', false);
      this.setOutputValue('truthTable', 'ERROR');
      
      if (context.performanceMonitor) {
        context.performanceMonitor.recordNodeExecution(this.type, 'error', error.message);
      }
    }
  }
}

// ==================== 增强版时间节点 ====================

/**
 * 增强版延迟节点
 */
export class EnhancedDelayNode extends Node {
  private timeoutId: NodeJS.Timeout | null = null;

  constructor(options: NodeOptions) {
    super({
      ...options,
      type: 'time/delay',
      category: NodeCategory.FLOW
    });

    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '延迟时间（秒）',
      defaultValue: 1.0
    });

    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '延迟后执行'
    });
  }

  protected async executeInternal(context: ExecutionContext): Promise<void> {
    try {
      const duration = Math.max(0, this.getInputValue('duration') || 1.0);

      if (typeof duration !== 'number' || isNaN(duration)) {
        throw new Error(`无效的延迟时间: ${duration}`);
      }

      // 清除之前的延迟
      if (this.timeoutId) {
        clearTimeout(this.timeoutId);
      }

      // 设置延迟执行
      this.timeoutId = setTimeout(() => {
        this.executeOutput('exec', context);
        this.timeoutId = null;

        if (context.performanceMonitor) {
          context.performanceMonitor.recordNodeExecution(this.type, 'completed');
        }
      }, duration * 1000);

    } catch (error) {
      console.error(`延迟节点执行错误:`, error);

      if (context.performanceMonitor) {
        context.performanceMonitor.recordNodeExecution(this.type, 'error', error.message);
      }
    }
  }

  public dispose(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }
}

// ==================== 注册增强版节点 ====================

/**
 * 注册增强版节点到引擎
 */
export function registerEnhanced228Nodes(registry: NodeRegistry): void {
  console.log('🚀 开始注册增强版节点...');

  let registeredCount = 0;

  try {
    // 注册增强版数学节点
    registry.registerNodeType({
      type: 'math/trigonometry/sin',
      category: NodeCategory.MATH,
      constructor: EnhancedSinNode,
      label: '正弦（增强版）',
      description: '计算角度的正弦值，包含数据验证和错误处理',
      icon: 'sin',
      color: '#2196F3',
      tags: ['math', 'trigonometry', 'enhanced']
    });
    registeredCount++;

    registry.registerNodeType({
      type: 'math/trigonometry/cos',
      category: NodeCategory.MATH,
      constructor: EnhancedCosNode,
      label: '余弦（增强版）',
      description: '计算角度的余弦值，包含数据验证和错误处理',
      icon: 'cos',
      color: '#2196F3',
      tags: ['math', 'trigonometry', 'enhanced']
    });
    registeredCount++;

    registry.registerNodeType({
      type: 'math/vector/magnitude',
      category: NodeCategory.MATH,
      constructor: EnhancedVectorMagnitudeNode,
      label: '向量长度（增强版）',
      description: '计算向量的长度，包含向量格式验证',
      icon: 'vector-magnitude',
      color: '#2196F3',
      tags: ['math', 'vector', 'enhanced']
    });
    registeredCount++;

    registry.registerNodeType({
      type: 'math/vector/normalize',
      category: NodeCategory.MATH,
      constructor: EnhancedVectorNormalizeNode,
      label: '向量归一化（增强版）',
      description: '将向量归一化为单位向量，处理零向量情况',
      icon: 'vector-normalize',
      color: '#2196F3',
      tags: ['math', 'vector', 'enhanced']
    });
    registeredCount++;

    // 注册增强版逻辑节点
    registry.registerNodeType({
      type: 'logic/boolean/and',
      category: NodeCategory.LOGIC,
      constructor: EnhancedLogicalAndNode,
      label: '逻辑与（增强版）',
      description: '执行逻辑与运算，提供真值表输出',
      icon: 'and',
      color: '#FF9800',
      tags: ['logic', 'boolean', 'enhanced']
    });
    registeredCount++;

    // 注册增强版时间节点
    registry.registerNodeType({
      type: 'time/delay',
      category: NodeCategory.FLOW,
      constructor: EnhancedDelayNode,
      label: '延迟（增强版）',
      description: '延迟执行指定时间，支持取消和状态监控',
      icon: 'delay',
      color: '#607D8B',
      tags: ['time', 'delay', 'enhanced']
    });
    registeredCount++;

    console.log(`✅ 已成功注册 ${registeredCount} 个增强版节点`);

  } catch (error) {
    console.error('注册增强版节点时发生错误:', error);
  }
}
