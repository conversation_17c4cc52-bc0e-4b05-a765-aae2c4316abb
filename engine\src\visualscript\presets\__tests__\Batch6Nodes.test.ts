/**
 * 第6批次节点测试
 * 测试高级物理系统和动画系统节点（151-180）
 */
import { describe, it, expect, beforeEach } from '@jest/globals';
import { NodeRegistry } from '../../nodes/NodeRegistry';
import { World } from '../../../core/World';
import { Entity } from '../../../core/Entity';
import { Vector3 } from '../../../math/Vector3';

// 导入第6批次节点
import {
  OnCollisionExitNode,
  OnTriggerEnterNode,
  OnTriggerExitNode,
  SetGravityNode,
  SetTimeStepNode,
  CreateCharacterControllerNode,
  MoveCharacterNode,
  JumpCharacterNode,
  CreateVehicleNode,
  SetEngineForceNode,
  SetBrakeForceNode,
  SetSteeringValueNode,
  CreateFluidSimulationNode,
  CreateClothSimulationNode,
  CreateDestructibleNode
} from '../PhysicsNodes';

import {
  CreateAnimationClipNode,
  AddKeyframeNode,
  SetInterpolationNode,
  CreateAnimationMixerNode,
  PlayAnimationActionNode,
  CreateSkeletalAnimationNode,
  SetBoneTransformNode,
  CreateIKConstraintNode,
  SolveIKNode,
  CreateMorphTargetNode,
  SetMorphWeightNode,
  CreateAnimationCurveNode,
  EvaluateCurveNode,
  CreateStateMachineNode,
  TransitionStateNode
} from '../AnimationNodes';

describe('第6批次：高级物理系统和动画系统节点测试', () => {
  let registry: NodeRegistry;
  let world: World;
  let entity: Entity;

  beforeEach(() => {
    registry = new NodeRegistry();
    world = new World();
    entity = new Entity('TestEntity');
    world.addEntity(entity);
  });

  describe('碰撞事件节点 (151-153)', () => {
    it('应该创建碰撞结束事件节点', () => {
      const node = new OnCollisionExitNode({
        id: 'test-collision-exit',
        name: '碰撞结束事件',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(2); // physicsBodyA, physicsBodyB
      expect(node.getOutputs()).toHaveLength(5); // flow, entityA, entityB, contactPoint, contactNormal
    });

    it('应该创建触发器进入事件节点', () => {
      const node = new OnTriggerEnterNode({
        id: 'test-trigger-enter',
        name: '触发器进入事件',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(2); // trigger, targetBody
      expect(node.getOutputs()).toHaveLength(3); // flow, triggerEntity, enteringEntity
    });

    it('应该创建触发器退出事件节点', () => {
      const node = new OnTriggerExitNode({
        id: 'test-trigger-exit',
        name: '触发器退出事件',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(2); // trigger, targetBody
      expect(node.getOutputs()).toHaveLength(3); // flow, triggerEntity, exitingEntity
    });
  });

  describe('物理世界控制节点 (154-155)', () => {
    it('应该创建设置重力节点', () => {
      const node = new SetGravityNode({
        id: 'test-set-gravity',
        name: '设置重力',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(2); // flow, gravity
      expect(node.getOutputs()).toHaveLength(1); // flow
    });

    it('应该创建设置时间步长节点', () => {
      const node = new SetTimeStepNode({
        id: 'test-set-timestep',
        name: '设置时间步长',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(2); // flow, timeStep
      expect(node.getOutputs()).toHaveLength(1); // flow
    });
  });

  describe('角色控制器节点 (156-158)', () => {
    it('应该创建角色控制器节点', () => {
      const node = new CreateCharacterControllerNode({
        id: 'test-create-character',
        name: '创建角色控制器',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(5); // flow, entity, offset, maxSlopeClimbAngle, maxStepHeight, snapToGroundDistance
      expect(node.getOutputs()).toHaveLength(2); // flow, characterController
    });

    it('应该创建移动角色节点', () => {
      const node = new MoveCharacterNode({
        id: 'test-move-character',
        name: '移动角色',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(3); // flow, characterController, movement
      expect(node.getOutputs()).toHaveLength(3); // flow, actualMovement, isGrounded
    });

    it('应该创建角色跳跃节点', () => {
      const node = new JumpCharacterNode({
        id: 'test-jump-character',
        name: '角色跳跃',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(3); // flow, characterController, jumpForce
      expect(node.getOutputs()).toHaveLength(2); // flow, jumpSuccessful
    });
  });

  describe('载具系统节点 (159-162)', () => {
    it('应该创建载具节点', () => {
      const node = new CreateVehicleNode({
        id: 'test-create-vehicle',
        name: '创建载具',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(5); // flow, entity, maxEngineForce, maxBrakeForce, maxSteeringAngle, chassisMass
      expect(node.getOutputs()).toHaveLength(2); // flow, vehicleController
    });

    it('应该创建设置引擎力节点', () => {
      const node = new SetEngineForceNode({
        id: 'test-set-engine-force',
        name: '设置引擎力',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(3); // flow, vehicleController, engineForce
      expect(node.getOutputs()).toHaveLength(1); // flow
    });

    it('应该创建设置制动力节点', () => {
      const node = new SetBrakeForceNode({
        id: 'test-set-brake-force',
        name: '设置制动力',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(3); // flow, vehicleController, brakeForce
      expect(node.getOutputs()).toHaveLength(1); // flow
    });

    it('应该创建设置转向值节点', () => {
      const node = new SetSteeringValueNode({
        id: 'test-set-steering-value',
        name: '设置转向值',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(3); // flow, vehicleController, steeringValue
      expect(node.getOutputs()).toHaveLength(1); // flow
    });
  });

  describe('高级物理节点 (163-165)', () => {
    it('应该创建流体模拟节点', () => {
      const node = new CreateFluidSimulationNode({
        id: 'test-create-fluid',
        name: '创建流体模拟',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(5); // flow, particleCount, viscosity, density, position
      expect(node.getOutputs()).toHaveLength(2); // flow, fluidSystem
    });

    it('应该创建布料模拟节点', () => {
      const node = new CreateClothSimulationNode({
        id: 'test-create-cloth',
        name: '创建布料模拟',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(6); // flow, width, height, stiffness, damping, position
      expect(node.getOutputs()).toHaveLength(2); // flow, clothSystem
    });

    it('应该创建可破坏物体节点', () => {
      const node = new CreateDestructibleNode({
        id: 'test-create-destructible',
        name: '创建可破坏物体',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(5); // flow, entity, destructionThreshold, fragmentCount, fragmentSize
      expect(node.getOutputs()).toHaveLength(2); // flow, destructibleBody
    });
  });

  describe('动画系统节点 (166-180)', () => {
    it('应该创建动画片段节点', () => {
      const node = new CreateAnimationClipNode({
        id: 'test-create-animation-clip',
        name: '创建动画片段',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(4); // flow, name, duration, loop
      expect(node.getOutputs()).toHaveLength(2); // flow, animationClip
    });

    it('应该创建添加关键帧节点', () => {
      const node = new AddKeyframeNode({
        id: 'test-add-keyframe',
        name: '添加关键帧',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(5); // flow, animationClip, time, property, value
      expect(node.getOutputs()).toHaveLength(2); // flow, animationClip
    });

    it('应该创建动画混合器节点', () => {
      const node = new CreateAnimationMixerNode({
        id: 'test-create-animation-mixer',
        name: '创建动画混合器',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(2); // flow, entity
      expect(node.getOutputs()).toHaveLength(2); // flow, animationMixer
    });

    it('应该创建状态机节点', () => {
      const node = new CreateStateMachineNode({
        id: 'test-create-state-machine',
        name: '创建状态机',
        context: { world }
      });

      expect(node).toBeDefined();
      expect(node.getInputs()).toHaveLength(2); // flow, initialState
      expect(node.getOutputs()).toHaveLength(2); // flow, stateMachine
    });
  });

  describe('节点执行测试', () => {
    it('应该正确执行设置重力节点', () => {
      const node = new SetGravityNode({
        id: 'test-set-gravity-exec',
        name: '设置重力执行测试',
        context: { world }
      });

      // 设置输入值
      node.setInputValue('gravity', new Vector3(0, -10, 0));

      // 执行节点
      node.execute();

      // 验证执行成功（这里只是基本验证，实际的物理系统验证需要更复杂的设置）
      expect(node).toBeDefined();
    });

    it('应该正确执行创建动画片段节点', () => {
      const node = new CreateAnimationClipNode({
        id: 'test-create-clip-exec',
        name: '创建动画片段执行测试',
        context: { world }
      });

      // 设置输入值
      node.setInputValue('name', 'TestClip');
      node.setInputValue('duration', 2.0);
      node.setInputValue('loop', true);

      // 执行节点
      node.execute();

      // 验证输出
      const animationClip = node.getOutputValue('animationClip') as any;
      expect(animationClip).toBeDefined();
      expect(animationClip.name).toBe('TestClip');
      expect(animationClip.duration).toBe(2.0);
      expect(animationClip.loop).toBe(true);
    });
  });
});
