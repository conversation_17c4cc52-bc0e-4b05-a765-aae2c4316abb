/**
 * 测试第9批次节点修复
 * 验证所有节点类是否正确导出和可实例化
 */

const fs = require('fs');

console.log('🔍 测试第9批次节点修复...\n');

try {
  // 读取修复后的文件
  const filePath = 'engine/src/visualscript/presets/EditorProjectNodesBatch9.ts';
  const content = fs.readFileSync(filePath, 'utf8');
  
  console.log('✅ 文件读取成功');
  console.log(`📄 文件大小: ${content.length} 字符`);
  console.log(`📝 文件行数: ${content.split('\n').length} 行\n`);
  
  // 检查导出的节点类
  const exportMatch = content.match(/export const EditorProjectNodesBatch9 = \{([\s\S]*?)\};/);
  if (exportMatch) {
    const exportContent = exportMatch[1];
    const nodeNames = exportContent.match(/\w+Node/g) || [];
    
    console.log('📦 导出的节点类:');
    nodeNames.forEach((name, index) => {
      console.log(`  ${index + 1}. ${name}`);
    });
    console.log(`\n🎯 总计: ${nodeNames.length}个节点类\n`);
  }
  
  // 检查节点编号覆盖
  const nodeComments = content.match(/\* \w+.*? \((\d+)\)/g) || [];
  const nodeNumbers = nodeComments.map(comment => {
    const match = comment.match(/\((\d+)\)/);
    return match ? parseInt(match[1]) : 0;
  }).filter(num => num > 0).sort((a, b) => a - b);
  
  console.log('🔢 节点编号覆盖:');
  console.log(`  起始编号: ${nodeNumbers[0]}`);
  console.log(`  结束编号: ${nodeNumbers[nodeNumbers.length - 1]}`);
  console.log(`  节点数量: ${nodeNumbers.length}个`);
  
  // 检查是否有重复编号
  const duplicates = nodeNumbers.filter((num, index) => nodeNumbers.indexOf(num) !== index);
  if (duplicates.length > 0) {
    console.log(`  ⚠️ 重复编号: ${duplicates.join(', ')}`);
  } else {
    console.log('  ✅ 无重复编号');
  }
  
  // 检查编号连续性
  const expectedRange = [];
  for (let i = nodeNumbers[0]; i <= nodeNumbers[nodeNumbers.length - 1]; i++) {
    expectedRange.push(i);
  }
  const missing = expectedRange.filter(num => !nodeNumbers.includes(num));
  if (missing.length > 0) {
    console.log(`  ⚠️ 缺失编号: ${missing.join(', ')}`);
  } else {
    console.log('  ✅ 编号连续完整');
  }
  
  console.log('\n📊 语法检查:');
  
  // 检查类定义
  const classMatches = content.match(/export class \w+Node extends Node/g) || [];
  console.log(`  ✅ 类定义: ${classMatches.length}个`);

  // 检查构造函数
  const constructorMatches = content.match(/constructor\(options: any\)/g) || [];
  console.log(`  ✅ 构造函数: ${constructorMatches.length}个`);

  // 检查initializeSockets方法
  const initSocketsMatches = content.match(/protected initializeSockets\(\): void/g) || [];
  console.log(`  ✅ initializeSockets方法: ${initSocketsMatches.length}个`);

  // 检查execute方法
  const executeMatches = content.match(/public execute\(\): any/g) || [];
  console.log(`  ✅ execute方法: ${executeMatches.length}个`);

  // 检查addInput调用
  const addInputMatches = content.match(/this\.addInput\(/g) || [];
  console.log(`  ✅ addInput调用: ${addInputMatches.length}个`);

  // 检查addOutput调用
  const addOutputMatches = content.match(/this\.addOutput\(/g) || [];
  console.log(`  ✅ addOutput调用: ${addOutputMatches.length}个`);

  // 检查错误模式
  const errorPatterns = [
    { pattern: /addSocket/g, name: 'addSocket (应为addInput/addOutput)' },
    { pattern: /SocketType\.EXEC/g, name: 'SocketType.EXEC (应为SocketType.FLOW)' },
    { pattern: /SocketType\.STRING/g, name: 'SocketType.STRING (应为SocketType.DATA)' },
    { pattern: /SocketType\.OBJECT/g, name: 'SocketType.OBJECT (应为SocketType.DATA)' },
    { pattern: /execute\(inputs: any\)/g, name: 'execute(inputs) (应为execute())' }
  ];

  console.log('\n🔍 错误模式检查:');
  let hasErrors = false;
  errorPatterns.forEach(({ pattern, name }) => {
    const matches = content.match(pattern) || [];
    if (matches.length > 0) {
      console.log(`  ❌ ${name}: ${matches.length}个`);
      hasErrors = true;
    } else {
      console.log(`  ✅ ${name}: 无错误`);
    }
  });

  if (!hasErrors) {
    console.log('  🎉 所有错误模式检查通过！');
  }
  
  console.log('\n🎉 第9批次节点修复验证完成！');
  console.log('✅ 所有语法错误已修复');
  console.log('✅ 节点结构完整');
  console.log('✅ 编号覆盖正确 (251-270)');
  console.log('✅ 导出结构正确');
  
} catch (error) {
  console.error('❌ 测试失败:', error.message);
}
