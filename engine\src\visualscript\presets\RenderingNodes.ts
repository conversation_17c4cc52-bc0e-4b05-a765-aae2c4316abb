/**
 * 渲染系统节点
 * 提供相机创建和控制功能
 */
import * as THREE from 'three';
import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory, SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { Camera, CameraType } from '../../rendering/Camera';
import { Entity } from '../../core/Entity';
import { Transform } from '../../scene/Transform';
import {
  CreateBasicMaterialNode,
  CreateStandardMaterialNode,
  CreatePhysicalMaterialNode,
  SetMaterialColorNode,
  SetMaterialTextureNode,
  SetMaterialOpacityNode
} from './MaterialNodes';
import {
  EnableFXAANode,
  EnableSSAONode,
  EnableBloomNode
} from './PostProcessNodes';
import {
  CreateRigidBodyNode,
  SetMassNode,
  SetFrictionNode,
  SetRestitutionNode
} from './RigidBodyNodes';

/**
 * 创建透视相机节点
 */
export class CreatePerspectiveCameraNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'fov',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 75,
      description: '视野角度'
    });

    this.addInput({
      name: 'aspect',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 16/9,
      description: '宽高比'
    });

    this.addInput({
      name: 'near',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 0.1,
      description: '近裁剪面'
    });

    this.addInput({
      name: 'far',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 1000,
      description: '远裁剪面'
    });

    this.addInput({
      name: 'entityName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      defaultValue: '透视相机',
      description: '实体名称'
    });

    // 输出插槽
    this.addOutput({
      name: 'onComplete',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成输出'
    });

    this.addOutput({
      name: 'camera',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Camera',
      description: '创建的相机组件'
    });

    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Entity',
      description: '相机实体'
    });
  }

  public execute(): any {
    try {
      // 获取输入值
      const fov = this.getInputValue('fov') || 75;
      const aspect = this.getInputValue('aspect') || (window.innerWidth / window.innerHeight);
      const near = this.getInputValue('near') || 0.1;
      const far = this.getInputValue('far') || 1000;
      const entityName = this.getInputValue('entityName') || '透视相机';

      // 创建相机实体
      const cameraEntity = new Entity(entityName);

      // 创建相机组件
      const camera = new Camera({
        type: CameraType.PERSPECTIVE,
        fov: fov,
        aspect: aspect,
        near: near,
        far: far,
        autoAspect: true
      });

      // 添加相机组件到实体
      cameraEntity.addComponent(camera);

      // 添加变换组件
      const transform = new Transform();
      cameraEntity.addComponent(transform);

      // 设置输出值
      this.setOutputValue('camera', camera);
      this.setOutputValue('entity', cameraEntity);

      // 触发完成流程
      this.triggerFlow('onComplete');

      return {
        camera: camera,
        entity: cameraEntity
      };
    } catch (error) {
      console.error('创建透视相机失败:', error);
      throw error;
    }
  }
}

/**
 * 创建正交相机节点
 */
export class CreateOrthographicCameraNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'left',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: -10,
      description: '左平面'
    });

    this.addInput({
      name: 'right',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 10,
      description: '右平面'
    });

    this.addInput({
      name: 'top',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 10,
      description: '上平面'
    });

    this.addInput({
      name: 'bottom',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: -10,
      description: '下平面'
    });

    this.addInput({
      name: 'near',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 0.1,
      description: '近裁剪面'
    });

    this.addInput({
      name: 'far',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 1000,
      description: '远裁剪面'
    });

    this.addInput({
      name: 'entityName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      defaultValue: '正交相机',
      description: '实体名称'
    });

    // 输出插槽
    this.addOutput({
      name: 'onComplete',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成输出'
    });

    this.addOutput({
      name: 'camera',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Camera',
      description: '创建的相机组件'
    });

    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Entity',
      description: '相机实体'
    });
  }

  public execute(): any {
    try {
      // 获取输入值
      const left = this.getInputValue('left') || -10;
      const right = this.getInputValue('right') || 10;
      const top = this.getInputValue('top') || 10;
      const bottom = this.getInputValue('bottom') || -10;
      const near = this.getInputValue('near') || 0.1;
      const far = this.getInputValue('far') || 1000;
      const entityName = this.getInputValue('entityName') || '正交相机';

      // 创建相机实体
      const cameraEntity = new Entity(entityName);

      // 创建相机组件
      const camera = new Camera({
        type: CameraType.ORTHOGRAPHIC,
        left: left,
        right: right,
        top: top,
        bottom: bottom,
        near: near,
        far: far
      });

      // 添加相机组件到实体
      cameraEntity.addComponent(camera);

      // 添加变换组件
      const transform = new Transform();
      cameraEntity.addComponent(transform);

      // 设置输出值
      this.setOutputValue('camera', camera);
      this.setOutputValue('entity', cameraEntity);

      // 触发完成流程
      this.triggerFlow('onComplete');

      return {
        camera: camera,
        entity: cameraEntity
      };
    } catch (error) {
      console.error('创建正交相机失败:', error);
      throw error;
    }
  }
}

/**
 * 设置相机位置节点
 */
export class SetCameraPositionNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Entity',
      description: '相机实体'
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      defaultValue: { x: 0, y: 0, z: 5 },
      description: '相机位置'
    });

    // 输出插槽
    this.addOutput({
      name: 'onComplete',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成输出'
    });

    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Entity',
      description: '相机实体'
    });
  }

  public execute(): any {
    try {
      // 获取输入值
      const entity = this.getInputValue('entity');
      const position = this.getInputValue('position') || { x: 0, y: 0, z: 5 };

      if (!entity) {
        throw new Error('相机实体不能为空');
      }

      // 获取变换组件
      const transform = entity.getComponent(Transform);
      if (!transform) {
        throw new Error('相机实体缺少变换组件');
      }

      // 设置位置
      transform.setPosition(position.x, position.y, position.z);

      // 设置输出值
      this.setOutputValue('entity', entity);

      // 触发完成流程
      this.triggerFlow('onComplete');

      return { entity: entity };
    } catch (error) {
      console.error('设置相机位置失败:', error);
      throw error;
    }
  }
}

/**
 * 设置相机目标节点
 */
export class SetCameraTargetNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 相机输入
    this.addInput({
      name: 'camera',
      type: SocketType.DATA,
      dataType: 'Camera',
      direction: SocketDirection.INPUT,
      description: '相机',
      optional: false
    });

    // 目标位置输入
    this.addInput({
      name: 'target',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '目标位置',
      optional: false,
      defaultValue: { x: 0, y: 0, z: 0 }
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 相机输出
    this.addOutput({
      name: 'camera',
      type: SocketType.DATA,
      dataType: 'Camera',
      direction: SocketDirection.OUTPUT,
      description: '相机'
    });
  }

  protected async executeFlow(): Promise<void> {
    const camera = this.getInputValue('camera');
    const target = this.getInputValue('target');

    if (!camera) {
      throw new Error('相机参数不能为空');
    }

    if (!target) {
      throw new Error('目标位置参数不能为空');
    }

    try {
      // 设置相机观察目标
      if (camera.threeCamera) {
        camera.threeCamera.lookAt(target.x, target.y, target.z);
        camera.threeCamera.updateProjectionMatrix();
      }

      // 输出相机
      this.setOutputValue('camera', camera);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('设置相机目标失败:', error);
      throw error;
    }
  }
}

/**
 * 设置相机视野节点
 */
export class SetCameraFOVNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 相机输入
    this.addInput({
      name: 'camera',
      type: SocketType.DATA,
      dataType: 'Camera',
      direction: SocketDirection.INPUT,
      description: '相机',
      optional: false
    });

    // 视野角度输入
    this.addInput({
      name: 'fov',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '视野角度',
      optional: false,
      defaultValue: 75
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 相机输出
    this.addOutput({
      name: 'camera',
      type: SocketType.DATA,
      dataType: 'Camera',
      direction: SocketDirection.OUTPUT,
      description: '相机'
    });
  }

  protected async executeFlow(): Promise<void> {
    const camera = this.getInputValue('camera');
    const fov = this.getInputValue('fov');

    if (!camera) {
      throw new Error('相机参数不能为空');
    }

    if (typeof fov !== 'number' || fov <= 0 || fov >= 180) {
      throw new Error('视野角度必须在0-180度之间');
    }

    try {
      // 设置相机视野角度
      if (camera.threeCamera && camera.threeCamera.isPerspectiveCamera) {
        camera.threeCamera.fov = fov;
        camera.threeCamera.updateProjectionMatrix();
      }

      // 输出相机
      this.setOutputValue('camera', camera);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('设置相机视野失败:', error);
      throw error;
    }
  }
}

/**
 * 创建方向光节点
 */
export class CreateDirectionalLightNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 颜色输入
    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      dataType: 'Color',
      direction: SocketDirection.INPUT,
      description: '颜色',
      defaultValue: { r: 1, g: 1, b: 1 }
    });

    // 强度输入
    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '强度',
      defaultValue: 1
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 光源输出
    this.addOutput({
      name: 'light',
      type: SocketType.DATA,
      dataType: 'Light',
      direction: SocketDirection.OUTPUT,
      description: '光源'
    });
  }

  protected async executeFlow(): Promise<void> {
    const color = this.getInputValue('color') || { r: 1, g: 1, b: 1 };
    const intensity = this.getInputValue('intensity') || 1;

    try {
      // 创建方向光
      const directionalLight = new THREE.DirectionalLight(
        new THREE.Color(color.r, color.g, color.b),
        intensity
      );

      // 设置默认位置和方向
      directionalLight.position.set(5, 5, 5);
      directionalLight.target.position.set(0, 0, 0);

      // 输出光源
      this.setOutputValue('light', {
        threeLight: directionalLight,
        type: 'directional'
      });

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('创建方向光失败:', error);
      throw error;
    }
  }
}

/**
 * 创建点光源节点
 */
export class CreatePointLightNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 颜色输入
    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      dataType: 'Color',
      direction: SocketDirection.INPUT,
      description: '颜色',
      defaultValue: { r: 1, g: 1, b: 1 }
    });

    // 强度输入
    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '强度',
      defaultValue: 1
    });

    // 距离输入
    this.addInput({
      name: 'distance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '距离',
      defaultValue: 0
    });

    // 衰减输入
    this.addInput({
      name: 'decay',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '衰减',
      defaultValue: 1
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 光源输出
    this.addOutput({
      name: 'light',
      type: SocketType.DATA,
      dataType: 'Light',
      direction: SocketDirection.OUTPUT,
      description: '光源'
    });
  }

  protected async executeFlow(): Promise<void> {
    const color = this.getInputValue('color') || { r: 1, g: 1, b: 1 };
    const intensity = this.getInputValue('intensity') || 1;
    const distance = this.getInputValue('distance') || 0;
    const decay = this.getInputValue('decay') || 1;

    try {
      // 创建点光源
      const pointLight = new THREE.PointLight(
        new THREE.Color(color.r, color.g, color.b),
        intensity,
        distance,
        decay
      );

      // 设置默认位置
      pointLight.position.set(0, 5, 0);

      // 输出光源
      this.setOutputValue('light', {
        threeLight: pointLight,
        type: 'point'
      });

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('创建点光源失败:', error);
      throw error;
    }
  }
}

/**
 * 创建聚光灯节点
 */
export class CreateSpotLightNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 颜色输入
    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      dataType: 'Color',
      direction: SocketDirection.INPUT,
      description: '颜色',
      defaultValue: { r: 1, g: 1, b: 1 }
    });

    // 强度输入
    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '强度',
      defaultValue: 1
    });

    // 距离输入
    this.addInput({
      name: 'distance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '距离',
      defaultValue: 0
    });

    // 角度输入
    this.addInput({
      name: 'angle',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '角度',
      defaultValue: Math.PI / 3
    });

    // 边缘柔化输入
    this.addInput({
      name: 'penumbra',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '边缘柔化',
      defaultValue: 0
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 光源输出
    this.addOutput({
      name: 'light',
      type: SocketType.DATA,
      dataType: 'Light',
      direction: SocketDirection.OUTPUT,
      description: '光源'
    });
  }

  protected async executeFlow(): Promise<void> {
    const color = this.getInputValue('color') || { r: 1, g: 1, b: 1 };
    const intensity = this.getInputValue('intensity') || 1;
    const distance = this.getInputValue('distance') || 0;
    const angle = this.getInputValue('angle') || Math.PI / 3;
    const penumbra = this.getInputValue('penumbra') || 0;

    try {
      // 创建聚光灯
      const spotLight = new THREE.SpotLight(
        new THREE.Color(color.r, color.g, color.b),
        intensity,
        distance,
        angle,
        penumbra
      );

      // 设置默认位置和目标
      spotLight.position.set(0, 5, 0);
      spotLight.target.position.set(0, 0, 0);

      // 输出光源
      this.setOutputValue('light', {
        threeLight: spotLight,
        type: 'spot'
      });

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('创建聚光灯失败:', error);
      throw error;
    }
  }
}

/**
 * 创建环境光节点
 */
export class CreateAmbientLightNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 颜色输入
    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      dataType: 'Color',
      direction: SocketDirection.INPUT,
      description: '颜色',
      defaultValue: { r: 1, g: 1, b: 1 }
    });

    // 强度输入
    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '强度',
      defaultValue: 0.4
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 光源输出
    this.addOutput({
      name: 'light',
      type: SocketType.DATA,
      dataType: 'Light',
      direction: SocketDirection.OUTPUT,
      description: '光源'
    });
  }

  protected async executeFlow(): Promise<void> {
    const color = this.getInputValue('color') || { r: 1, g: 1, b: 1 };
    const intensity = this.getInputValue('intensity') || 0.4;

    try {
      // 创建环境光
      const ambientLight = new THREE.AmbientLight(
        new THREE.Color(color.r, color.g, color.b),
        intensity
      );

      // 输出光源
      this.setOutputValue('light', {
        threeLight: ambientLight,
        type: 'ambient'
      });

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('创建环境光失败:', error);
      throw error;
    }
  }
}

/**
 * 设置光源颜色节点
 */
export class SetLightColorNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 光源输入
    this.addInput({
      name: 'light',
      type: SocketType.DATA,
      dataType: 'Light',
      direction: SocketDirection.INPUT,
      description: '光源',
      optional: false
    });

    // 颜色输入
    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      dataType: 'Color',
      direction: SocketDirection.INPUT,
      description: '颜色',
      optional: false,
      defaultValue: { r: 1, g: 1, b: 1 }
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 光源输出
    this.addOutput({
      name: 'light',
      type: SocketType.DATA,
      dataType: 'Light',
      direction: SocketDirection.OUTPUT,
      description: '光源'
    });
  }

  protected async executeFlow(): Promise<void> {
    const light = this.getInputValue('light');
    const color = this.getInputValue('color');

    if (!light) {
      throw new Error('光源参数不能为空');
    }

    if (!color) {
      throw new Error('颜色参数不能为空');
    }

    try {
      // 设置光源颜色
      if (light.threeLight) {
        light.threeLight.color.setRGB(color.r, color.g, color.b);
      }

      // 输出光源
      this.setOutputValue('light', light);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('设置光源颜色失败:', error);
      throw error;
    }
  }
}

/**
 * 设置光源强度节点
 */
export class SetLightIntensityNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 光源输入
    this.addInput({
      name: 'light',
      type: SocketType.DATA,
      dataType: 'Light',
      direction: SocketDirection.INPUT,
      description: '光源',
      optional: false
    });

    // 强度输入
    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '强度',
      optional: false,
      defaultValue: 1
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 光源输出
    this.addOutput({
      name: 'light',
      type: SocketType.DATA,
      dataType: 'Light',
      direction: SocketDirection.OUTPUT,
      description: '光源'
    });
  }

  protected async executeFlow(): Promise<void> {
    const light = this.getInputValue('light');
    const intensity = this.getInputValue('intensity');

    if (!light) {
      throw new Error('光源参数不能为空');
    }

    if (typeof intensity !== 'number' || intensity < 0) {
      throw new Error('强度必须是非负数');
    }

    try {
      // 设置光源强度
      if (light.threeLight) {
        light.threeLight.intensity = intensity;
      }

      // 输出光源
      this.setOutputValue('light', light);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('设置光源强度失败:', error);
      throw error;
    }
  }
}

/**
 * 启用阴影节点
 */
export class EnableShadowsNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 渲染器输入
    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'Renderer',
      direction: SocketDirection.INPUT,
      description: '渲染器',
      optional: false
    });

    // 启用状态输入
    this.addInput({
      name: 'enabled',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '启用',
      defaultValue: true
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 渲染器输出
    this.addOutput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'Renderer',
      direction: SocketDirection.OUTPUT,
      description: '渲染器'
    });
  }

  protected async executeFlow(): Promise<void> {
    const renderer = this.getInputValue('renderer');
    const enabled = this.getInputValue('enabled') !== false;

    if (!renderer) {
      throw new Error('渲染器参数不能为空');
    }

    try {
      // 启用或禁用阴影
      if (renderer.threeRenderer) {
        renderer.threeRenderer.shadowMap.enabled = enabled;
        renderer.threeRenderer.shadowMap.type = THREE.PCFSoftShadowMap;
      }

      // 输出渲染器
      this.setOutputValue('renderer', renderer);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('启用阴影失败:', error);
      throw error;
    }
  }
}

/**
 * 设置阴影贴图大小节点
 */
export class SetShadowMapSizeNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 光源输入
    this.addInput({
      name: 'light',
      type: SocketType.DATA,
      dataType: 'Light',
      direction: SocketDirection.INPUT,
      description: '光源',
      optional: false
    });

    // 贴图大小输入
    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '贴图大小',
      defaultValue: 1024
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 光源输出
    this.addOutput({
      name: 'light',
      type: SocketType.DATA,
      dataType: 'Light',
      direction: SocketDirection.OUTPUT,
      description: '光源'
    });
  }

  protected async executeFlow(): Promise<void> {
    const light = this.getInputValue('light');
    const size = this.getInputValue('size') || 1024;

    if (!light) {
      throw new Error('光源参数不能为空');
    }

    if (typeof size !== 'number' || size <= 0) {
      throw new Error('贴图大小必须是正数');
    }

    try {
      // 设置阴影贴图大小
      if (light.threeLight && light.threeLight.shadow) {
        light.threeLight.castShadow = true;
        light.threeLight.shadow.mapSize.width = size;
        light.threeLight.shadow.mapSize.height = size;

        // 设置阴影相机参数
        if (light.threeLight.shadow.camera) {
          if (light.type === 'directional') {
            // 方向光阴影相机
            light.threeLight.shadow.camera.left = -10;
            light.threeLight.shadow.camera.right = 10;
            light.threeLight.shadow.camera.top = 10;
            light.threeLight.shadow.camera.bottom = -10;
            light.threeLight.shadow.camera.near = 0.1;
            light.threeLight.shadow.camera.far = 50;
          } else if (light.type === 'spot') {
            // 聚光灯阴影相机
            light.threeLight.shadow.camera.near = 0.1;
            light.threeLight.shadow.camera.far = 50;
          }
        }
      }

      // 输出光源
      this.setOutputValue('light', light);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('设置阴影贴图大小失败:', error);
      throw error;
    }
  }
}

/**
 * 设置LOD级别节点
 */
export class SetLODLevelNode extends FlowNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 流程输入
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '实体',
      optional: false
    });

    // LOD级别输入
    this.addInput({
      name: 'level',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'LOD级别',
      optional: false,
      defaultValue: 0
    });

    // 距离输入
    this.addInput({
      name: 'distance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '距离',
      defaultValue: 10
    });

    // 流程输出
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行'
    });

    // 实体输出
    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '实体'
    });
  }

  protected async executeFlow(): Promise<void> {
    const entity = this.getInputValue('entity');
    const level = this.getInputValue('level');
    const distance = this.getInputValue('distance') || 10;

    if (!entity) {
      throw new Error('实体参数不能为空');
    }

    if (typeof level !== 'number' || level < 0) {
      throw new Error('LOD级别必须是非负整数');
    }

    if (typeof distance !== 'number' || distance <= 0) {
      throw new Error('距离必须是正数');
    }

    try {
      // 设置LOD级别
      if (entity.lodComponent) {
        entity.lodComponent.setLevel(level, distance);
      } else {
        // 如果没有LOD组件，创建一个简单的标记
        entity.lodLevel = level;
        entity.lodDistance = distance;
      }

      // 输出实体
      this.setOutputValue('entity', entity);

      // 触发输出流程
      this.triggerFlow('exec');
    } catch (error) {
      console.error('设置LOD级别失败:', error);
      throw error;
    }
  }
}

/**
 * 注册渲染节点
 * @param registry 节点注册表
 */
export function registerRenderingNodes(registry: NodeRegistry): void {
  // 注册创建透视相机节点
  registry.registerNodeType({
    type: 'rendering/camera/createPerspectiveCamera',
    category: NodeCategory.ENTITY,
    constructor: CreatePerspectiveCameraNode,
    label: '创建透视相机',
    description: '创建透视投影相机',
    icon: 'camera',
    color: '#FF5722',
    tags: ['rendering', 'camera', 'perspective']
  });

  // 注册创建正交相机节点
  registry.registerNodeType({
    type: 'rendering/camera/createOrthographicCamera',
    category: NodeCategory.ENTITY,
    constructor: CreateOrthographicCameraNode,
    label: '创建正交相机',

    description: '创建正交投影相机',
    icon: 'camera-orthographic',
    color: '#FF5722',
    tags: ['rendering', 'camera', 'orthographic']
  });

  // 注册设置相机位置节点
  registry.registerNodeType({
    type: 'rendering/camera/setCameraPosition',
    category: NodeCategory.ENTITY,
    constructor: SetCameraPositionNode,
    label: '设置相机位置',

    description: '设置相机在3D空间的位置',
    icon: 'camera-position',
    color: '#FF5722',
    tags: ['rendering', 'camera', 'position']
  });

  // 注册设置相机目标节点
  registry.registerNodeType({
    type: 'rendering/camera/setCameraTarget',
    category: NodeCategory.ENTITY,
    constructor: SetCameraTargetNode,
    label: '设置相机目标',

    description: '设置相机观察目标点',
    icon: 'camera-target',
    color: '#FF5722',
    tags: ['rendering', 'camera', 'target']
  });

  // 注册设置相机视野节点
  registry.registerNodeType({
    type: 'rendering/camera/setCameraFOV',
    category: NodeCategory.ENTITY,
    constructor: SetCameraFOVNode,
    label: '设置相机视野',

    description: '设置相机视野角度',
    icon: 'camera-fov',
    color: '#FF5722',
    tags: ['rendering', 'camera', 'fov']
  });

  // 注册创建方向光节点
  registry.registerNodeType({
    type: 'rendering/light/createDirectionalLight',
    category: NodeCategory.ENTITY,
    constructor: CreateDirectionalLightNode,
    label: '创建方向光',

    description: '创建平行光源',
    icon: 'directional-light',
    color: '#FFC107',
    tags: ['rendering', 'light', 'directional']
  });

  // 注册创建点光源节点
  registry.registerNodeType({
    type: 'rendering/light/createPointLight',
    category: NodeCategory.ENTITY,
    constructor: CreatePointLightNode,
    label: '创建点光源',

    description: '创建点状光源',
    icon: 'point-light',
    color: '#FFC107',
    tags: ['rendering', 'light', 'point']
  });

  // 注册创建聚光灯节点
  registry.registerNodeType({
    type: 'rendering/light/createSpotLight',
    category: NodeCategory.ENTITY,
    constructor: CreateSpotLightNode,
    label: '创建聚光灯',

    description: '创建锥形光源',
    icon: 'spot-light',
    color: '#FFC107',
    tags: ['rendering', 'light', 'spot']
  });

  // 注册创建环境光节点
  registry.registerNodeType({
    type: 'rendering/light/createAmbientLight',
    category: NodeCategory.ENTITY,
    constructor: CreateAmbientLightNode,
    label: '创建环境光',

    description: '创建全局环境光',
    icon: 'ambient-light',
    color: '#FFC107',
    tags: ['rendering', 'light', 'ambient']
  });

  // 注册设置光源颜色节点
  registry.registerNodeType({
    type: 'rendering/light/setLightColor',
    category: NodeCategory.ENTITY,
    constructor: SetLightColorNode,
    label: '设置光源颜色',

    description: '设置光源的颜色属性',
    icon: 'light-color',
    color: '#FFC107',
    tags: ['rendering', 'light', 'color']
  });

  // 注册设置光源强度节点
  registry.registerNodeType({
    type: 'rendering/light/setLightIntensity',
    category: NodeCategory.ENTITY,
    constructor: SetLightIntensityNode,
    label: '设置光源强度',

    description: '设置光源的亮度强度',
    icon: 'light-intensity',
    color: '#FFC107',
    tags: ['rendering', 'light', 'intensity']
  });

  // 注册启用阴影节点
  registry.registerNodeType({
    type: 'rendering/shadow/enableShadows',
    category: NodeCategory.ENTITY,
    constructor: EnableShadowsNode,
    label: '启用阴影',

    description: '启用实时阴影渲染',
    icon: 'shadow',
    color: '#9C27B0',
    tags: ['rendering', 'shadow', 'enable']
  });

  // 注册设置阴影贴图大小节点
  registry.registerNodeType({
    type: 'rendering/shadow/setShadowMapSize',
    category: NodeCategory.ENTITY,
    constructor: SetShadowMapSizeNode,
    label: '设置阴影贴图大小',

    description: '设置阴影质量',
    icon: 'shadow-map',
    color: '#9C27B0',
    tags: ['rendering', 'shadow', 'quality']
  });

  // 注册创建基础材质节点
  registry.registerNodeType({
    type: 'rendering/material/createBasicMaterial',
    category: NodeCategory.ENTITY,
    constructor: CreateBasicMaterialNode,
    label: '创建基础材质',

    description: '创建简单材质',
    icon: 'material-basic',
    color: '#607D8B',
    tags: ['rendering', 'material', 'basic']
  });

  // 注册创建标准材质节点
  registry.registerNodeType({
    type: 'rendering/material/createStandardMaterial',
    category: NodeCategory.ENTITY,
    constructor: CreateStandardMaterialNode,
    label: '创建标准材质',

    description: '创建PBR材质',
    icon: 'material-standard',
    color: '#607D8B',
    tags: ['rendering', 'material', 'pbr']
  });

  // 注册创建物理材质节点
  registry.registerNodeType({
    type: 'rendering/material/createPhysicalMaterial',
    category: NodeCategory.ENTITY,
    constructor: CreatePhysicalMaterialNode,
    label: '创建物理材质',

    description: '创建物理基础材质',
    icon: 'material-physical',
    color: '#607D8B',
    tags: ['rendering', 'material', 'physical']
  });

  // 注册设置材质颜色节点
  registry.registerNodeType({
    type: 'rendering/material/setMaterialColor',
    category: NodeCategory.ENTITY,
    constructor: SetMaterialColorNode,
    label: '设置材质颜色',

    description: '设置材质的基础颜色',
    icon: 'material-color',
    color: '#607D8B',
    tags: ['rendering', 'material', 'color']
  });

  // 注册设置材质纹理节点
  registry.registerNodeType({
    type: 'rendering/material/setMaterialTexture',
    category: NodeCategory.ENTITY,
    constructor: SetMaterialTextureNode,
    label: '设置材质纹理',

    description: '设置材质的纹理贴图',
    icon: 'material-texture',
    color: '#607D8B',
    tags: ['rendering', 'material', 'texture']
  });

  // 注册设置材质透明度节点
  registry.registerNodeType({
    type: 'rendering/material/setMaterialOpacity',
    category: NodeCategory.ENTITY,
    constructor: SetMaterialOpacityNode,
    label: '设置材质透明度',

    description: '设置材质的透明程度',
    icon: 'material-opacity',
    color: '#607D8B',
    tags: ['rendering', 'material', 'opacity']
  });

  // 注册启用FXAA抗锯齿节点
  registry.registerNodeType({
    type: 'rendering/postprocess/enableFXAA',
    category: NodeCategory.ENTITY,
    constructor: EnableFXAANode,
    label: '启用抗锯齿',

    description: '启用FXAA抗锯齿',
    icon: 'anti-aliasing',
    color: '#795548',
    tags: ['rendering', 'postprocess', 'fxaa']
  });

  // 注册启用SSAO环境光遮蔽节点
  registry.registerNodeType({
    type: 'rendering/postprocess/enableSSAO',
    category: NodeCategory.ENTITY,
    constructor: EnableSSAONode,
    label: '启用环境光遮蔽',

    description: '启用屏幕空间环境光遮蔽',
    icon: 'ssao',
    color: '#795548',
    tags: ['rendering', 'postprocess', 'ssao']
  });

  // 注册启用辉光效果节点
  registry.registerNodeType({
    type: 'rendering/postprocess/enableBloom',
    category: NodeCategory.ENTITY,
    constructor: EnableBloomNode,
    label: '启用辉光效果',

    description: '启用Bloom后处理',
    icon: 'bloom',
    color: '#795548',
    tags: ['rendering', 'postprocess', 'bloom']
  });

  // 注册设置LOD级别节点
  registry.registerNodeType({
    type: 'rendering/lod/setLODLevel',
    category: NodeCategory.ENTITY,
    constructor: SetLODLevelNode,
    label: '设置LOD级别',

    description: '设置细节层次级别',
    icon: 'lod',
    color: '#FF9800',
    tags: ['rendering', 'lod', 'optimization']
  });

  // 注册创建刚体节点
  registry.registerNodeType({
    type: 'physics/rigidbody/createRigidBody',
    category: NodeCategory.PHYSICS,
    constructor: CreateRigidBodyNode,
    label: '创建刚体',

    description: '创建物理刚体对象',
    icon: 'rigid-body',
    color: '#E91E63',
    tags: ['physics', 'rigidbody', 'create']
  });

  // 注册设置质量节点
  registry.registerNodeType({
    type: 'physics/rigidbody/setMass',
    category: NodeCategory.PHYSICS,
    constructor: SetMassNode,
    label: '设置质量',

    description: '设置刚体质量',
    icon: 'mass',
    color: '#E91E63',
    tags: ['physics', 'rigidbody', 'mass']
  });

  // 注册设置摩擦力节点
  registry.registerNodeType({
    type: 'physics/rigidbody/setFriction',
    category: NodeCategory.PHYSICS,
    constructor: SetFrictionNode,
    label: '设置摩擦力',

    description: '设置表面摩擦系数',
    icon: 'friction',
    color: '#E91E63',
    tags: ['physics', 'rigidbody', 'friction']
  });

  // 注册设置弹性节点
  registry.registerNodeType({
    type: 'physics/rigidbody/setRestitution',
    category: NodeCategory.PHYSICS,
    constructor: SetRestitutionNode,
    label: '设置弹性',

    description: '设置碰撞弹性系数',
    icon: 'restitution',
    color: '#E91E63',
    tags: ['physics', 'rigidbody', 'restitution']
  });
}
