/**
 * 序号151-200节点验证脚本
 * 验证第6批次和第7批次节点的注册和集成状态
 */

// 模拟NodeRegistryService来检查节点注册状态
const fs = require('fs');
const path = require('path');

/**
 * 序号151-200的节点配置
 */
const BATCH6_7_NODES = [
  // 第6批次：高级物理系统和动画系统节点 (151-180)
  // 碰撞事件节点 (151-153)
  { id: 151, type: 'physics/collision/onCollisionExit', name: '碰撞结束事件' },
  { id: 152, type: 'physics/collision/onTriggerEnter', name: '触发器进入事件' },
  { id: 153, type: 'physics/collision/onTriggerExit', name: '触发器退出事件' },
  
  // 物理世界控制节点 (154-155)
  { id: 154, type: 'physics/world/setGravity', name: '设置重力' },
  { id: 155, type: 'physics/world/setTimeStep', name: '设置时间步长' },
  
  // 角色控制器节点 (156-158)
  { id: 156, type: 'physics/character/createCharacterController', name: '创建角色控制器' },
  { id: 157, type: 'physics/character/moveCharacter', name: '移动角色' },
  { id: 158, type: 'physics/character/jumpCharacter', name: '角色跳跃' },
  
  // 载具系统节点 (159-162)
  { id: 159, type: 'physics/vehicle/createVehicle', name: '创建载具' },
  { id: 160, type: 'physics/vehicle/setEngineForce', name: '设置引擎力' },
  { id: 161, type: 'physics/vehicle/setBrakeForce', name: '设置制动力' },
  { id: 162, type: 'physics/vehicle/setSteeringValue', name: '设置转向值' },
  
  // 高级物理节点 (163-165)
  { id: 163, type: 'physics/fluid/createFluidSimulation', name: '创建流体模拟' },
  { id: 164, type: 'physics/cloth/createClothSimulation', name: '创建布料模拟' },
  { id: 165, type: 'physics/destruction/createDestructible', name: '创建可破坏物体' },
  
  // 动画系统节点 (166-180)
  { id: 166, type: 'animation/clip/createAnimationClip', name: '创建动画片段' },
  { id: 167, type: 'animation/clip/addKeyframe', name: '添加关键帧' },
  { id: 168, type: 'animation/clip/setInterpolation', name: '设置插值方式' },
  { id: 169, type: 'animation/mixer/createAnimationMixer', name: '创建动画混合器' },
  { id: 170, type: 'animation/mixer/playClip', name: '播放动画片段' },
  { id: 171, type: 'animation/mixer/stopClip', name: '停止动画片段' },
  { id: 172, type: 'animation/mixer/crossFade', name: '交叉淡化' },
  { id: 173, type: 'animation/mixer/setWeight', name: '设置动画权重' },
  { id: 174, type: 'animation/bone/getBoneTransform', name: '获取骨骼变换' },
  { id: 175, type: 'animation/bone/setBoneTransform', name: '设置骨骼变换' },
  { id: 176, type: 'animation/ik/createIKChain', name: '创建IK链' },
  { id: 177, type: 'animation/ik/solveIK', name: '解算IK' },
  { id: 178, type: 'animation/morph/createMorphTarget', name: '创建变形目标' },
  { id: 179, type: 'animation/morph/setMorphWeight', name: '设置变形权重' },
  { id: 180, type: 'animation/curve/createAnimationCurve', name: '创建动画曲线' },
  
  // 第7批次：动画曲线和高级音频系统节点 (181-200)
  // 动画曲线节点 (181-185)
  { id: 181, type: 'animation/curve/evaluateCurve', name: '计算曲线值' },
  { id: 182, type: 'animation/state/createStateMachine', name: '创建状态机' },
  { id: 183, type: 'animation/state/addState', name: '添加状态' },
  { id: 184, type: 'animation/state/addTransition', name: '添加过渡' },
  { id: 185, type: 'animation/state/setCurrentState', name: '设置当前状态' },
  
  // 高级音频系统节点 (186-200)
  { id: 186, type: 'audio/source/create3DAudioSource', name: '创建3D音频源' },
  { id: 187, type: 'audio/source/setAudioPosition', name: '设置音频位置' },
  { id: 188, type: 'audio/source/setAudioVelocity', name: '设置音频速度' },
  { id: 189, type: 'audio/listener/setListenerPosition', name: '设置听者位置' },
  { id: 190, type: 'audio/listener/setListenerOrientation', name: '设置听者朝向' },
  { id: 191, type: 'audio/effect/createReverb', name: '创建混响效果' },
  { id: 192, type: 'audio/effect/createEcho', name: '创建回声效果' },
  { id: 193, type: 'audio/effect/createFilter', name: '创建滤波器' },
  { id: 194, type: 'audio/analysis/createAnalyzer', name: '创建音频分析器' },
  { id: 195, type: 'audio/analysis/getFrequencyData', name: '获取频率数据' },
  { id: 196, type: 'audio/analysis/getWaveformData', name: '获取波形数据' },
  { id: 197, type: 'audio/streaming/createAudioStream', name: '创建音频流' },
  { id: 198, type: 'audio/streaming/connectStream', name: '连接音频流' },
  { id: 199, type: 'audio/recording/startRecording', name: '开始录音' },
  { id: 200, type: 'audio/recording/stopRecording', name: '停止录音' }
];

/**
 * 检查NodeRegistryService中是否注册了指定节点类型
 */
function checkNodeRegistration() {
  try {
    // 读取NodeRegistryService文件
    const nodeRegistryPath = path.join(__dirname, 'editor', 'src', 'services', 'NodeRegistryService.ts');
    const content = fs.readFileSync(nodeRegistryPath, 'utf8');
    
    const registeredTypes = new Set();
    
    // 查找所有registerNode调用中的type字段
    const registerNodeRegex = /registerNode\s*\(\s*\{[^}]*type:\s*['"`]([^'"`]+)['"`]/g;
    let match;
    
    while ((match = registerNodeRegex.exec(content)) !== null) {
      registeredTypes.add(match[1]);
    }
    
    return registeredTypes;
  } catch (error) {
    console.error('读取NodeRegistryService文件失败:', error);
    return new Set();
  }
}

/**
 * 验证序号151-200的50个节点
 */
function validateBatch6And7Nodes() {
  console.log('🔍 开始验证序号151-200的50个节点...');
  
  const registeredTypes = checkNodeRegistration();
  
  const results = [];
  let successCount = 0;
  let warningCount = 0;
  let errorCount = 0;

  // 验证每个节点
  BATCH6_7_NODES.forEach(node => {
    const result = {
      nodeId: node.id,
      nodeType: node.type,
      nodeName: node.name,
      editorRegistered: false,
      draggable: false,
      status: 'error',
      issues: []
    };

    // 检查编辑器注册状态
    result.editorRegistered = registeredTypes.has(node.type);
    if (!result.editorRegistered) {
      result.issues.push('未在编辑器中注册');
    }

    // 检查拖拽功能（基于编辑器注册状态）
    result.draggable = result.editorRegistered;
    if (!result.draggable) {
      result.issues.push('无法拖拽使用');
    }

    // 确定状态
    if (result.editorRegistered && result.draggable) {
      result.status = 'success';
      successCount++;
    } else if (result.editorRegistered || result.draggable) {
      result.status = 'warning';
      warningCount++;
    } else {
      result.status = 'error';
      errorCount++;
    }

    results.push(result);
  });

  // 生成建议
  const recommendations = [];
  if (errorCount > 0) {
    recommendations.push(`需要修复 ${errorCount} 个节点的注册问题`);
  }
  if (warningCount > 0) {
    recommendations.push(`需要检查 ${warningCount} 个节点的部分功能`);
  }
  if (successCount === BATCH6_7_NODES.length) {
    recommendations.push('所有节点都已正确注册和集成！');
  }

  const successRate = ((successCount / BATCH6_7_NODES.length) * 100).toFixed(1);

  console.log('\n📊 验证结果统计:');
  console.log(`总计: ${BATCH6_7_NODES.length}个节点`);
  console.log(`✅ 成功: ${successCount}个 (${successRate}%)`);
  console.log(`⚠️ 警告: ${warningCount}个`);
  console.log(`❌ 错误: ${errorCount}个`);

  return {
    summary: {
      total: BATCH6_7_NODES.length,
      success: successCount,
      warning: warningCount,
      error: errorCount,
      successRate: `${successRate}%`
    },
    results,
    recommendations
  };
}

/**
 * 打印详细验证结果
 */
function printDetailedResults(validationResult) {
  console.log('\n📋 详细验证结果:');
  
  validationResult.results.forEach(result => {
    const statusIcon = result.status === 'success' ? '✅' : 
                      result.status === 'warning' ? '⚠️' : '❌';
    
    console.log(`${statusIcon} ${result.nodeId.toString().padStart(3, '0')}. ${result.nodeName}`);
    
    if (result.issues.length > 0) {
      result.issues.forEach(issue => {
        console.log(`    - ${issue}`);
      });
    }
  });

  console.log('\n💡 建议:');
  validationResult.recommendations.forEach(rec => {
    console.log(`- ${rec}`);
  });
}

// 运行验证
const result = validateBatch6And7Nodes();
printDetailedResults(result);

console.log('\n🎯 总结:');
if (result.summary.success === result.summary.total) {
  console.log('✅ 所有50个序号151-200的节点都已正确注册和集成！');
} else {
  console.log(`❌ 还有 ${result.summary.error} 个节点需要修复注册问题`);
}
