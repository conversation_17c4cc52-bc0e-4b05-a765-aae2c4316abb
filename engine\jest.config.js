/**
 * Jest配置文件
 * 用于运行视觉脚本节点的单元测试
 */

module.exports = {
  // 测试环境
  testEnvironment: 'node',
  
  // 根目录
  rootDir: '.',
  
  // 测试文件匹配模式
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.test.ts',
    '<rootDir>/src/**/__tests__/**/*.test.js'
  ],
  
  // 忽略的文件和目录
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/'
  ],
  
  // TypeScript支持
  preset: 'ts-jest',
  
  // 模块文件扩展名
  moduleFileExtensions: [
    'ts',
    'tsx',
    'js',
    'jsx',
    'json'
  ],
  
  // 转换配置
  transform: {
    '^.+\\.tsx?$': 'ts-jest'
  },
  
  // 模块名映射（用于路径别名）
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  
  // 覆盖率配置
  collectCoverage: true,
  collectCoverageFrom: [
    'src/visualscript/presets/**/*.ts',
    '!src/visualscript/presets/**/*.d.ts',
    '!src/visualscript/presets/**/__tests__/**',
    '!src/visualscript/presets/**/test-*.ts'
  ],
  
  // 覆盖率报告格式
  coverageReporters: [
    'text',
    'lcov',
    'html'
  ],
  
  // 覆盖率输出目录
  coverageDirectory: 'coverage',
  
  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  
  // 设置文件
  setupFilesAfterEnv: [
    '<rootDir>/jest.setup.js'
  ],
  
  // 详细输出
  verbose: true,
  
  // 测试超时时间（毫秒）
  testTimeout: 10000,
  
  // 清除模拟
  clearMocks: true,
  
  // 恢复模拟
  restoreMocks: true
};
