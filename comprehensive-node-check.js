const fs = require('fs');
const path = require('path');

// 检查引擎注册状态
function checkEngineRegistration(nodeType) {
  const engineFiles = [
    'engine/src/visualscript/presets/CoreNodes.ts',
    'engine/src/visualscript/presets/MathNodes.ts',
    'engine/src/visualscript/presets/LogicNodes.ts',
    'engine/src/visualscript/presets/EntityNodes.ts',
    'engine/src/visualscript/presets/PhysicsNodes.ts',
    'engine/src/visualscript/presets/SoftBodyNodes.ts',
    'engine/src/visualscript/presets/NetworkNodes.ts',
    'engine/src/visualscript/presets/AINodes.ts',
    'engine/src/visualscript/presets/TimeNodes.ts',
    'engine/src/visualscript/presets/AnimationNodes.ts',
    'engine/src/visualscript/presets/InputNodes.ts',
    'engine/src/visualscript/presets/AudioNodes.ts',
    'engine/src/visualscript/presets/DebugNodes.ts',
    'engine/src/visualscript/presets/NetworkSecurityNodes.ts',
    'engine/src/visualscript/presets/WebRTCNodes.ts',
    'engine/src/visualscript/presets/AIEmotionNodes.ts',
    'engine/src/visualscript/presets/AINLPNodes.ts',
    'engine/src/visualscript/presets/NetworkProtocolNodes.ts',
    'engine/src/visualscript/presets/StringNodes.ts',
    'engine/src/visualscript/presets/ArrayNodes.ts',
    'engine/src/visualscript/presets/ObjectNodes.ts',
    'engine/src/visualscript/presets/VariableNodes.ts',
    'engine/src/visualscript/presets/RenderingNodes.ts'
  ];

  for (const filePath of engineFiles) {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.includes(`type: '${nodeType}'`)) {
        return true;
      }
    }
  }
  return false;
}

// 检查编辑器注册状态
function checkEditorRegistration(nodeType) {
  const editorFile = 'editor/src/services/NodeRegistryService.ts';
  if (fs.existsSync(editorFile)) {
    const content = fs.readFileSync(editorFile, 'utf8');
    return content.includes(`type: '${nodeType}'`);
  }
  return false;
}

// 基于实际文件分析的节点类型映射
const actualNodeTypes = [
  // 核心节点 (已确认)
  'core/events/onStart',
  'core/events/onUpdate', 
  'core/flow/branch',
  'core/flow/sequence',
  'core/debug/print',
  
  // 数学节点 (已确认)
  'math/basic/add',
  'math/basic/subtract',
  'math/basic/multiply',
  'math/basic/divide',
  
  // 逻辑节点 (已确认)
  'logic/comparison/equal',
  'logic/comparison/notEqual',
  'logic/comparison/greater',
  'logic/comparison/less',
  
  // 实体节点 (已确认)
  'entity/get',
  'entity/component/get',
  'entity/component/add',
  'entity/component/remove',
  
  // 物理节点 (已确认)
  'physics/raycast',
  'physics/applyForce',
  
  // 软体物理节点 (已确认)
  'physics/softbody/createCloth',
  'physics/softbody/createRope',
  
  // 网络节点 (已确认)
  'network/connectToServer',
  'network/sendMessage',
  'network/events/onMessage',
  
  // AI节点 (已确认)
  'ai/animation/generateBodyAnimation',
  'ai/animation/generateFacialAnimation',
  
  // 字符串节点 (已确认)
  'string/concat',
  'string/substring',
  'string/replace',
  'string/split',
  'string/length',
  'string/toUpperCase',
  'string/toLowerCase',
  'string/trim',
  
  // 数组节点 (已确认)
  'array/push',
  'array/pop',
  'array/length',
  'array/get',
  'array/set',
  'array/indexOf',
  'array/slice',
  'array/sort',
  
  // 对象节点 (已确认)
  'object/getProperty',
  'object/setProperty',
  'object/hasProperty',
  'object/keys',
  'object/values',
  'object/merge',
  'object/clone',
  
  // 变量节点 (已确认)
  'variable/get',
  'variable/set',
  'variable/increment',
  'variable/decrement',
  'variable/exists',
  'variable/delete',
  'variable/type'
];

console.log('=== 检查实际已实现的节点状态 ===\n');

let completedCount = 0;
let engineRegisteredCount = 0;
let editorRegisteredCount = 0;

actualNodeTypes.forEach((nodeType, index) => {
  const engineRegistered = checkEngineRegistration(nodeType);
  const editorRegistered = checkEditorRegistration(nodeType);
  const isCompleted = engineRegistered && editorRegistered;

  if (engineRegistered) engineRegisteredCount++;
  if (editorRegistered) editorRegisteredCount++;
  if (isCompleted) completedCount++;

  const status = isCompleted ? '✅' : '';
  const engineStatus = engineRegistered ? '引擎✓' : '引擎✗';
  const editorStatus = editorRegistered ? '编辑器✓' : '编辑器✗';
  
  const nodeId = (index + 1).toString().padStart(3, '0');
  console.log(`${status} ${nodeId}. ${nodeType} - ${engineStatus} ${editorStatus}`);
});

console.log(`\n=== 统计结果 ===`);
console.log(`完全完成的节点: ${completedCount}/${actualNodeTypes.length}`);
console.log(`引擎注册的节点: ${engineRegisteredCount}/${actualNodeTypes.length}`);
console.log(`编辑器注册的节点: ${editorRegisteredCount}/${actualNodeTypes.length}`);
