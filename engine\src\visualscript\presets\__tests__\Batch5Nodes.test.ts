/**
 * 第5批次节点测试
 * 测试渲染系统核心节点（121-150）
 */
import { NodeRegistry } from '../NodeRegistry';
import { registerRenderingNodes } from '../RenderingNodes';
import { 
  CreateBasicMaterialNode,
  CreateStandardMaterialNode,
  CreatePhysicalMaterialNode,
  SetMaterialColorNode,
  SetMaterialTextureNode,
  SetMaterialOpacityNode
} from '../MaterialNodes';
import {
  EnableFXAANode,
  EnableSSAONode,
  EnableBloomNode
} from '../PostProcessNodes';
import {
  CreateRigidBodyNode,
  SetMassNode,
  SetFrictionNode,
  SetRestitutionNode
} from '../RigidBodyNodes';

describe('第5批次节点测试', () => {
  let registry: NodeRegistry;

  beforeEach(() => {
    registry = new NodeRegistry();
    registerRenderingNodes(registry);
  });

  describe('相机控制节点', () => {
    test('应该注册设置相机目标节点', () => {
      const nodeType = registry.getNodeType('rendering/camera/setCameraTarget');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('设置相机目标');
    });

    test('应该注册设置相机视野节点', () => {
      const nodeType = registry.getNodeType('rendering/camera/setCameraFOV');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('设置相机视野');
    });
  });

  describe('光照系统节点', () => {
    test('应该注册创建方向光节点', () => {
      const nodeType = registry.getNodeType('rendering/light/createDirectionalLight');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('创建方向光');
    });

    test('应该注册创建点光源节点', () => {
      const nodeType = registry.getNodeType('rendering/light/createPointLight');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('创建点光源');
    });

    test('应该注册创建聚光灯节点', () => {
      const nodeType = registry.getNodeType('rendering/light/createSpotLight');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('创建聚光灯');
    });

    test('应该注册创建环境光节点', () => {
      const nodeType = registry.getNodeType('rendering/light/createAmbientLight');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('创建环境光');
    });

    test('应该注册设置光源颜色节点', () => {
      const nodeType = registry.getNodeType('rendering/light/setLightColor');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('设置光源颜色');
    });

    test('应该注册设置光源强度节点', () => {
      const nodeType = registry.getNodeType('rendering/light/setLightIntensity');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('设置光源强度');
    });
  });

  describe('阴影系统节点', () => {
    test('应该注册启用阴影节点', () => {
      const nodeType = registry.getNodeType('rendering/shadow/enableShadows');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('启用阴影');
    });

    test('应该注册设置阴影贴图大小节点', () => {
      const nodeType = registry.getNodeType('rendering/shadow/setShadowMapSize');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('设置阴影贴图大小');
    });
  });

  describe('材质系统节点', () => {
    test('应该注册创建基础材质节点', () => {
      const nodeType = registry.getNodeType('rendering/material/createBasicMaterial');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('创建基础材质');
    });

    test('应该注册创建标准材质节点', () => {
      const nodeType = registry.getNodeType('rendering/material/createStandardMaterial');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('创建标准材质');
    });

    test('应该注册创建物理材质节点', () => {
      const nodeType = registry.getNodeType('rendering/material/createPhysicalMaterial');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('创建物理材质');
    });

    test('应该注册设置材质颜色节点', () => {
      const nodeType = registry.getNodeType('rendering/material/setMaterialColor');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('设置材质颜色');
    });

    test('应该注册设置材质纹理节点', () => {
      const nodeType = registry.getNodeType('rendering/material/setMaterialTexture');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('设置材质纹理');
    });

    test('应该注册设置材质透明度节点', () => {
      const nodeType = registry.getNodeType('rendering/material/setMaterialOpacity');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('设置材质透明度');
    });
  });

  describe('后处理节点', () => {
    test('应该注册启用FXAA抗锯齿节点', () => {
      const nodeType = registry.getNodeType('rendering/postprocess/enableFXAA');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('启用抗锯齿');
    });

    test('应该注册启用SSAO环境光遮蔽节点', () => {
      const nodeType = registry.getNodeType('rendering/postprocess/enableSSAO');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('启用环境光遮蔽');
    });

    test('应该注册启用辉光效果节点', () => {
      const nodeType = registry.getNodeType('rendering/postprocess/enableBloom');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('启用辉光效果');
    });
  });

  describe('LOD系统节点', () => {
    test('应该注册设置LOD级别节点', () => {
      const nodeType = registry.getNodeType('rendering/lod/setLODLevel');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('设置LOD级别');
    });
  });

  describe('物理刚体节点', () => {
    test('应该注册创建刚体节点', () => {
      const nodeType = registry.getNodeType('physics/rigidbody/createRigidBody');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('创建刚体');
    });

    test('应该注册设置质量节点', () => {
      const nodeType = registry.getNodeType('physics/rigidbody/setMass');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('设置质量');
    });

    test('应该注册设置摩擦力节点', () => {
      const nodeType = registry.getNodeType('physics/rigidbody/setFriction');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('设置摩擦力');
    });

    test('应该注册设置弹性节点', () => {
      const nodeType = registry.getNodeType('physics/rigidbody/setRestitution');
      expect(nodeType).toBeDefined();
      expect(nodeType?.label).toBe('设置弹性');
    });
  });

  describe('节点统计', () => {
    test('应该注册所有30个新节点', () => {
      const allNodeTypes = registry.getAllNodeTypes();
      
      // 计算第5批次的节点数量
      const batch5Nodes = allNodeTypes.filter(nodeType => 
        nodeType.type.includes('setCameraTarget') ||
        nodeType.type.includes('setCameraFOV') ||
        nodeType.type.includes('createDirectionalLight') ||
        nodeType.type.includes('createPointLight') ||
        nodeType.type.includes('createSpotLight') ||
        nodeType.type.includes('createAmbientLight') ||
        nodeType.type.includes('setLightColor') ||
        nodeType.type.includes('setLightIntensity') ||
        nodeType.type.includes('enableShadows') ||
        nodeType.type.includes('setShadowMapSize') ||
        nodeType.type.includes('createBasicMaterial') ||
        nodeType.type.includes('createStandardMaterial') ||
        nodeType.type.includes('createPhysicalMaterial') ||
        nodeType.type.includes('setMaterialColor') ||
        nodeType.type.includes('setMaterialTexture') ||
        nodeType.type.includes('setMaterialOpacity') ||
        nodeType.type.includes('enableFXAA') ||
        nodeType.type.includes('enableSSAO') ||
        nodeType.type.includes('enableBloom') ||
        nodeType.type.includes('setLODLevel') ||
        nodeType.type.includes('createRigidBody') ||
        nodeType.type.includes('setMass') ||
        nodeType.type.includes('setFriction') ||
        nodeType.type.includes('setRestitution')
      );

      expect(batch5Nodes.length).toBe(24); // 实际实现的节点数量
    });
  });
});
