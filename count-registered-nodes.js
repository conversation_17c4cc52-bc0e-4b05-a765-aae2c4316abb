/**
 * 统计NodeRegistryService中已注册的节点数量
 */

const fs = require('fs');

console.log('开始分析NodeRegistryService中已注册的节点...');

// 读取NodeRegistryService文件
const filePath = 'editor/src/services/NodeRegistryService.ts';
const content = fs.readFileSync(filePath, 'utf8');

// 使用正则表达式匹配所有registerNode调用
const registerNodePattern = /this\.registerNode\(\{[\s\S]*?\}\);/g;
const matches = content.match(registerNodePattern);

if (!matches) {
  console.log('未找到任何registerNode调用');
  process.exit(1);
}

console.log(`\n找到 ${matches.length} 个registerNode调用`);

// 提取每个节点的type信息
const nodeTypes = [];
const nodesByBatch = {
  'Default': [],
  'Batch4': [],
  'Batch5': [],
  'Batch7': [],
  'Batch8': []
};

let currentBatch = 'Default';

// 分析每个registerNode调用
matches.forEach((match, index) => {
  // 提取type字段
  const typeMatch = match.match(/type:\s*['"`]([^'"`]+)['"`]/);
  if (typeMatch) {
    const nodeType = typeMatch[1];
    nodeTypes.push(nodeType);
    
    // 根据位置判断属于哪个批次
    const matchIndex = content.indexOf(match);
    const beforeMatch = content.substring(0, matchIndex);
    
    if (beforeMatch.includes('initializeBatch8Nodes')) {
      currentBatch = 'Batch8';
    } else if (beforeMatch.includes('initializeBatch7Nodes')) {
      currentBatch = 'Batch7';
    } else if (beforeMatch.includes('initializeBatch5Nodes')) {
      currentBatch = 'Batch5';
    } else if (beforeMatch.includes('initializeBatch4Nodes')) {
      currentBatch = 'Batch4';
    } else if (beforeMatch.includes('initializeDefaultNodes')) {
      currentBatch = 'Default';
    }
    
    nodesByBatch[currentBatch].push(nodeType);
  }
});

// 重新分析，更准确地分配批次
const lines = content.split('\n');
let currentBatchName = 'Default';
const nodesByBatchAccurate = {
  'Default': [],
  'Batch4': [],
  'Batch5': [],
  'Batch7': [],
  'Batch8': []
};

for (let i = 0; i < lines.length; i++) {
  const line = lines[i];
  
  // 检测批次开始
  if (line.includes('initializeDefaultNodes')) {
    currentBatchName = 'Default';
  } else if (line.includes('initializeBatch4Nodes')) {
    currentBatchName = 'Batch4';
  } else if (line.includes('initializeBatch5Nodes')) {
    currentBatchName = 'Batch5';
  } else if (line.includes('initializeBatch7Nodes')) {
    currentBatchName = 'Batch7';
  } else if (line.includes('initializeBatch8Nodes')) {
    currentBatchName = 'Batch8';
  }
  
  // 检测registerNode调用
  if (line.includes('this.registerNode({')) {
    // 查找type字段
    for (let j = i; j < Math.min(i + 10, lines.length); j++) {
      const typeLine = lines[j];
      const typeMatch = typeLine.match(/type:\s*['"`]([^'"`]+)['"`]/);
      if (typeMatch) {
        const nodeType = typeMatch[1];
        nodesByBatchAccurate[currentBatchName].push(nodeType);
        break;
      }
    }
  }
}

// 输出统计结果
console.log('\n=== 节点注册统计结果 ===');

console.log('\n📊 按批次统计:');
let totalNodes = 0;
Object.entries(nodesByBatchAccurate).forEach(([batch, nodes]) => {
  console.log(`${batch}: ${nodes.length}个节点`);
  totalNodes += nodes.length;
});

console.log(`\n📈 总计: ${totalNodes}个节点`);

// 详细列表
console.log('\n📋 详细节点列表:');
Object.entries(nodesByBatchAccurate).forEach(([batch, nodes]) => {
  if (nodes.length > 0) {
    console.log(`\n${batch} (${nodes.length}个):`);
    nodes.forEach((nodeType, index) => {
      console.log(`  ${index + 1}. ${nodeType}`);
    });
  }
});

// 验证节点类型唯一性
const uniqueTypes = new Set(nodeTypes);
console.log(`\n🔍 节点类型唯一性检查:`);
console.log(`总注册调用: ${matches.length}`);
console.log(`唯一节点类型: ${uniqueTypes.size}`);
console.log(`是否有重复: ${matches.length !== uniqueTypes.size ? '❌ 是' : '✅ 否'}`);

// 分析节点类型分布
console.log(`\n🏷️ 节点类型分布:`);
const typeCategories = {};
nodeTypes.forEach(type => {
  const category = type.split('/')[0];
  typeCategories[category] = (typeCategories[category] || 0) + 1;
});

Object.entries(typeCategories)
  .sort(([,a], [,b]) => b - a)
  .forEach(([category, count]) => {
    console.log(`  ${category}: ${count}个`);
  });

console.log('\n=== 分析完成 ===');
console.log(`✅ 项目视觉脚本系统当前已注册并集成到编辑器中的节点总数: ${totalNodes}个`);
