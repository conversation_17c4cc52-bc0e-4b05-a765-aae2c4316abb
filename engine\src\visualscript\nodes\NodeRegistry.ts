/**
 * 视觉脚本节点注册表
 * 用于注册和管理节点类型
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Node, NodeCategory } from './Node';

/**
 * 节点构造函数类型
 */
export type NodeConstructor = new (...args: any[]) => Node;

/**
 * 节点类型信息
 */
export interface NodeTypeInfo {
  /** 节点类型名称 */
  type: string;
  /** 节点类别 */
  category: NodeCategory;
  /** 节点构造函数 */
  constructor: NodeConstructor;
  /** 节点描述 */
  description?: string;
  /** 节点标签 */
  label?: string;
  /** 节点图标 */
  icon?: string;
  /** 节点颜色 */
  color?: string;
  /** 是否已弃用 */
  deprecated?: boolean;
  /** 弃用原因 */
  deprecatedReason?: string;
  /** 是否实验性 */
  experimental?: boolean;
  /** 标签列表 */
  tags?: string[];
  /** 示例代码 */
  examples?: string[];
  /** 文档链接 */
  documentationUrl?: string;
  /** 版本 */
  version?: string;
  /** 作者 */
  author?: string;
  /** 许可证 */
  license?: string;
  /** 依赖项 */
  dependencies?: string[];
  /** 自定义元数据 */
  [key: string]: any;
}

/**
 * 节点注册表
 * 用于注册和管理节点类型
 */
export class NodeRegistry extends EventEmitter {
  /** 节点类型映射 */
  private nodeTypes: Map<string, NodeTypeInfo> = new Map();
  
  /** 节点类别映射 */
  private categories: Map<NodeCategory, Set<string>> = new Map();
  
  /** 节点标签映射 */
  private tags: Map<string, Set<string>> = new Map();
  
  /**
   * 创建节点注册表
   */
  constructor() {
    super();
    
    // 初始化类别映射
    for (const category of Object.values(NodeCategory)) {
      this.categories.set(category, new Set());
    }
  }
  
  /**
   * 注册节点类型
   * @param info 节点类型信息
   * @returns 是否注册成功
   */
  public registerNodeType(info: NodeTypeInfo): boolean {
    // 检查是否已存在
    if (this.nodeTypes.has(info.type)) {
      console.warn(`节点类型已存在: ${info.type}`);
      return false;
    }
    
    // 注册节点类型
    this.nodeTypes.set(info.type, info);
    
    // 添加到类别映射
    const categorySet = this.categories.get(info.category) || new Set();
    categorySet.add(info.type);
    this.categories.set(info.category, categorySet);
    
    // 添加到标签映射
    if (info.tags) {
      for (const tag of info.tags) {
        const tagSet = this.tags.get(tag) || new Set();
        tagSet.add(info.type);
        this.tags.set(tag, tagSet);
      }
    }
    
    // 触发注册事件
    this.emit('nodeTypeRegistered', info);
    
    return true;
  }
  
  /**
   * 注销节点类型
   * @param type 节点类型名称
   * @returns 是否注销成功
   */
  public unregisterNodeType(type: string): boolean {
    // 检查是否存在
    const info = this.nodeTypes.get(type);
    
    if (!info) {
      return false;
    }
    
    // 从类别映射中移除
    const categorySet = this.categories.get(info.category);
    
    if (categorySet) {
      categorySet.delete(type);
    }
    
    // 从标签映射中移除
    if (info.tags) {
      for (const tag of info.tags) {
        const tagSet = this.tags.get(tag);
        
        if (tagSet) {
          tagSet.delete(type);
          
          // 如果标签集合为空，移除标签
          if (tagSet.size === 0) {
            this.tags.delete(tag);
          }
        }
      }
    }
    
    // 从节点类型映射中移除
    this.nodeTypes.delete(type);
    
    // 触发注销事件
    this.emit('nodeTypeUnregistered', info);
    
    return true;
  }
  
  /**
   * 获取节点类型
   * @param type 节点类型名称
   * @returns 节点构造函数
   */
  public getNodeType(type: string): NodeConstructor | undefined {
    const info = this.nodeTypes.get(type);
    return info ? info.constructor : undefined;
  }

  /**
   * 创建节点实例
   * @param type 节点类型名称
   * @param options 节点选项
   * @returns 节点实例
   */
  public createNode(type: string, options: any): Node | undefined {
    const NodeConstructor = this.getNodeType(type);
    if (!NodeConstructor) {
      console.warn(`未找到节点类型: ${type}`);
      return undefined;
    }

    try {
      return new NodeConstructor(options);
    } catch (error) {
      console.error(`创建节点失败: ${type}`, error);
      return undefined;
    }
  }
  
  /**
   * 获取节点类型信息
   * @param type 节点类型名称
   * @returns 节点类型信息
   */
  public getNodeTypeInfo(type: string): NodeTypeInfo | undefined {
    return this.nodeTypes.get(type);
  }
  
  /**
   * 获取所有节点类型
   * @returns 节点类型信息列表
   */
  public getAllNodeTypes(): NodeTypeInfo[] {
    return Array.from(this.nodeTypes.values());
  }
  
  /**
   * 获取指定类别的节点类型
   * @param category 节点类别
   * @returns 节点类型信息列表
   */
  public getNodeTypesByCategory(category: NodeCategory): NodeTypeInfo[] {
    const categorySet = this.categories.get(category);
    
    if (!categorySet) {
      return [];
    }
    
    return Array.from(categorySet).map(type => this.nodeTypes.get(type)!);
  }
  
  /**
   * 获取指定标签的节点类型
   * @param tag 标签
   * @returns 节点类型信息列表
   */
  public getNodeTypesByTag(tag: string): NodeTypeInfo[] {
    const tagSet = this.tags.get(tag);
    
    if (!tagSet) {
      return [];
    }
    
    return Array.from(tagSet).map(type => this.nodeTypes.get(type)!);
  }
  
  /**
   * 获取所有类别
   * @returns 类别列表
   */
  public getAllCategories(): NodeCategory[] {
    return Array.from(this.categories.keys());
  }
  
  /**
   * 获取所有标签
   * @returns 标签列表
   */
  public getAllTags(): string[] {
    return Array.from(this.tags.keys());
  }
  
  /**
   * 搜索节点类型
   * @param query 搜索查询
   * @returns 节点类型信息列表
   */
  public searchNodeTypes(query: string): NodeTypeInfo[] {
    if (!query) {
      return this.getAllNodeTypes();
    }
    
    const lowerQuery = query.toLowerCase();
    
    return this.getAllNodeTypes().filter(info => {
      // 匹配类型名称
      if (info.type.toLowerCase().includes(lowerQuery)) {
        return true;
      }
      
      // 匹配标签
      if (info.label && info.label.toLowerCase().includes(lowerQuery)) {
        return true;
      }
      
      // 匹配描述
      if (info.description && info.description.toLowerCase().includes(lowerQuery)) {
        return true;
      }
      
      // 匹配标签
      if (info.tags && info.tags.some(tag => tag.toLowerCase().includes(lowerQuery))) {
        return true;
      }
      
      return false;
    });
  }
  
  /**
   * 清空注册表
   */
  public clear(): void {
    // 清空节点类型映射
    this.nodeTypes.clear();
    
    // 清空类别映射
    for (const category of Object.values(NodeCategory)) {
      this.categories.set(category, new Set());
    }
    
    // 清空标签映射
    this.tags.clear();
    
    // 触发清空事件
    this.emit('cleared');
  }
}
