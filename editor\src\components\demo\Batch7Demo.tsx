/**
 * 第7批次节点演示组件
 * 展示动画系统扩展节点的实际使用效果
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Button, Space, Slider, message, Tabs, Row, Col, Progress } from 'antd';
import {
  PlayCircleOutlined,
  SoundOutlined,
  EyeOutlined,
  SettingOutlined,
  ExperimentOutlined
} from '@ant-design/icons';

const { TabPane } = Tabs;

interface AnimationState {
  name: string;
  isActive: boolean;
  progress: number;
}

interface AudioVisualization {
  frequencies: number[];
  waveform: number[];
  volume: number;
}

/**
 * 第7批次节点演示组件
 */
const Batch7Demo: React.FC = () => {
  // 动画状态
  const [animationStates, setAnimationStates] = useState<AnimationState[]>([
    { name: 'idle', isActive: true, progress: 0 },
    { name: 'walk', isActive: false, progress: 0 },
    { name: 'run', isActive: false, progress: 0 }
  ]);
  const [currentState, setCurrentState] = useState('idle');
  const [animationSpeed, setAnimationSpeed] = useState(1.0);

  // 音频状态
  const [audioVisualization, setAudioVisualization] = useState<AudioVisualization>({
    frequencies: new Array(32).fill(0),
    waveform: new Array(64).fill(128),
    volume: 0
  });
  const [audioPosition, setAudioPosition] = useState({ x: 0, y: 0, z: 0 });
  const [listenerPosition, setListenerPosition] = useState({ x: 0, y: 0, z: 0 });
  const [reverbLevel, setReverbLevel] = useState(0.3);

  // 场景状态
  const [sceneObjects, setSceneObjects] = useState(15);
  const [cullingEnabled, setCullingEnabled] = useState(true);
  const [batchingEnabled, setBatchingEnabled] = useState(true);
  const [instancingCount, setInstancingCount] = useState(100);

  // 性能统计
  const [performanceStats] = useState({
    fps: 60,
    drawCalls: 25,
    triangles: 15000,
    memoryUsage: 45
  });

  const animationIntervalRef = useRef<NodeJS.Timeout>();
  const audioIntervalRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    // 模拟动画更新
    animationIntervalRef.current = setInterval(() => {
      setAnimationStates(prev => prev.map(state => ({
        ...state,
        progress: state.isActive ? (state.progress + animationSpeed) % 100 : state.progress
      })));
    }, 100);

    // 模拟音频可视化数据
    audioIntervalRef.current = setInterval(() => {
      setAudioVisualization(prev => ({
        frequencies: prev.frequencies.map(() => Math.random() * 100),
        waveform: prev.waveform.map(() => Math.random() * 255),
        volume: Math.random() * 100
      }));
    }, 50);

    return () => {
      if (animationIntervalRef.current) clearInterval(animationIntervalRef.current);
      if (audioIntervalRef.current) clearInterval(audioIntervalRef.current);
    };
  }, [animationSpeed]);

  /**
   * 切换动画状态
   */
  const switchAnimationState = (stateName: string) => {
    setCurrentState(stateName);
    setAnimationStates(prev => prev.map(state => ({
      ...state,
      isActive: state.name === stateName
    })));
    message.success(`切换到动画状态: ${stateName}`);
  };

  /**
   * 更新音频位置
   */
  const updateAudioPosition = (axis: 'x' | 'y' | 'z', value: number) => {
    setAudioPosition(prev => ({ ...prev, [axis]: value }));
  };

  /**
   * 更新听者位置
   */
  const updateListenerPosition = (axis: 'x' | 'y' | 'z', value: number) => {
    setListenerPosition(prev => ({ ...prev, [axis]: value }));
  };

  /**
   * 渲染频率可视化
   */
  const renderFrequencyBars = () => {
    return (
      <div style={{ display: 'flex', alignItems: 'end', height: '100px', gap: '2px' }}>
        {audioVisualization.frequencies.map((freq, index) => (
          <div
            key={index}
            style={{
              width: '8px',
              height: `${freq}%`,
              backgroundColor: `hsl(${freq * 3.6}, 70%, 50%)`,
              borderRadius: '2px 2px 0 0'
            }}
          />
        ))}
      </div>
    );
  };

  /**
   * 渲染波形可视化
   */
  const renderWaveform = () => {
    const points = audioVisualization.waveform.map((value, index) => 
      `${index * 4},${128 - value / 2}`
    ).join(' ');

    return (
      <svg width="256" height="128" style={{ border: '1px solid #d9d9d9' }}>
        <polyline
          points={points}
          fill="none"
          stroke="#1890ff"
          strokeWidth="2"
        />
        <line x1="0" y1="64" x2="256" y2="64" stroke="#d9d9d9" strokeWidth="1" />
      </svg>
    );
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title="第7批次节点演示 - 动画系统扩展" style={{ marginBottom: '24px' }}>
        <Tabs defaultActiveKey="animation">
          <TabPane tab={<span><PlayCircleOutlined />动画系统</span>} key="animation">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card title="动画状态机" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <strong>当前状态:</strong> {currentState}
                    </div>
                    <Space>
                      {animationStates.map(state => (
                        <Button
                          key={state.name}
                          type={state.isActive ? 'primary' : 'default'}
                          onClick={() => switchAnimationState(state.name)}
                        >
                          {state.name}
                        </Button>
                      ))}
                    </Space>
                    <div>
                      <div>动画速度: {animationSpeed.toFixed(1)}x</div>
                      <Slider
                        min={0.1}
                        max={3.0}
                        step={0.1}
                        value={animationSpeed}
                        onChange={setAnimationSpeed}
                      />
                    </div>
                  </Space>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="动画进度" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    {animationStates.map(state => (
                      <div key={state.name}>
                        <div style={{ marginBottom: '8px' }}>
                          {state.name}: {state.progress.toFixed(1)}%
                        </div>
                        <Progress 
                          percent={state.progress} 
                          status={state.isActive ? 'active' : 'normal'}
                          showInfo={false}
                        />
                      </div>
                    ))}
                  </Space>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab={<span><SoundOutlined />音频系统</span>} key="audio">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card title="3D音频控制" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <strong>音频源位置</strong>
                      <div>X: <Slider min={-10} max={10} value={audioPosition.x} onChange={(v) => updateAudioPosition('x', v)} /></div>
                      <div>Y: <Slider min={-10} max={10} value={audioPosition.y} onChange={(v) => updateAudioPosition('y', v)} /></div>
                      <div>Z: <Slider min={-10} max={10} value={audioPosition.z} onChange={(v) => updateAudioPosition('z', v)} /></div>
                    </div>
                    <div>
                      <strong>听者位置</strong>
                      <div>X: <Slider min={-10} max={10} value={listenerPosition.x} onChange={(v) => updateListenerPosition('x', v)} /></div>
                      <div>Y: <Slider min={-10} max={10} value={listenerPosition.y} onChange={(v) => updateListenerPosition('y', v)} /></div>
                      <div>Z: <Slider min={-10} max={10} value={listenerPosition.z} onChange={(v) => updateListenerPosition('z', v)} /></div>
                    </div>
                    <div>
                      <strong>混响级别: {reverbLevel.toFixed(2)}</strong>
                      <Slider min={0} max={1} step={0.01} value={reverbLevel} onChange={setReverbLevel} />
                    </div>
                  </Space>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="音频可视化" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <strong>频谱分析</strong>
                      {renderFrequencyBars()}
                    </div>
                    <div>
                      <strong>波形显示</strong>
                      {renderWaveform()}
                    </div>
                    <div>
                      <strong>音量: {audioVisualization.volume.toFixed(1)}%</strong>
                      <Progress percent={audioVisualization.volume} showInfo={false} />
                    </div>
                  </Space>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab={<span><EyeOutlined />场景管理</span>} key="scene">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card title="场景优化" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <strong>场景对象数量: {sceneObjects}</strong>
                      <Slider min={1} max={100} value={sceneObjects} onChange={setSceneObjects} />
                    </div>
                    <div>
                      <Space>
                        <Button 
                          type={cullingEnabled ? 'primary' : 'default'}
                          onClick={() => setCullingEnabled(!cullingEnabled)}
                        >
                          视锥体剔除: {cullingEnabled ? '开启' : '关闭'}
                        </Button>
                        <Button 
                          type={batchingEnabled ? 'primary' : 'default'}
                          onClick={() => setBatchingEnabled(!batchingEnabled)}
                        >
                          批处理: {batchingEnabled ? '开启' : '关闭'}
                        </Button>
                      </Space>
                    </div>
                    <div>
                      <strong>实例化数量: {instancingCount}</strong>
                      <Slider min={10} max={1000} value={instancingCount} onChange={setInstancingCount} />
                    </div>
                  </Space>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="性能统计" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>FPS: <Progress percent={(performanceStats.fps / 60) * 100} format={() => `${performanceStats.fps}`} /></div>
                    <div>绘制调用: <Progress percent={(performanceStats.drawCalls / 50) * 100} format={() => `${performanceStats.drawCalls}`} /></div>
                    <div>三角形数: <Progress percent={(performanceStats.triangles / 30000) * 100} format={() => `${performanceStats.triangles}`} /></div>
                    <div>内存使用: <Progress percent={performanceStats.memoryUsage} format={() => `${performanceStats.memoryUsage}MB`} /></div>
                  </Space>
                </Card>
              </Col>
            </Row>
          </TabPane>
        </Tabs>

        <div style={{ marginTop: '16px', textAlign: 'center' }}>
          <Space>
            <Button 
              type="primary" 
              icon={<ExperimentOutlined />}
              onClick={() => window.open('/visual-script-editor', '_blank')}
            >
              在节点编辑器中体验
            </Button>
            <Button 
              icon={<SettingOutlined />}
              onClick={() => message.info('演示功能基于第7批次节点实现')}
            >
              查看实现详情
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default Batch7Demo;
