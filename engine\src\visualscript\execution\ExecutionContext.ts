/**
 * 视觉脚本执行上下文
 * 提供视觉脚本执行时的上下文环境
 */
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { VisualScriptEngine } from '../VisualScriptEngine';

/**
 * 执行上下文选项
 */
export interface ExecutionContextOptions {
  /** 视觉脚本引擎 */
  engine: VisualScriptEngine;
  /** 所属实体 */
  entity: Entity;
  /** 所属世界 */
  world: World;
}

/**
 * 视觉脚本执行上下文
 * 提供视觉脚本执行时的上下文环境
 */
export class ExecutionContext {
  /** 视觉脚本引擎 */
  public readonly engine: VisualScriptEngine;
  
  /** 所属实体 */
  public readonly entity: Entity;
  
  /** 所属世界 */
  public readonly world: World;
  
  /** 上下文变量 */
  private variables: Map<string, any> = new Map();
  
  /** 上下文函数 */
  private functions: Map<string, Function> = new Map();
  
  /**
   * 创建执行上下文
   * @param options 上下文选项
   */
  constructor(options: ExecutionContextOptions) {
    this.engine = options.engine;
    this.entity = options.entity;
    this.world = options.world;
    
    // 初始化上下文
    this.initialize();
  }
  
  /**
   * 初始化上下文
   */
  private initialize(): void {
    // 注册内置函数
    this.registerBuiltinFunctions();
  }
  
  /**
   * 注册内置函数
   */
  private registerBuiltinFunctions(): void {
    // 日志函数
    this.registerFunction('log', (message: any) => {
      console.log(`[VisualScript] ${message}`);
    });
    
    this.registerFunction('warn', (message: any) => {
      console.warn(`[VisualScript] ${message}`);
    });
    
    this.registerFunction('error', (message: any) => {
      console.error(`[VisualScript] ${message}`);
    });
    
    // 时间函数
    this.registerFunction('delay', (ms: number) => {
      if (typeof ms !== 'number' || ms < 0) {
        throw new Error('延迟时间必须是非负数');
      }
      return new Promise(resolve => setTimeout(resolve, ms));
    });

    this.registerFunction('now', () => {
      return Date.now();
    });
    
    // 实体函数
    this.registerFunction('getEntity', (id: string) => {
      return this.world.getEntity(id);
    });
    
    this.registerFunction('createEntity', (name?: string) => {
      return this.world.createEntity(name);
    });
    
    this.registerFunction('destroyEntity', (entity: Entity) => {
      this.world.removeEntity(entity);
    });
    
    // 组件函数
    this.registerFunction('getComponent', (entity: Entity, type: string) => {
      return entity.getComponent(type);
    });
    
    this.registerFunction('addComponent', (entity: Entity, component: any) => {
      if (!entity || typeof entity.addComponent !== 'function') {
        throw new Error('第一个参数必须是有效的实体');
      }
      if (!component) {
        throw new Error('第二个参数必须是组件实例');
      }
      return entity.addComponent(component);
    });
    
    this.registerFunction('removeComponent', (entity: Entity, type: string) => {
      entity.removeComponent(type);
    });
    
    // 数学函数
    this.registerFunction('random', (min: number, max: number) => {
      if (typeof min !== 'number' || typeof max !== 'number') {
        throw new Error('参数必须是数字');
      }
      if (min > max) {
        throw new Error('最小值不能大于最大值');
      }
      return Math.random() * (max - min) + min;
    });

    this.registerFunction('randomInt', (min: number, max: number) => {
      if (typeof min !== 'number' || typeof max !== 'number') {
        throw new Error('参数必须是数字');
      }
      if (min > max) {
        throw new Error('最小值不能大于最大值');
      }
      return Math.floor(Math.random() * (max - min + 1)) + min;
    });

    this.registerFunction('clamp', (value: number, min: number, max: number) => {
      if (typeof value !== 'number' || typeof min !== 'number' || typeof max !== 'number') {
        throw new Error('所有参数必须是数字');
      }
      if (min > max) {
        throw new Error('最小值不能大于最大值');
      }
      return Math.min(Math.max(value, min), max);
    });

    this.registerFunction('lerp', (a: number, b: number, t: number) => {
      if (typeof a !== 'number' || typeof b !== 'number' || typeof t !== 'number') {
        throw new Error('所有参数必须是数字');
      }
      return a + (b - a) * t;
    });
    
    // 字符串函数
    this.registerFunction('concat', (...args: any[]) => {
      return args.join('');
    });
    
    this.registerFunction('format', (template: string, ...args: any[]) => {
      return template.replace(/{(\d+)}/g, (match, index) => {
        return args[index] !== undefined ? args[index] : match;
      });
    });
    
    // 数组函数
    this.registerFunction('createArray', (...items: any[]) => {
      return [...items];
    });

    this.registerFunction('arrayLength', (array: any[]) => {
      if (!Array.isArray(array)) {
        throw new Error('参数必须是数组');
      }
      return array.length;
    });

    this.registerFunction('arrayGet', (array: any[], index: number) => {
      if (!Array.isArray(array)) {
        throw new Error('第一个参数必须是数组');
      }
      if (typeof index !== 'number' || index < 0 || index >= array.length) {
        return undefined;
      }
      return array[index];
    });

    this.registerFunction('arraySet', (array: any[], index: number, value: any) => {
      if (!Array.isArray(array)) {
        throw new Error('第一个参数必须是数组');
      }
      if (typeof index !== 'number' || index < 0) {
        throw new Error('索引必须是非负数');
      }
      array[index] = value;
      return array;
    });

    this.registerFunction('arrayPush', (array: any[], value: any) => {
      if (!Array.isArray(array)) {
        throw new Error('第一个参数必须是数组');
      }
      array.push(value);
      return array;
    });

    this.registerFunction('arrayPop', (array: any[]) => {
      if (!Array.isArray(array)) {
        throw new Error('参数必须是数组');
      }
      return array.pop();
    });
    
    // 对象函数
    this.registerFunction('createObject', (properties?: Record<string, any>) => {
      return properties || {};
    });

    this.registerFunction('objectGet', (object: Record<string, any>, key: string) => {
      if (typeof object !== 'object' || object === null) {
        throw new Error('第一个参数必须是对象');
      }
      if (typeof key !== 'string') {
        throw new Error('键必须是字符串');
      }
      return object[key];
    });

    this.registerFunction('objectSet', (object: Record<string, any>, key: string, value: any) => {
      if (typeof object !== 'object' || object === null) {
        throw new Error('第一个参数必须是对象');
      }
      if (typeof key !== 'string') {
        throw new Error('键必须是字符串');
      }
      object[key] = value;
      return object;
    });

    this.registerFunction('objectKeys', (object: Record<string, any>) => {
      if (typeof object !== 'object' || object === null) {
        throw new Error('参数必须是对象');
      }
      return Object.keys(object);
    });

    this.registerFunction('objectValues', (object: Record<string, any>) => {
      if (typeof object !== 'object' || object === null) {
        throw new Error('参数必须是对象');
      }
      return Object.values(object);
    });
  }
  
  /**
   * 设置变量
   * @param name 变量名称
   * @param value 变量值
   */
  public setVariable(name: string, value: any): void {
    this.variables.set(name, value);
  }
  
  /**
   * 获取变量
   * @param name 变量名称
   * @returns 变量值
   */
  public getVariable(name: string): any {
    return this.variables.get(name);
  }

  /**
   * 检查变量是否存在
   * @param name 变量名称
   * @returns 是否存在
   */
  public hasVariable(name: string): boolean {
    return this.variables.has(name);
  }

  /**
   * 删除变量
   * @param name 变量名称
   * @returns 是否删除成功
   */
  public deleteVariable(name: string): boolean {
    return this.variables.delete(name);
  }
  
  /**
   * 注册函数
   * @param name 函数名称
   * @param func 函数实现
   */
  public registerFunction(name: string, func: Function): void {
    this.functions.set(name, func);
  }
  
  /**
   * 获取函数
   * @param name 函数名称
   * @returns 函数实现
   */
  public getFunction(name: string): Function | undefined {
    return this.functions.get(name);
  }
  
  /**
   * 调用函数
   * @param name 函数名称
   * @param args 函数参数
   * @returns 函数返回值
   */
  public callFunction(name: string, ...args: any[]): any {
    const func = this.getFunction(name);
    
    if (!func) {
      throw new Error(`未找到函数: ${name}`);
    }
    
    return func(...args);
  }
  
  /**
   * 清空上下文
   */
  public clear(): void {
    // 清空变量
    this.variables.clear();

    // 清空自定义函数（保留内置函数）
    // 重新初始化函数映射并注册内置函数
    this.functions.clear();
    this.registerBuiltinFunctions();
  }
}
