/**
 * 动画系统扩展节点 - 第二部分
 * 包含剩余的音频节点和场景管理节点
 */

import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Node } from '../nodes/Node';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// 剩余音频系统节点（192-200）
// ============================================================================

/**
 * 创建回声效果节点 (192)
 * 创建音频回声处理器
 */
export class CreateEchoNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'audioContext',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '音频上下文',
      defaultValue: null
    
    });
    this.addInput({
      name: 'delay',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '延迟时间',
      defaultValue: 0.3
    
    });
    this.addInput({
      name: 'feedback',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '反馈量',
      defaultValue: 0.3
    
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'echoNode',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '回声节点'
    });
  }

  public execute(): any {
    const audioContext = this.getInputValue('audioContext');
    const delay = this.getInputValue('delay');
    const feedback = this.getInputValue('feedback');

    if (!audioContext) {
      console.warn('CreateEchoNode: 音频上下文为空');
      this.setOutputValue('echoNode', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 创建回声效果
      const delayNode = audioContext.createDelay(1.0);
      const feedbackNode = audioContext.createGain();
      const outputNode = audioContext.createGain();

      delayNode.delayTime.value = delay;
      feedbackNode.gain.value = feedback;

      // 连接节点
      delayNode.connect(feedbackNode);
      feedbackNode.connect(delayNode);
      delayNode.connect(outputNode);

      const echoEffect = {
        input: delayNode,
        output: outputNode,
        delay: delayNode,
        feedback: feedbackNode
      };

      this.setOutputValue('echoNode', echoEffect);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateEchoNode: 创建回声效果失败', error);
      this.setOutputValue('echoNode', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 创建滤波器节点 (193)
 * 创建音频滤波处理器
 */
export class CreateFilterNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'audioContext',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '音频上下文',
      defaultValue: null
    
    });
    this.addInput({
      name: 'type',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '滤波器类型',
      defaultValue: 'lowpass'
    
    });
    this.addInput({
      name: 'frequency',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '截止频率',
      defaultValue: 1000
    
    });
    this.addInput({
      name: 'Q',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: 'Q值',
      defaultValue: 1
    
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'filterNode',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '滤波器节点'
    });
  }

  public execute(): any {
    const audioContext = this.getInputValue('audioContext');
    const type = this.getInputValue('type');
    const frequency = this.getInputValue('frequency');
    const Q = this.getInputValue('Q');

    if (!audioContext) {
      console.warn('CreateFilterNode: 音频上下文为空');
      this.setOutputValue('filterNode', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 创建滤波器
      const filter = audioContext.createBiquadFilter();
      filter.type = type;
      filter.frequency.value = frequency;
      filter.Q.value = Q;

      this.setOutputValue('filterNode', filter);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateFilterNode: 创建滤波器失败', error);
      this.setOutputValue('filterNode', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 创建音频分析器节点 (194)
 * 创建音频频谱分析器
 */
export class CreateAnalyzerNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'audioContext',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '音频上下文',
      defaultValue: null
    
    });
    this.addInput({
      name: 'fftSize',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: 'FFT大小',
      defaultValue: 2048
    
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'analyzerNode',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '分析器节点'
    });
  }

  public execute(): any {
    const audioContext = this.getInputValue('audioContext');
    const fftSize = this.getInputValue('fftSize');

    if (!audioContext) {
      console.warn('CreateAnalyzerNode: 音频上下文为空');
      this.setOutputValue('analyzerNode', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 创建分析器
      const analyzer = audioContext.createAnalyser();
      analyzer.fftSize = fftSize;
      analyzer.smoothingTimeConstant = 0.8;

      this.setOutputValue('analyzerNode', analyzer);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateAnalyzerNode: 创建分析器失败', error);
      this.setOutputValue('analyzerNode', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 获取频率数据节点 (195)
 * 获取音频频谱数据
 */
export class GetFrequencyDataNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'analyzerNode',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '分析器节点',
      defaultValue: null
    
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'frequencyData',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '频率数据'
    });
  }

  public execute(): any {
    const analyzerNode = this.getInputValue('analyzerNode');

    if (!analyzerNode) {
      console.warn('GetFrequencyDataNode: 分析器节点为空');
      this.setOutputValue('frequencyData', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 获取频率数据
      const bufferLength = analyzerNode.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      analyzerNode.getByteFrequencyData(dataArray);

      this.setOutputValue('frequencyData', Array.from(dataArray));
      this.triggerFlow('completed');
    } catch (error) {
      console.error('GetFrequencyDataNode: 获取频率数据失败', error);
      this.setOutputValue('frequencyData', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 获取波形数据节点 (196)
 * 获取音频波形数据
 */
export class GetWaveformDataNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'analyzerNode',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '分析器节点',
      defaultValue: null
    
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'waveformData',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '波形数据'
    });
  }

  public execute(): any {
    const analyzerNode = this.getInputValue('analyzerNode');

    if (!analyzerNode) {
      console.warn('GetWaveformDataNode: 分析器节点为空');
      this.setOutputValue('waveformData', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 获取波形数据
      const bufferLength = analyzerNode.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      analyzerNode.getByteTimeDomainData(dataArray);

      this.setOutputValue('waveformData', Array.from(dataArray));
      this.triggerFlow('completed');
    } catch (error) {
      console.error('GetWaveformDataNode: 获取波形数据失败', error);
      this.setOutputValue('waveformData', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 创建音频流节点 (197)
 * 创建实时音频流
 */
export class CreateAudioStreamNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '流URL',
      defaultValue: ''
    
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'audioStream',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '音频流'
    });
  }

  public execute(): any {
    const url = this.getInputValue('url');

    if (!url) {
      console.warn('CreateAudioStreamNode: 流URL为空');
      this.setOutputValue('audioStream', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 创建音频流
      const audio = new Audio();
      audio.src = url;
      audio.crossOrigin = 'anonymous';

      this.setOutputValue('audioStream', audio);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateAudioStreamNode: 创建音频流失败', error);
      this.setOutputValue('audioStream', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 连接音频流节点 (198)
 * 连接到音频流源
 */
export class ConnectStreamNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'audioStream',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '音频流',
      defaultValue: null
    
    });
    this.addInput({
      name: 'audioContext',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '音频上下文',
      defaultValue: null
    
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'sourceNode',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '源节点'
    });
  }

  public execute(): any {
    const audioStream = this.getInputValue('audioStream');
    const audioContext = this.getInputValue('audioContext');

    if (!audioStream || !audioContext) {
      console.warn('ConnectStreamNode: 音频流或音频上下文为空');
      this.setOutputValue('sourceNode', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 连接音频流
      const sourceNode = audioContext.createMediaElementSource(audioStream);
      this.setOutputValue('sourceNode', sourceNode);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('ConnectStreamNode: 连接音频流失败', error);
      this.setOutputValue('sourceNode', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 开始录音节点 (199)
 * 开始音频录制
 */
export class StartRecordingNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'recorder',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '录音器'
    });
  }

  public execute(): any {
    try {
      // 开始录音
      navigator.mediaDevices.getUserMedia({ audio: true })
        .then(stream => {
          const mediaRecorder = new MediaRecorder(stream);
          const chunks: Blob[] = [];

          mediaRecorder.ondataavailable = (event) => {
            chunks.push(event.data);
          };

          mediaRecorder.start();

          const recorder = {
            mediaRecorder,
            chunks,
            stream
          };

          this.setOutputValue('recorder', recorder);
          this.triggerFlow('completed');
        })
        .catch(error => {
          console.error('StartRecordingNode: 开始录音失败', error);
          this.setOutputValue('recorder', null);
          this.triggerFlow('completed');
        });
    } catch (error) {
      console.error('StartRecordingNode: 开始录音失败', error);
      this.setOutputValue('recorder', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 停止录音节点 (200)
 * 停止音频录制
 */
export class StopRecordingNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'recorder',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '录音器',
      defaultValue: null
    
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'audioBlob',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '音频数据'
    });
  }

  public execute(): any {
    const recorder = this.getInputValue('recorder');

    if (!recorder) {
      console.warn('StopRecordingNode: 录音器为空');
      this.setOutputValue('audioBlob', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 停止录音
      recorder.mediaRecorder.onstop = () => {
        const audioBlob = new Blob(recorder.chunks, { type: 'audio/wav' });
        recorder.stream.getTracks().forEach((track: any) => track.stop());

        this.setOutputValue('audioBlob', audioBlob);
        this.triggerFlow('completed');
      };

      recorder.mediaRecorder.stop();
    } catch (error) {
      console.error('StopRecordingNode: 停止录音失败', error);
      this.setOutputValue('audioBlob', null);
      this.triggerFlow('completed');
    }
  }
}
