/**
 * 动画系统扩展节点
 * 第7批次：动画系统扩展（节点181-210）
 * 包含动画曲线、状态机、场景管理、高级音频等功能
 */

import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Node } from '../nodes/Node';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { AnimationClip } from '../../animation/AnimationClip';
import { AnimationStateMachine } from '../../animation/AnimationStateMachine';
import { Animator } from '../../animation/Animator';

// ============================================================================
// 动画曲线节点（181-185）
// ============================================================================

/**
 * 计算曲线值节点 (181)
 * 计算动画曲线在指定时间的值
 */
export class EvaluateCurveNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'curve',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '动画曲线',
      defaultValue: null
    });
    this.addInput({
      name: 'time',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '时间',
      defaultValue: 0
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '值'
    });
  }

  public execute(): any {
    const curve = this.getInputValue('curve');
    const time = this.getInputValue('time');

    if (!curve) {
      console.warn('EvaluateCurveNode: 动画曲线为空');
      this.setOutputValue('value', 0);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 计算曲线值
      let value = 0;
      if (curve.evaluate) {
        value = curve.evaluate(time);
      } else if (curve instanceof THREE.AnimationClip) {
        // 如果是Three.js动画片段，获取第一个轨道的值
        if (curve.tracks && curve.tracks.length > 0) {
          const track = curve.tracks[0];
          const times = track.times;
          const values = track.values;
          
          // 简单线性插值
          for (let i = 0; i < times.length - 1; i++) {
            if (time >= times[i] && time <= times[i + 1]) {
              const t = (time - times[i]) / (times[i + 1] - times[i]);
              value = values[i] + (values[i + 1] - values[i]) * t;
              break;
            }
          }
        }
      }

      this.setOutputValue('value', value);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('EvaluateCurveNode: 计算曲线值失败', error);
      this.setOutputValue('value', 0);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 创建状态机节点 (182)
 * 创建动画状态机
 */
export class CreateStateMachineNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '实体',
      defaultValue: null
    
    });
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '状态机名称',
      defaultValue: '状态机'
    
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'stateMachine',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '状态机'
    });
  }

  public execute(): any {
    const entity = this.getInputValue('entity') as Entity;
    const name = this.getInputValue('name');

    if (!entity) {
      console.warn('CreateStateMachineNode: 实体为空');
      this.setOutputValue('stateMachine', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 获取或创建Animator组件
      let animator = entity.getComponent('Animator') as Animator;
      if (!animator) {
        // 创建新的Animator组件
        animator = new Animator({ entity: entity });
        entity.addComponent(animator);
      }

      // 创建状态机
      const stateMachine = new AnimationStateMachine(animator);

      this.setOutputValue('stateMachine', stateMachine);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateStateMachineNode: 创建状态机失败', error);
      this.setOutputValue('stateMachine', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 添加状态节点 (183)
 * 向状态机添加动画状态
 */
export class AddStateNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'stateMachine',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '状态机',
      defaultValue: null
    
    });
    this.addInput({
      name: 'stateName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '状态名称',
      defaultValue: '新状态'
    
    });
    this.addInput({
      name: 'animationClip',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '动画片段',
      defaultValue: null
    
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'state',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '状态'
    });
  }

  public execute(): any {
    const stateMachine = this.getInputValue('stateMachine');
    const stateName = this.getInputValue('stateName');
    const animationClip = this.getInputValue('animationClip');

    if (!stateMachine) {
      console.warn('AddStateNode: 状态机为空');
      this.setOutputValue('state', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 添加状态
      const state = stateMachine.addState(stateName, {
        clip: animationClip,
        loop: true,
        speed: 1.0
      });

      this.setOutputValue('state', state);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('AddStateNode: 添加状态失败', error);
      this.setOutputValue('state', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 添加过渡节点 (184)
 * 在状态间添加过渡条件
 */
export class AddTransitionNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'stateMachine',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '状态机',
      defaultValue: null
    
    });
    this.addInput({
      name: 'fromState',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '源状态',
      defaultValue: ''
    
    });
    this.addInput({
      name: 'toState',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '目标状态',
      defaultValue: ''
    
    });
    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '条件',
      defaultValue: ''
    
    });
    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '过渡时间',
      defaultValue: 0.3
    
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'transition',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '过渡'
    });
  }

  public execute(): any {
    const stateMachine = this.getInputValue('stateMachine');
    const fromState = this.getInputValue('fromState');
    const toState = this.getInputValue('toState');
    const condition = this.getInputValue('condition');
    const duration = this.getInputValue('duration');

    if (!stateMachine) {
      console.warn('AddTransitionNode: 状态机为空');
      this.setOutputValue('transition', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 添加过渡
      const transition = stateMachine.addTransition(fromState, toState, {
        condition: condition,
        duration: duration,
        exitTime: 0.9
      });

      this.setOutputValue('transition', transition);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('AddTransitionNode: 添加过渡失败', error);
      this.setOutputValue('transition', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置当前状态节点 (185)
 * 切换到指定动画状态
 */
export class SetCurrentStateNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'stateMachine',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '状态机',
      defaultValue: null
    
    });
    this.addInput({
      name: 'stateName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '状态名称',
      defaultValue: ''
    
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '成功'
    });
  }

  public execute(): any {
    const stateMachine = this.getInputValue('stateMachine');
    const stateName = this.getInputValue('stateName');

    if (!stateMachine) {
      console.warn('SetCurrentStateNode: 状态机为空');
      this.setOutputValue('success', false);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 设置当前状态
      const success = stateMachine.setState(stateName);
      this.setOutputValue('success', success);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetCurrentStateNode: 设置当前状态失败', error);
      this.setOutputValue('success', false);
      this.triggerFlow('completed');
    }
  }
}

// ============================================================================
// 高级音频系统节点（186-200）
// ============================================================================

/**
 * 创建3D音频源节点 (186)
 * 创建空间音频源
 */
export class Create3DAudioSourceNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '实体',
      defaultValue: null
    
    });
    this.addInput({
      name: 'audioBuffer',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '音频缓冲区',
      defaultValue: null
    
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'audioSource',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '音频源'
    });
  }

  public execute(): any {
    const entity = this.getInputValue('entity') as Entity;
    const audioBuffer = this.getInputValue('audioBuffer');

    if (!entity) {
      console.warn('Create3DAudioSourceNode: 实体为空');
      this.setOutputValue('audioSource', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 创建3D音频源
      const listener = new THREE.AudioListener();
      const sound = new THREE.PositionalAudio(listener);

      if (audioBuffer) {
        sound.setBuffer(audioBuffer);
      }

      // 设置3D音频属性
      sound.setRefDistance(20);
      sound.setRolloffFactor(1);
      sound.setDistanceModel('inverse');

      this.setOutputValue('audioSource', sound);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('Create3DAudioSourceNode: 创建3D音频源失败', error);
      this.setOutputValue('audioSource', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置音频位置节点 (187)
 * 设置3D音频源位置
 */
export class SetAudioPositionNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'audioSource',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '音频源',
      defaultValue: null
    
    });
    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '位置',
      defaultValue: { x: 0, y: 0, z: 0 }
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
  }

  public execute(): any {
    const audioSource = this.getInputValue('audioSource');
    const position = this.getInputValue('position');

    if (!audioSource) {
      console.warn('SetAudioPositionNode: 音频源为空');
      this.triggerFlow('completed');
      return;
    }

    try {
      // 设置音频位置
      if (audioSource.position) {
        audioSource.position.set(position.x, position.y, position.z);
      }

      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetAudioPositionNode: 设置音频位置失败', error);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置音频速度节点 (188)
 * 设置音频源移动速度
 */
export class SetAudioVelocityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'audioSource',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '音频源',
      defaultValue: null
    
    });
    this.addInput({
      name: 'velocity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '速度',
      defaultValue: { x: 0, y: 0, z: 0 }
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
  }

  public execute(): any {
    const audioSource = this.getInputValue('audioSource');
    const velocity = this.getInputValue('velocity');

    if (!audioSource) {
      console.warn('SetAudioVelocityNode: 音频源为空');
      this.triggerFlow('completed');
      return;
    }

    try {
      // 设置音频速度（用于多普勒效应）
      if (audioSource.setVelocity) {
        audioSource.setVelocity(velocity.x, velocity.y, velocity.z);
      }

      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetAudioVelocityNode: 设置音频速度失败', error);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置听者位置节点 (189)
 * 设置音频听者位置
 */
export class SetListenerPositionNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'listener',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '听者',
      defaultValue: null
    });
    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '位置',
      defaultValue: { x: 0, y: 0, z: 0 }
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
  }

  public execute(): any {
    const listener = this.getInputValue('listener');
    const position = this.getInputValue('position');

    if (!listener) {
      console.warn('SetListenerPositionNode: 听者为空');
      this.triggerFlow('completed');
      return;
    }

    try {
      // 设置听者位置
      if (listener.position) {
        listener.position.set(position.x, position.y, position.z);
      }

      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetListenerPositionNode: 设置听者位置失败', error);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置听者朝向节点 (190)
 * 设置音频听者朝向
 */
export class SetListenerOrientationNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'listener',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '听者',
      defaultValue: null
    });
    this.addInput({
      name: 'forward',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '前方向',
      defaultValue: { x: 0, y: 0, z: -1 }
    });
    this.addInput({
      name: 'up',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '上方向',
      defaultValue: { x: 0, y: 1, z: 0 }
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
  }

  public execute(): any {
    const listener = this.getInputValue('listener');
    const forward = this.getInputValue('forward');
    const up = this.getInputValue('up');

    if (!listener) {
      console.warn('SetListenerOrientationNode: 听者为空');
      this.triggerFlow('completed');
      return;
    }

    try {
      // 设置听者朝向
      if (listener.setOrientation) {
        listener.setOrientation(forward.x, forward.y, forward.z, up.x, up.y, up.z);
      }

      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetListenerOrientationNode: 设置听者朝向失败', error);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 创建混响效果节点 (191)
 * 创建音频混响处理器
 */
export class CreateReverbNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });
    this.addInput({
      name: 'audioContext',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '音频上下文',
      defaultValue: null
    
    });
    this.addInput({
      name: 'roomSize',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '房间大小',
      defaultValue: 0.5
    
    });
    this.addInput({
      name: 'decay',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '衰减时间',
      defaultValue: 2.0
    
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });
    this.addOutput({
      name: 'reverbNode',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '混响节点'
    });
  }

  public execute(): any {
    const audioContext = this.getInputValue('audioContext');
    const roomSize = this.getInputValue('roomSize');
    const decay = this.getInputValue('decay');

    if (!audioContext) {
      console.warn('CreateReverbNode: 音频上下文为空');
      this.setOutputValue('reverbNode', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 创建混响效果
      const convolver = audioContext.createConvolver();

      // 生成冲激响应
      const sampleRate = audioContext.sampleRate;
      const length = sampleRate * decay;
      const impulse = audioContext.createBuffer(2, length, sampleRate);

      for (let channel = 0; channel < 2; channel++) {
        const channelData = impulse.getChannelData(channel);
        for (let i = 0; i < length; i++) {
          const n = length - i;
          channelData[i] = (Math.random() * 2 - 1) * Math.pow(n / length, roomSize);
        }
      }

      convolver.buffer = impulse;

      this.setOutputValue('reverbNode', convolver);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateReverbNode: 创建混响效果失败', error);
      this.setOutputValue('reverbNode', null);
      this.triggerFlow('completed');
    }
  }
}


