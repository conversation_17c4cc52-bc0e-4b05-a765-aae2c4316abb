/**
 * 音频与粒子系统节点 - 第三部分
 * 第8批次：音频与粒子系统（节点231-240）
 * 包含地形系统和水体系统节点
 */

import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Node } from '../nodes/Node';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// 地形系统节点（231-237）
// ============================================================================

/**
 * 创建地形节点 (231)
 * 创建地形网格
 */
export class CreateTerrainNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'width',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形宽度',
      defaultValue: 100
    });

    this.addInput({
      name: 'height',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形高度',
      defaultValue: 100
    });

    this.addInput({
      name: 'segments',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '分段数',
      defaultValue: 64
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '地形对象'
    });
  }

  public execute(): any {
    const width = this.getInputValue('width');
    const height = this.getInputValue('height');
    const segments = this.getInputValue('segments');

    try {
      const geometry = new THREE.PlaneGeometry(width, height, segments, segments);
      const material = new THREE.MeshLambertMaterial({ 
        color: 0x8B7355,
        wireframe: false
      });

      const terrain = new THREE.Mesh(geometry, material);
      terrain.rotation.x = -Math.PI / 2;
      terrain.receiveShadow = true;

      (terrain as any).terrainWidth = width;
      (terrain as any).terrainHeight = height;
      (terrain as any).terrainSegments = segments;

      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateTerrainNode: 创建地形失败', error);
      this.setOutputValue('terrain', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 生成高度图节点 (232)
 * 生成地形高度图
 */
export class GenerateHeightmapNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形对象'
    });

    this.addInput({
      name: 'heightScale',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '高度缩放',
      defaultValue: 10
    });

    this.addInput({
      name: 'seed',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '随机种子',
      defaultValue: 12345
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的地形'
    });
  }

  public execute(): any {
    const terrain = this.getInputValue('terrain');
    const heightScale = this.getInputValue('heightScale');
    const seed = this.getInputValue('seed');

    try {
      if (terrain && terrain.geometry) {
        const positions = terrain.geometry.attributes.position.array;
        
        // 简单的噪声生成
        for (let i = 0; i < positions.length; i += 3) {
          const x = positions[i];
          const z = positions[i + 2];
          
          // 使用简单的正弦波生成高度
          const height = Math.sin(x * 0.1 + seed) * Math.cos(z * 0.1 + seed) * heightScale;
          positions[i + 1] = height;
        }
        
        terrain.geometry.attributes.position.needsUpdate = true;
        terrain.geometry.computeVertexNormals();
      }
      
      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('GenerateHeightmapNode: 生成高度图失败', error);
      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 应用噪声节点 (233)
 * 为地形应用噪声纹理
 */
export class ApplyNoiseNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形对象'
    });

    this.addInput({
      name: 'noiseScale',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '噪声缩放',
      defaultValue: 0.1
    });

    this.addInput({
      name: 'noiseStrength',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '噪声强度',
      defaultValue: 5
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的地形'
    });
  }

  public execute(): any {
    const terrain = this.getInputValue('terrain');
    const noiseScale = this.getInputValue('noiseScale');
    const noiseStrength = this.getInputValue('noiseStrength');

    try {
      if (terrain && terrain.geometry) {
        const positions = terrain.geometry.attributes.position.array;
        
        // 应用噪声到现有高度
        for (let i = 0; i < positions.length; i += 3) {
          const x = positions[i];
          const z = positions[i + 2];
          
          // 简单的噪声函数
          const noise = (Math.random() - 0.5) * 2 * noiseStrength;
          positions[i + 1] += noise * Math.sin(x * noiseScale) * Math.cos(z * noiseScale);
        }
        
        terrain.geometry.attributes.position.needsUpdate = true;
        terrain.geometry.computeVertexNormals();
      }
      
      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('ApplyNoiseNode: 应用噪声失败', error);
      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置地形纹理节点 (234)
 * 设置地形表面纹理
 */
export class SetTerrainTextureNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形对象'
    });

    this.addInput({
      name: 'texture',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形纹理'
    });

    this.addInput({
      name: 'repeatU',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: 'U方向重复',
      defaultValue: 10
    });

    this.addInput({
      name: 'repeatV',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: 'V方向重复',
      defaultValue: 10
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的地形'
    });
  }

  public execute(): any {
    const terrain = this.getInputValue('terrain');
    const texture = this.getInputValue('texture');
    const repeatU = this.getInputValue('repeatU');
    const repeatV = this.getInputValue('repeatV');

    try {
      if (terrain && terrain.material && texture) {
        terrain.material.map = texture;
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(repeatU, repeatV);
        terrain.material.needsUpdate = true;
      }

      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetTerrainTextureNode: 设置地形纹理失败', error);
      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 混合纹理节点 (235)
 * 混合多个地形纹理
 */
export class BlendTexturesNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形对象'
    });

    this.addInput({
      name: 'texture1',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '纹理1'
    });

    this.addInput({
      name: 'texture2',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '纹理2'
    });

    this.addInput({
      name: 'blendFactor',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '混合因子',
      defaultValue: 0.5
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的地形'
    });
  }

  public execute(): any {
    const terrain = this.getInputValue('terrain');
    const texture1 = this.getInputValue('texture1');
    const texture2 = this.getInputValue('texture2');
    const blendFactor = this.getInputValue('blendFactor');

    try {
      if (terrain && terrain.material) {
        // 创建混合材质
        const blendMaterial = new THREE.MeshLambertMaterial({
          map: texture1
        });

        // 存储混合信息
        (blendMaterial as any).blendTexture = texture2;
        (blendMaterial as any).blendFactor = blendFactor;

        terrain.material = blendMaterial;
      }

      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('BlendTexturesNode: 混合纹理失败', error);
      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 启用地形LOD节点 (236)
 * 启用地形细节层次
 */
export class EnableTerrainLODNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形对象'
    });

    this.addInput({
      name: 'lodLevels',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: 'LOD级别数',
      defaultValue: 3
    });

    this.addInput({
      name: 'lodDistance',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: 'LOD距离',
      defaultValue: 50
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的地形'
    });
  }

  public execute(): any {
    const terrain = this.getInputValue('terrain');
    const lodLevels = this.getInputValue('lodLevels');
    const lodDistance = this.getInputValue('lodDistance');

    try {
      if (terrain) {
        (terrain as any).lodEnabled = true;
        (terrain as any).lodLevels = lodLevels;
        (terrain as any).lodDistance = lodDistance;
      }

      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('EnableTerrainLODNode: 启用地形LOD失败', error);
      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 启用地形碰撞节点 (237)
 * 启用地形物理碰撞
 */
export class EnableTerrainCollisionNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形对象'
    });

    this.addInput({
      name: 'friction',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '摩擦系数',
      defaultValue: 0.8
    });

    this.addInput({
      name: 'restitution',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '弹性系数',
      defaultValue: 0.1
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的地形'
    });
  }

  public execute(): any {
    const terrain = this.getInputValue('terrain');
    const friction = this.getInputValue('friction');
    const restitution = this.getInputValue('restitution');

    try {
      if (terrain) {
        (terrain as any).collisionEnabled = true;
        (terrain as any).friction = friction;
        (terrain as any).restitution = restitution;
      }

      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('EnableTerrainCollisionNode: 启用地形碰撞失败', error);
      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    }
  }
}

// ============================================================================
// 水体系统节点（238-240）
// ============================================================================

/**
 * 创建水面节点 (238)
 * 创建水体表面
 */
export class CreateWaterSurfaceNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '水面大小',
      defaultValue: 100
    });

    this.addInput({
      name: 'segments',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '分段数',
      defaultValue: 32
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'waterSurface',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '水面对象'
    });
  }

  public execute(): any {
    const size = this.getInputValue('size');
    const segments = this.getInputValue('segments');

    try {
      const geometry = new THREE.PlaneGeometry(size, size, segments, segments);
      const material = new THREE.MeshLambertMaterial({
        color: 0x006994,
        transparent: true,
        opacity: 0.7
      });

      const waterSurface = new THREE.Mesh(geometry, material);
      waterSurface.rotation.x = -Math.PI / 2;
      (waterSurface as any).isWater = true;
      (waterSurface as any).waterSize = size;
      (waterSurface as any).waterSegments = segments;

      this.setOutputValue('waterSurface', waterSurface);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateWaterSurfaceNode: 创建水面失败', error);
      this.setOutputValue('waterSurface', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 添加波浪节点 (239)
 * 为水面添加波浪效果
 */
export class AddWavesNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'waterSurface',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '水面对象'
    });

    this.addInput({
      name: 'waveHeight',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '波浪高度',
      defaultValue: 2.0
    });

    this.addInput({
      name: 'waveSpeed',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '波浪速度',
      defaultValue: 1.0
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'waterSurface',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的水面'
    });
  }

  public execute(): any {
    const waterSurface = this.getInputValue('waterSurface');
    const waveHeight = this.getInputValue('waveHeight');
    const waveSpeed = this.getInputValue('waveSpeed');

    try {
      if (waterSurface) {
        (waterSurface as any).wavesEnabled = true;
        (waterSurface as any).waveHeight = waveHeight;
        (waterSurface as any).waveSpeed = waveSpeed;
        (waterSurface as any).waveTime = 0;
      }

      this.setOutputValue('waterSurface', waterSurface);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('AddWavesNode: 添加波浪失败', error);
      this.setOutputValue('waterSurface', waterSurface);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 启用水面反射节点 (240)
 * 启用水面反射效果
 */
export class EnableReflectionNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'waterSurface',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '水面对象'
    });

    this.addInput({
      name: 'reflectionStrength',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '反射强度',
      defaultValue: 0.8
    });

    this.addInput({
      name: 'reflectionResolution',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '反射分辨率',
      defaultValue: 512
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'waterSurface',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的水面'
    });
  }

  public execute(): any {
    const waterSurface = this.getInputValue('waterSurface');
    const reflectionStrength = this.getInputValue('reflectionStrength');
    const reflectionResolution = this.getInputValue('reflectionResolution');

    try {
      if (waterSurface) {
        (waterSurface as any).reflectionEnabled = true;
        (waterSurface as any).reflectionStrength = reflectionStrength;
        (waterSurface as any).reflectionResolution = reflectionResolution;
      }

      this.setOutputValue('waterSurface', waterSurface);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('EnableReflectionNode: 启用水面反射失败', error);
      this.setOutputValue('waterSurface', waterSurface);
      this.triggerFlow('completed');
    }
  }
}
